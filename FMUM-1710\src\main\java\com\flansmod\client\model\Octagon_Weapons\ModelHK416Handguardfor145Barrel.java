//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: HK416 Handguard for 14.5 Barrel
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelHK416Handguardfor145Barrel extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelHK416Handguardfor145Barrel() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[253];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 9
		attachmentModel[1] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 41
		attachmentModel[2] = new ModelRendererTurbo(this, 142, 1, textureX, textureY); // Box 49
		attachmentModel[3] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 52
		attachmentModel[4] = new ModelRendererTurbo(this, 161, 1, textureX, textureY); // Box 158
		attachmentModel[5] = new ModelRendererTurbo(this, 185, 1, textureX, textureY); // Box 159
		attachmentModel[6] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 160
		attachmentModel[7] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 161
		attachmentModel[8] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 162
		attachmentModel[9] = new ModelRendererTurbo(this, 209, 9, textureX, textureY); // Box 163
		attachmentModel[10] = new ModelRendererTurbo(this, 233, 9, textureX, textureY); // Box 164
		attachmentModel[11] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 165
		attachmentModel[12] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 166
		attachmentModel[13] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 167
		attachmentModel[14] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 168
		attachmentModel[15] = new ModelRendererTurbo(this, 73, 25, textureX, textureY); // Box 169
		attachmentModel[16] = new ModelRendererTurbo(this, 97, 25, textureX, textureY); // Box 170
		attachmentModel[17] = new ModelRendererTurbo(this, 121, 25, textureX, textureY); // Box 171
		attachmentModel[18] = new ModelRendererTurbo(this, 145, 25, textureX, textureY); // Box 172
		attachmentModel[19] = new ModelRendererTurbo(this, 169, 25, textureX, textureY); // Box 173
		attachmentModel[20] = new ModelRendererTurbo(this, 193, 25, textureX, textureY); // Box 174
		attachmentModel[21] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 175
		attachmentModel[22] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 176
		attachmentModel[23] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 177
		attachmentModel[24] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 178
		attachmentModel[25] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 179
		attachmentModel[26] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 180
		attachmentModel[27] = new ModelRendererTurbo(this, 121, 33, textureX, textureY); // Box 181
		attachmentModel[28] = new ModelRendererTurbo(this, 145, 33, textureX, textureY); // Box 182
		attachmentModel[29] = new ModelRendererTurbo(this, 169, 33, textureX, textureY); // Box 183
		attachmentModel[30] = new ModelRendererTurbo(this, 193, 33, textureX, textureY); // Box 184
		attachmentModel[31] = new ModelRendererTurbo(this, 217, 33, textureX, textureY); // Box 185
		attachmentModel[32] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 186
		attachmentModel[33] = new ModelRendererTurbo(this, 25, 41, textureX, textureY); // Box 187
		attachmentModel[34] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 188
		attachmentModel[35] = new ModelRendererTurbo(this, 241, 25, textureX, textureY); // Box 200
		attachmentModel[36] = new ModelRendererTurbo(this, 153, 9, textureX, textureY); // Box 202
		attachmentModel[37] = new ModelRendererTurbo(this, 249, 17, textureX, textureY); // Box 203
		attachmentModel[38] = new ModelRendererTurbo(this, 73, 41, textureX, textureY); // Box 204
		attachmentModel[39] = new ModelRendererTurbo(this, 81, 41, textureX, textureY); // Box 205
		attachmentModel[40] = new ModelRendererTurbo(this, 89, 41, textureX, textureY); // Box 206
		attachmentModel[41] = new ModelRendererTurbo(this, 97, 41, textureX, textureY); // Box 207
		attachmentModel[42] = new ModelRendererTurbo(this, 105, 41, textureX, textureY); // Box 208
		attachmentModel[43] = new ModelRendererTurbo(this, 113, 41, textureX, textureY); // Box 209
		attachmentModel[44] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 210
		attachmentModel[45] = new ModelRendererTurbo(this, 129, 41, textureX, textureY); // Box 211
		attachmentModel[46] = new ModelRendererTurbo(this, 137, 41, textureX, textureY); // Box 212
		attachmentModel[47] = new ModelRendererTurbo(this, 145, 41, textureX, textureY); // Box 213
		attachmentModel[48] = new ModelRendererTurbo(this, 153, 41, textureX, textureY); // Box 214
		attachmentModel[49] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 215
		attachmentModel[50] = new ModelRendererTurbo(this, 161, 41, textureX, textureY); // Box 216
		attachmentModel[51] = new ModelRendererTurbo(this, 169, 41, textureX, textureY); // Box 217
		attachmentModel[52] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 219
		attachmentModel[53] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 220
		attachmentModel[54] = new ModelRendererTurbo(this, 201, 41, textureX, textureY); // Box 221
		attachmentModel[55] = new ModelRendererTurbo(this, 217, 41, textureX, textureY); // Box 222
		attachmentModel[56] = new ModelRendererTurbo(this, 225, 41, textureX, textureY); // Box 223
		attachmentModel[57] = new ModelRendererTurbo(this, 233, 41, textureX, textureY); // Box 224
		attachmentModel[58] = new ModelRendererTurbo(this, 241, 41, textureX, textureY); // Box 225
		attachmentModel[59] = new ModelRendererTurbo(this, 249, 41, textureX, textureY); // Box 226
		attachmentModel[60] = new ModelRendererTurbo(this, 145, 49, textureX, textureY); // Box 227
		attachmentModel[61] = new ModelRendererTurbo(this, 153, 49, textureX, textureY); // Box 228
		attachmentModel[62] = new ModelRendererTurbo(this, 161, 49, textureX, textureY); // Box 229
		attachmentModel[63] = new ModelRendererTurbo(this, 177, 49, textureX, textureY); // Box 230
		attachmentModel[64] = new ModelRendererTurbo(this, 193, 49, textureX, textureY); // Box 231
		attachmentModel[65] = new ModelRendererTurbo(this, 217, 49, textureX, textureY); // Box 232
		attachmentModel[66] = new ModelRendererTurbo(this, 225, 49, textureX, textureY); // Box 233
		attachmentModel[67] = new ModelRendererTurbo(this, 233, 49, textureX, textureY); // Box 234
		attachmentModel[68] = new ModelRendererTurbo(this, 241, 49, textureX, textureY); // Box 235
		attachmentModel[69] = new ModelRendererTurbo(this, 201, 1, textureX, textureY); // Box 503
		attachmentModel[70] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 456
		attachmentModel[71] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 457
		attachmentModel[72] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 458
		attachmentModel[73] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 459
		attachmentModel[74] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 460
		attachmentModel[75] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 461
		attachmentModel[76] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 462
		attachmentModel[77] = new ModelRendererTurbo(this, 1, 121, textureX, textureY); // Box 463
		attachmentModel[78] = new ModelRendererTurbo(this, 1, 129, textureX, textureY); // Box 464
		attachmentModel[79] = new ModelRendererTurbo(this, 1, 137, textureX, textureY); // Box 465
		attachmentModel[80] = new ModelRendererTurbo(this, 145, 57, textureX, textureY); // Box 468
		attachmentModel[81] = new ModelRendererTurbo(this, 225, 1, textureX, textureY); // Box 469
		attachmentModel[82] = new ModelRendererTurbo(this, 201, 9, textureX, textureY); // Box 479
		attachmentModel[83] = new ModelRendererTurbo(this, 225, 9, textureX, textureY); // Box 483
		attachmentModel[84] = new ModelRendererTurbo(this, 162, 57, textureX, textureY); // Box 498
		attachmentModel[85] = new ModelRendererTurbo(this, 185, 1, textureX, textureY); // Box 514
		attachmentModel[86] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 515
		attachmentModel[87] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 516
		attachmentModel[88] = new ModelRendererTurbo(this, 1, 145, textureX, textureY); // Box 517
		attachmentModel[89] = new ModelRendererTurbo(this, 145, 9, textureX, textureY); // Box 530
		attachmentModel[90] = new ModelRendererTurbo(this, 169, 57, textureX, textureY); // Box 627
		attachmentModel[91] = new ModelRendererTurbo(this, 249, 9, textureX, textureY); // Box 559
		attachmentModel[92] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 560
		attachmentModel[93] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 585
		attachmentModel[94] = new ModelRendererTurbo(this, 41, 25, textureX, textureY); // Box 586
		attachmentModel[95] = new ModelRendererTurbo(this, 65, 25, textureX, textureY); // Box 587
		attachmentModel[96] = new ModelRendererTurbo(this, 89, 25, textureX, textureY); // Box 588
		attachmentModel[97] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 589
		attachmentModel[98] = new ModelRendererTurbo(this, 137, 25, textureX, textureY); // Box 590
		attachmentModel[99] = new ModelRendererTurbo(this, 185, 57, textureX, textureY); // Box 591
		attachmentModel[100] = new ModelRendererTurbo(this, 201, 65, textureX, textureY); // Box 592
		attachmentModel[101] = new ModelRendererTurbo(this, 145, 73, textureX, textureY); // Box 593
		attachmentModel[102] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 594
		attachmentModel[103] = new ModelRendererTurbo(this, 161, 25, textureX, textureY); // Box 595
		attachmentModel[104] = new ModelRendererTurbo(this, 185, 25, textureX, textureY); // Box 596
		attachmentModel[105] = new ModelRendererTurbo(this, 209, 25, textureX, textureY); // Box 597
		attachmentModel[106] = new ModelRendererTurbo(this, 233, 25, textureX, textureY); // Box 598
		attachmentModel[107] = new ModelRendererTurbo(this, 17, 33, textureX, textureY); // Box 599
		attachmentModel[108] = new ModelRendererTurbo(this, 41, 33, textureX, textureY); // Box 600
		attachmentModel[109] = new ModelRendererTurbo(this, 65, 33, textureX, textureY); // Box 601
		attachmentModel[110] = new ModelRendererTurbo(this, 89, 33, textureX, textureY); // Box 602
		attachmentModel[111] = new ModelRendererTurbo(this, 113, 33, textureX, textureY); // Box 603
		attachmentModel[112] = new ModelRendererTurbo(this, 137, 33, textureX, textureY); // Box 604
		attachmentModel[113] = new ModelRendererTurbo(this, 161, 33, textureX, textureY); // Box 605
		attachmentModel[114] = new ModelRendererTurbo(this, 185, 33, textureX, textureY); // Box 606
		attachmentModel[115] = new ModelRendererTurbo(this, 209, 33, textureX, textureY); // Box 607
		attachmentModel[116] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 608
		attachmentModel[117] = new ModelRendererTurbo(this, 185, 73, textureX, textureY); // Box 609
		attachmentModel[118] = new ModelRendererTurbo(this, 145, 81, textureX, textureY); // Box 610
		attachmentModel[119] = new ModelRendererTurbo(this, 185, 81, textureX, textureY); // Box 611
		attachmentModel[120] = new ModelRendererTurbo(this, 233, 33, textureX, textureY); // Box 615
		attachmentModel[121] = new ModelRendererTurbo(this, 41, 41, textureX, textureY); // Box 616
		attachmentModel[122] = new ModelRendererTurbo(this, 65, 41, textureX, textureY); // Box 617
		attachmentModel[123] = new ModelRendererTurbo(this, 225, 57, textureX, textureY); // Box 618
		attachmentModel[124] = new ModelRendererTurbo(this, 241, 57, textureX, textureY); // Box 619
		attachmentModel[125] = new ModelRendererTurbo(this, 145, 65, textureX, textureY); // Box 620
		attachmentModel[126] = new ModelRendererTurbo(this, 241, 65, textureX, textureY); // Box 621
		attachmentModel[127] = new ModelRendererTurbo(this, 225, 73, textureX, textureY); // Box 622
		attachmentModel[128] = new ModelRendererTurbo(this, 241, 73, textureX, textureY); // Box 623
		attachmentModel[129] = new ModelRendererTurbo(this, 145, 89, textureX, textureY); // Box 624
		attachmentModel[130] = new ModelRendererTurbo(this, 185, 89, textureX, textureY); // Box 625
		attachmentModel[131] = new ModelRendererTurbo(this, 217, 97, textureX, textureY); // Box 626
		attachmentModel[132] = new ModelRendererTurbo(this, 121, 145, textureX, textureY); // Box 627
		attachmentModel[133] = new ModelRendererTurbo(this, 217, 105, textureX, textureY); // Box 628
		attachmentModel[134] = new ModelRendererTurbo(this, 217, 113, textureX, textureY); // Box 629
		attachmentModel[135] = new ModelRendererTurbo(this, 217, 121, textureX, textureY); // Box 630
		attachmentModel[136] = new ModelRendererTurbo(this, 225, 81, textureX, textureY); // Box 631
		attachmentModel[137] = new ModelRendererTurbo(this, 241, 81, textureX, textureY); // Box 632
		attachmentModel[138] = new ModelRendererTurbo(this, 225, 89, textureX, textureY); // Box 633
		attachmentModel[139] = new ModelRendererTurbo(this, 241, 89, textureX, textureY); // Box 634
		attachmentModel[140] = new ModelRendererTurbo(this, 217, 129, textureX, textureY); // Box 635
		attachmentModel[141] = new ModelRendererTurbo(this, 233, 129, textureX, textureY); // Box 636
		attachmentModel[142] = new ModelRendererTurbo(this, 249, 49, textureX, textureY); // Box 637
		attachmentModel[143] = new ModelRendererTurbo(this, 161, 65, textureX, textureY); // Box 638
		attachmentModel[144] = new ModelRendererTurbo(this, 249, 129, textureX, textureY); // Box 639
		attachmentModel[145] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 640
		attachmentModel[146] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 641
		attachmentModel[147] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 642
		attachmentModel[148] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 581
		attachmentModel[149] = new ModelRendererTurbo(this, 73, 153, textureX, textureY); // Box 582
		attachmentModel[150] = new ModelRendererTurbo(this, 217, 137, textureX, textureY); // Box 584
		attachmentModel[151] = new ModelRendererTurbo(this, 233, 137, textureX, textureY); // Box 585
		attachmentModel[152] = new ModelRendererTurbo(this, 241, 137, textureX, textureY); // Box 586
		attachmentModel[153] = new ModelRendererTurbo(this, 249, 137, textureX, textureY); // Box 587
		attachmentModel[154] = new ModelRendererTurbo(this, 241, 145, textureX, textureY); // Box 588
		attachmentModel[155] = new ModelRendererTurbo(this, 145, 153, textureX, textureY); // Box 589
		attachmentModel[156] = new ModelRendererTurbo(this, 161, 153, textureX, textureY); // Box 590
		attachmentModel[157] = new ModelRendererTurbo(this, 249, 145, textureX, textureY); // Box 591
		attachmentModel[158] = new ModelRendererTurbo(this, 177, 153, textureX, textureY); // Box 592
		attachmentModel[159] = new ModelRendererTurbo(this, 185, 153, textureX, textureY); // Box 593
		attachmentModel[160] = new ModelRendererTurbo(this, 201, 153, textureX, textureY); // Box 594
		attachmentModel[161] = new ModelRendererTurbo(this, 209, 153, textureX, textureY); // Box 595
		attachmentModel[162] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 596
		attachmentModel[163] = new ModelRendererTurbo(this, 217, 153, textureX, textureY); // Box 597
		attachmentModel[164] = new ModelRendererTurbo(this, 225, 153, textureX, textureY); // Box 598
		attachmentModel[165] = new ModelRendererTurbo(this, 169, 25, textureX, textureY); // Box 599
		attachmentModel[166] = new ModelRendererTurbo(this, 193, 25, textureX, textureY); // Box 602
		attachmentModel[167] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 606
		attachmentModel[168] = new ModelRendererTurbo(this, 233, 153, textureX, textureY); // Box 607
		attachmentModel[169] = new ModelRendererTurbo(this, 241, 153, textureX, textureY); // Box 608
		attachmentModel[170] = new ModelRendererTurbo(this, 249, 153, textureX, textureY); // Box 609
		attachmentModel[171] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 610
		attachmentModel[172] = new ModelRendererTurbo(this, 9, 161, textureX, textureY); // Box 611
		attachmentModel[173] = new ModelRendererTurbo(this, 17, 161, textureX, textureY); // Box 612
		attachmentModel[174] = new ModelRendererTurbo(this, 25, 161, textureX, textureY); // Box 613
		attachmentModel[175] = new ModelRendererTurbo(this, 33, 161, textureX, textureY); // Box 614
		attachmentModel[176] = new ModelRendererTurbo(this, 41, 161, textureX, textureY); // Box 615
		attachmentModel[177] = new ModelRendererTurbo(this, 49, 161, textureX, textureY); // Box 616
		attachmentModel[178] = new ModelRendererTurbo(this, 57, 161, textureX, textureY); // Box 617
		attachmentModel[179] = new ModelRendererTurbo(this, 65, 161, textureX, textureY); // Box 618
		attachmentModel[180] = new ModelRendererTurbo(this, 73, 161, textureX, textureY); // Box 619
		attachmentModel[181] = new ModelRendererTurbo(this, 81, 161, textureX, textureY); // Box 620
		attachmentModel[182] = new ModelRendererTurbo(this, 89, 161, textureX, textureY); // Box 621
		attachmentModel[183] = new ModelRendererTurbo(this, 97, 161, textureX, textureY); // Box 622
		attachmentModel[184] = new ModelRendererTurbo(this, 177, 161, textureX, textureY); // Box 623
		attachmentModel[185] = new ModelRendererTurbo(this, 193, 161, textureX, textureY); // Box 624
		attachmentModel[186] = new ModelRendererTurbo(this, 209, 161, textureX, textureY); // Box 625
		attachmentModel[187] = new ModelRendererTurbo(this, 225, 161, textureX, textureY); // Box 626
		attachmentModel[188] = new ModelRendererTurbo(this, 241, 161, textureX, textureY); // Box 627
		attachmentModel[189] = new ModelRendererTurbo(this, 1, 169, textureX, textureY); // Box 628
		attachmentModel[190] = new ModelRendererTurbo(this, 17, 169, textureX, textureY); // Box 629
		attachmentModel[191] = new ModelRendererTurbo(this, 25, 169, textureX, textureY); // Box 630
		attachmentModel[192] = new ModelRendererTurbo(this, 33, 169, textureX, textureY); // Box 631
		attachmentModel[193] = new ModelRendererTurbo(this, 41, 169, textureX, textureY); // Box 633
		attachmentModel[194] = new ModelRendererTurbo(this, 57, 169, textureX, textureY); // Box 634
		attachmentModel[195] = new ModelRendererTurbo(this, 73, 169, textureX, textureY); // Box 635
		attachmentModel[196] = new ModelRendererTurbo(this, 89, 169, textureX, textureY); // Box 636
		attachmentModel[197] = new ModelRendererTurbo(this, 169, 169, textureX, textureY); // Box 637
		attachmentModel[198] = new ModelRendererTurbo(this, 185, 169, textureX, textureY); // Box 638
		attachmentModel[199] = new ModelRendererTurbo(this, 201, 169, textureX, textureY); // Box 639
		attachmentModel[200] = new ModelRendererTurbo(this, 217, 169, textureX, textureY); // Box 640
		attachmentModel[201] = new ModelRendererTurbo(this, 225, 169, textureX, textureY); // Box 641
		attachmentModel[202] = new ModelRendererTurbo(this, 233, 169, textureX, textureY); // Box 642
		attachmentModel[203] = new ModelRendererTurbo(this, 241, 169, textureX, textureY); // Box 643
		attachmentModel[204] = new ModelRendererTurbo(this, 249, 169, textureX, textureY); // Box 644
		attachmentModel[205] = new ModelRendererTurbo(this, 1, 177, textureX, textureY); // Box 645
		attachmentModel[206] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 646
		attachmentModel[207] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 648
		attachmentModel[208] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 650
		attachmentModel[209] = new ModelRendererTurbo(this, 9, 177, textureX, textureY); // Box 652
		attachmentModel[210] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 654
		attachmentModel[211] = new ModelRendererTurbo(this, 17, 177, textureX, textureY); // Box 655
		attachmentModel[212] = new ModelRendererTurbo(this, 25, 177, textureX, textureY); // Box 656
		attachmentModel[213] = new ModelRendererTurbo(this, 41, 177, textureX, textureY); // Box 657
		attachmentModel[214] = new ModelRendererTurbo(this, 105, 177, textureX, textureY); // Box 658
		attachmentModel[215] = new ModelRendererTurbo(this, 177, 177, textureX, textureY); // Box 659
		attachmentModel[216] = new ModelRendererTurbo(this, 1, 185, textureX, textureY); // Box 660
		attachmentModel[217] = new ModelRendererTurbo(this, 249, 177, textureX, textureY); // Box 661
		attachmentModel[218] = new ModelRendererTurbo(this, 73, 185, textureX, textureY); // Box 662
		attachmentModel[219] = new ModelRendererTurbo(this, 81, 185, textureX, textureY); // Box 663
		attachmentModel[220] = new ModelRendererTurbo(this, 89, 185, textureX, textureY); // Box 664
		attachmentModel[221] = new ModelRendererTurbo(this, 97, 185, textureX, textureY); // Box 665
		attachmentModel[222] = new ModelRendererTurbo(this, 105, 185, textureX, textureY); // Box 666
		attachmentModel[223] = new ModelRendererTurbo(this, 113, 185, textureX, textureY); // Box 667
		attachmentModel[224] = new ModelRendererTurbo(this, 177, 185, textureX, textureY); // Box 668
		attachmentModel[225] = new ModelRendererTurbo(this, 201, 185, textureX, textureY); // Box 669
		attachmentModel[226] = new ModelRendererTurbo(this, 225, 185, textureX, textureY); // Box 670
		attachmentModel[227] = new ModelRendererTurbo(this, 1, 193, textureX, textureY); // Box 671
		attachmentModel[228] = new ModelRendererTurbo(this, 25, 193, textureX, textureY); // Box 672
		attachmentModel[229] = new ModelRendererTurbo(this, 49, 193, textureX, textureY); // Box 673
		attachmentModel[230] = new ModelRendererTurbo(this, 73, 193, textureX, textureY); // Box 674
		attachmentModel[231] = new ModelRendererTurbo(this, 97, 193, textureX, textureY); // Box 675
		attachmentModel[232] = new ModelRendererTurbo(this, 121, 193, textureX, textureY); // Box 677
		attachmentModel[233] = new ModelRendererTurbo(this, 145, 193, textureX, textureY); // Box 678
		attachmentModel[234] = new ModelRendererTurbo(this, 169, 193, textureX, textureY); // Box 679
		attachmentModel[235] = new ModelRendererTurbo(this, 193, 193, textureX, textureY); // Box 680
		attachmentModel[236] = new ModelRendererTurbo(this, 217, 193, textureX, textureY); // Box 681
		attachmentModel[237] = new ModelRendererTurbo(this, 1, 201, textureX, textureY); // Box 683
		attachmentModel[238] = new ModelRendererTurbo(this, 25, 201, textureX, textureY); // Box 684
		attachmentModel[239] = new ModelRendererTurbo(this, 49, 201, textureX, textureY); // Box 685
		attachmentModel[240] = new ModelRendererTurbo(this, 193, 185, textureX, textureY); // Box 686
		attachmentModel[241] = new ModelRendererTurbo(this, 217, 185, textureX, textureY); // Box 687
		attachmentModel[242] = new ModelRendererTurbo(this, 241, 185, textureX, textureY); // Box 688
		attachmentModel[243] = new ModelRendererTurbo(this, 17, 193, textureX, textureY); // Box 689
		attachmentModel[244] = new ModelRendererTurbo(this, 41, 193, textureX, textureY); // Box 690
		attachmentModel[245] = new ModelRendererTurbo(this, 65, 193, textureX, textureY); // Box 691
		attachmentModel[246] = new ModelRendererTurbo(this, 89, 193, textureX, textureY); // Box 692
		attachmentModel[247] = new ModelRendererTurbo(this, 113, 193, textureX, textureY); // Box 693
		attachmentModel[248] = new ModelRendererTurbo(this, 137, 193, textureX, textureY); // Box 694
		attachmentModel[249] = new ModelRendererTurbo(this, 161, 193, textureX, textureY); // Box 695
		attachmentModel[250] = new ModelRendererTurbo(this, 185, 193, textureX, textureY); // Box 696
		attachmentModel[251] = new ModelRendererTurbo(this, 209, 193, textureX, textureY); // Box 697
		attachmentModel[252] = new ModelRendererTurbo(this, 73, 201, textureX, textureY); // Box 698

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 67, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 9
		attachmentModel[0].setRotationPoint(0F, -6F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 67, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 41
		attachmentModel[1].setRotationPoint(0F, -10F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 49
		attachmentModel[2].setRotationPoint(6F, 4F, -5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 105, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[3].setRotationPoint(0F, 7F, -3F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 5, 3, 6, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F); // Box 158
		attachmentModel[4].setRotationPoint(97F, 8F, -3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 159
		attachmentModel[5].setRotationPoint(57F, 8F, -3F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 160
		attachmentModel[6].setRotationPoint(53F, 8F, -3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 161
		attachmentModel[7].setRotationPoint(49F, 8F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 162
		attachmentModel[8].setRotationPoint(45F, 8F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 163
		attachmentModel[9].setRotationPoint(41F, 8F, -3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 164
		attachmentModel[10].setRotationPoint(37F, 8F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 165
		attachmentModel[11].setRotationPoint(33F, 8F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 166
		attachmentModel[12].setRotationPoint(29F, 8F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 167
		attachmentModel[13].setRotationPoint(25F, 8F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 168
		attachmentModel[14].setRotationPoint(21F, 8F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 169
		attachmentModel[15].setRotationPoint(17F, 8F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 170
		attachmentModel[16].setRotationPoint(13F, 8F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 171
		attachmentModel[17].setRotationPoint(9F, 8F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 172
		attachmentModel[18].setRotationPoint(5F, 8F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 173
		attachmentModel[19].setRotationPoint(0F, 8F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 174
		attachmentModel[20].setRotationPoint(64F, -11F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 175
		attachmentModel[21].setRotationPoint(56F, -11F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 176
		attachmentModel[22].setRotationPoint(52F, -11F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 177
		attachmentModel[23].setRotationPoint(48F, -11F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 178
		attachmentModel[24].setRotationPoint(44F, -11F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 179
		attachmentModel[25].setRotationPoint(40F, -11F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 180
		attachmentModel[26].setRotationPoint(36F, -11F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 181
		attachmentModel[27].setRotationPoint(32F, -11F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 182
		attachmentModel[28].setRotationPoint(28F, -11F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 183
		attachmentModel[29].setRotationPoint(24F, -11F, -3F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 184
		attachmentModel[30].setRotationPoint(20F, -11F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 185
		attachmentModel[31].setRotationPoint(16F, -11F, -3F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 186
		attachmentModel[32].setRotationPoint(12F, -11F, -3F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 187
		attachmentModel[33].setRotationPoint(8F, -11F, -3F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 188
		attachmentModel[34].setRotationPoint(4F, -11F, -3F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 4, 6, 3, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 200
		attachmentModel[35].setRotationPoint(98F, -3F, 8F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 202
		attachmentModel[36].setRotationPoint(58F, -3F, 8F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 203
		attachmentModel[37].setRotationPoint(54F, -3F, 8F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 204
		attachmentModel[38].setRotationPoint(50F, -3F, 8F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 205
		attachmentModel[39].setRotationPoint(46F, -3F, 8F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 206
		attachmentModel[40].setRotationPoint(42F, -3F, 8F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 207
		attachmentModel[41].setRotationPoint(38F, -3F, 8F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 208
		attachmentModel[42].setRotationPoint(34F, -3F, 8F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 209
		attachmentModel[43].setRotationPoint(30F, -3F, 8F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 210
		attachmentModel[44].setRotationPoint(26F, -3F, 8F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 211
		attachmentModel[45].setRotationPoint(22F, -3F, 8F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 212
		attachmentModel[46].setRotationPoint(18F, -3F, 8F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 213
		attachmentModel[47].setRotationPoint(14F, -3F, 8F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 214
		attachmentModel[48].setRotationPoint(10F, -3F, 8F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 67, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 215
		attachmentModel[49].setRotationPoint(0F, -3F, 7F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 216
		attachmentModel[50].setRotationPoint(6F, -3F, 8F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 2, 6, 2, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 217
		attachmentModel[51].setRotationPoint(2F, -3F, 8F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 67, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 219
		attachmentModel[52].setRotationPoint(0F, -3F, -8F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 2, 6, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F); // Box 220
		attachmentModel[53].setRotationPoint(2F, -3F, -9F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 4, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, -2F, 0F, 0F, -2F); // Box 221
		attachmentModel[54].setRotationPoint(98F, -3F, -9F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 222
		attachmentModel[55].setRotationPoint(58F, -3F, -9F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 223
		attachmentModel[56].setRotationPoint(54F, -3F, -9F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 224
		attachmentModel[57].setRotationPoint(50F, -3F, -9F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 225
		attachmentModel[58].setRotationPoint(46F, -3F, -9F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 226
		attachmentModel[59].setRotationPoint(42F, -3F, -9F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 227
		attachmentModel[60].setRotationPoint(38F, -3F, -9F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 228
		attachmentModel[61].setRotationPoint(34F, -3F, -9F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 229
		attachmentModel[62].setRotationPoint(30F, -3F, -9F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 230
		attachmentModel[63].setRotationPoint(26F, -3F, -9F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 231
		attachmentModel[64].setRotationPoint(22F, -3F, -9F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 232
		attachmentModel[65].setRotationPoint(18F, -3F, -9F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 233
		attachmentModel[66].setRotationPoint(14F, -3F, -9F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 234
		attachmentModel[67].setRotationPoint(10F, -3F, -9F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 235
		attachmentModel[68].setRotationPoint(6F, -3F, -9F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 503
		attachmentModel[69].setRotationPoint(50F, -4.5F, 3.5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 67, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[70].setRotationPoint(0F, -9F, -3F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 67, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 457
		attachmentModel[71].setRotationPoint(0F, -8F, -3F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 67, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 458
		attachmentModel[72].setRotationPoint(0F, -8F, 2F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 67, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 459
		attachmentModel[73].setRotationPoint(0F, -6F, -3F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 105, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 460
		attachmentModel[74].setRotationPoint(0F, -3F, -7F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 105, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[75].setRotationPoint(0F, 2F, -7F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 105, 1, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 462
		attachmentModel[76].setRotationPoint(0F, 2F, 5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 105, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[77].setRotationPoint(0F, -3F, 5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 105, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 464
		attachmentModel[78].setRotationPoint(0F, 6F, -3F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 105, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 465
		attachmentModel[79].setRotationPoint(0F, 6F, 2F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 468
		attachmentModel[80].setRotationPoint(0F, -11F, -3F);

		attachmentModel[81].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[81].setRotationPoint(9F, 6F, 5F);
		attachmentModel[81].rotateAngleZ = -0.78539816F;

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 479
		attachmentModel[82].setRotationPoint(50F, -5.5F, 3F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 483
		attachmentModel[83].setRotationPoint(50F, -3.5F, 4.5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 498
		attachmentModel[84].setRotationPoint(6F, 4F, 3F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0.5F, 0F, 1F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F); // Box 514
		attachmentModel[85].setRotationPoint(57F, 3F, 4F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, -0.5F, -0.5F); // Box 515
		attachmentModel[86].setRotationPoint(57F, 4F, 4F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0.5F, -0.5F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0.5F, 0.5F, 2.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 2.5F, 0F, -1F); // Box 516
		attachmentModel[87].setRotationPoint(58F, 5F, 3F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, -1F, 1.5F, 0F, -1F, 1.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 517
		attachmentModel[88].setRotationPoint(0F, 5F, 2F);

		attachmentModel[89].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 530
		attachmentModel[89].setRotationPoint(9F, 6F, -6F);
		attachmentModel[89].rotateAngleZ = -0.78539816F;

		attachmentModel[90].addShapeBox(-1F, -1F, 0F, 2, 2, 10, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 627
		attachmentModel[90].setRotationPoint(9F, 6F, -5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 559
		attachmentModel[91].setRotationPoint(-1F, -11F, -1F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0F, 0F, 0F); // Box 560
		attachmentModel[92].setRotationPoint(-2F, -11F, -0.5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 585
		attachmentModel[93].setRotationPoint(35F, -5.5F, 3F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 586
		attachmentModel[94].setRotationPoint(35F, -4.5F, 3.5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 587
		attachmentModel[95].setRotationPoint(35F, -3.5F, 4.5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 588
		attachmentModel[96].setRotationPoint(20F, -5.5F, 3F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 589
		attachmentModel[97].setRotationPoint(20F, -4.5F, 3.5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 590
		attachmentModel[98].setRotationPoint(20F, -3.5F, 4.5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 591
		attachmentModel[99].setRotationPoint(0F, -4.5F, 3.5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 592
		attachmentModel[100].setRotationPoint(0F, -5.5F, 3F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 593
		attachmentModel[101].setRotationPoint(0F, -3.5F, 4.5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 594
		attachmentModel[102].setRotationPoint(65F, -3.5F, 4.5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 595
		attachmentModel[103].setRotationPoint(65F, -4.5F, 3.5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 596
		attachmentModel[104].setRotationPoint(65F, -5.5F, 3F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 597
		attachmentModel[105].setRotationPoint(65F, -5.5F, -4F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 598
		attachmentModel[106].setRotationPoint(65F, -4.5F, -4.5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 599
		attachmentModel[107].setRotationPoint(65F, -3.5F, -5.5F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 600
		attachmentModel[108].setRotationPoint(50F, -5.5F, -4F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 601
		attachmentModel[109].setRotationPoint(50F, -4.5F, -4.5F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 602
		attachmentModel[110].setRotationPoint(50F, -3.5F, -5.5F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 603
		attachmentModel[111].setRotationPoint(35F, -5.5F, -4F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 604
		attachmentModel[112].setRotationPoint(35F, -4.5F, -4.5F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 605
		attachmentModel[113].setRotationPoint(35F, -3.5F, -5.5F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 606
		attachmentModel[114].setRotationPoint(20F, -5.5F, -4F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 607
		attachmentModel[115].setRotationPoint(20F, -4.5F, -4.5F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 608
		attachmentModel[116].setRotationPoint(20F, -3.5F, -5.5F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 609
		attachmentModel[117].setRotationPoint(0F, -5.5F, -4F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 610
		attachmentModel[118].setRotationPoint(0F, -4.5F, -4.5F);

		attachmentModel[119].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 611
		attachmentModel[119].setRotationPoint(0F, -3.5F, -5.5F);

		attachmentModel[120].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 615
		attachmentModel[120].setRotationPoint(50F, 2.5F, 4.5F);

		attachmentModel[121].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 616
		attachmentModel[121].setRotationPoint(50F, 3.5F, 3.5F);

		attachmentModel[122].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 617
		attachmentModel[122].setRotationPoint(50F, 4.5F, 3F);

		attachmentModel[123].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 618
		attachmentModel[123].setRotationPoint(35F, 3.5F, 3.5F);

		attachmentModel[124].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 619
		attachmentModel[124].setRotationPoint(35F, 4.5F, 3F);

		attachmentModel[125].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 620
		attachmentModel[125].setRotationPoint(35F, 2.5F, 4.5F);

		attachmentModel[126].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 621
		attachmentModel[126].setRotationPoint(20F, 4.5F, 3F);

		attachmentModel[127].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 622
		attachmentModel[127].setRotationPoint(20F, 3.5F, 3.5F);

		attachmentModel[128].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 623
		attachmentModel[128].setRotationPoint(20F, 2.5F, 4.5F);

		attachmentModel[129].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 624
		attachmentModel[129].setRotationPoint(0F, 4.5F, 3F);

		attachmentModel[130].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 625
		attachmentModel[130].setRotationPoint(0F, 3.5F, 3.5F);

		attachmentModel[131].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 626
		attachmentModel[131].setRotationPoint(0F, 2.5F, 4.5F);

		attachmentModel[132].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, 1F, 1.5F, 0F, 1F, 1.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 627
		attachmentModel[132].setRotationPoint(0F, 5F, -3F);

		attachmentModel[133].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 628
		attachmentModel[133].setRotationPoint(0F, 4.5F, -4F);

		attachmentModel[134].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 629
		attachmentModel[134].setRotationPoint(0F, 3.5F, -4.5F);

		attachmentModel[135].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 630
		attachmentModel[135].setRotationPoint(0F, 2.5F, -5.5F);

		attachmentModel[136].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 631
		attachmentModel[136].setRotationPoint(20F, 4.5F, -4F);

		attachmentModel[137].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 632
		attachmentModel[137].setRotationPoint(20F, 3.5F, -4.5F);

		attachmentModel[138].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 633
		attachmentModel[138].setRotationPoint(20F, 2.5F, -5.5F);

		attachmentModel[139].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 634
		attachmentModel[139].setRotationPoint(35F, 4.5F, -4F);

		attachmentModel[140].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 635
		attachmentModel[140].setRotationPoint(35F, 3.5F, -4.5F);

		attachmentModel[141].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 636
		attachmentModel[141].setRotationPoint(35F, 2.5F, -5.5F);

		attachmentModel[142].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 637
		attachmentModel[142].setRotationPoint(50F, 4.5F, -4F);

		attachmentModel[143].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 638
		attachmentModel[143].setRotationPoint(50F, 2.5F, -5.5F);

		attachmentModel[144].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 639
		attachmentModel[144].setRotationPoint(50F, 3.5F, -4.5F);

		attachmentModel[145].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0.5F, 0.5F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0.5F, -0.5F, 2.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 2.5F, 0F, 1F); // Box 640
		attachmentModel[145].setRotationPoint(58F, 5F, -4F);

		attachmentModel[146].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, -0.5F, 0.5F); // Box 641
		attachmentModel[146].setRotationPoint(57F, 4F, -5F);

		attachmentModel[147].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, 0.5F, 0F, -1F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F); // Box 642
		attachmentModel[147].setRotationPoint(57F, 3F, -5F);

		attachmentModel[148].addShapeBox(0F, 0F, 0F, 33, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 581
		attachmentModel[148].setRotationPoint(72F, -3F, 7F);

		attachmentModel[149].addShapeBox(0F, 0F, 0F, 33, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 582
		attachmentModel[149].setRotationPoint(72F, -3F, -8F);

		attachmentModel[150].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 584
		attachmentModel[150].setRotationPoint(67.5F, 1F, -8F);

		attachmentModel[151].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 585
		attachmentModel[151].setRotationPoint(67F, 1F, -9F);

		attachmentModel[152].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 586
		attachmentModel[152].setRotationPoint(71F, 1F, -9F);

		attachmentModel[153].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F); // Box 587
		attachmentModel[153].setRotationPoint(67F, -3F, -9F);

		attachmentModel[154].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F); // Box 588
		attachmentModel[154].setRotationPoint(71F, -3F, -9F);

		attachmentModel[155].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 589
		attachmentModel[155].setRotationPoint(67.5F, -3F, -8F);

		attachmentModel[156].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 590
		attachmentModel[156].setRotationPoint(67.5F, 1F, 7F);

		attachmentModel[157].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 591
		attachmentModel[157].setRotationPoint(67F, 1F, 7F);

		attachmentModel[158].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F); // Box 592
		attachmentModel[158].setRotationPoint(67F, -3F, 7F);

		attachmentModel[159].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 593
		attachmentModel[159].setRotationPoint(67.5F, -3F, 7F);

		attachmentModel[160].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F); // Box 594
		attachmentModel[160].setRotationPoint(71F, -3F, 7F);

		attachmentModel[161].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 595
		attachmentModel[161].setRotationPoint(71F, 1F, 7F);

		attachmentModel[162].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 596
		attachmentModel[162].setRotationPoint(67F, -3F, 8F);

		attachmentModel[163].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 597
		attachmentModel[163].setRotationPoint(66F, -3F, 8F);

		attachmentModel[164].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 598
		attachmentModel[164].setRotationPoint(62F, -3F, 8F);

		attachmentModel[165].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 599
		attachmentModel[165].setRotationPoint(67F, 1F, 8F);

		attachmentModel[166].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 602
		attachmentModel[166].setRotationPoint(70F, -3F, 8F);

		attachmentModel[167].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 606
		attachmentModel[167].setRotationPoint(70F, 1F, 8F);

		attachmentModel[168].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 607
		attachmentModel[168].setRotationPoint(74F, -3F, 8F);

		attachmentModel[169].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 608
		attachmentModel[169].setRotationPoint(78F, -3F, 8F);

		attachmentModel[170].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 609
		attachmentModel[170].setRotationPoint(86F, -3F, 8F);

		attachmentModel[171].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 610
		attachmentModel[171].setRotationPoint(82F, -3F, 8F);

		attachmentModel[172].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 611
		attachmentModel[172].setRotationPoint(94F, -3F, 8F);

		attachmentModel[173].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 612
		attachmentModel[173].setRotationPoint(90F, -3F, 8F);

		attachmentModel[174].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 613
		attachmentModel[174].setRotationPoint(94F, -3F, -9F);

		attachmentModel[175].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 614
		attachmentModel[175].setRotationPoint(90F, -3F, -9F);

		attachmentModel[176].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 615
		attachmentModel[176].setRotationPoint(86F, -3F, -9F);

		attachmentModel[177].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 616
		attachmentModel[177].setRotationPoint(82F, -3F, -9F);

		attachmentModel[178].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 617
		attachmentModel[178].setRotationPoint(78F, -3F, -9F);

		attachmentModel[179].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 618
		attachmentModel[179].setRotationPoint(74F, -3F, -9F);

		attachmentModel[180].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -1F, 0F, 0F, 1F, 0.5F, -0.5F, 1F, 0.5F, 0.5F, -1F, 0F, 0F, 0F, 0F, 1F, 2.5F, 0F, 1F, 2.5F, 0F, -1F, 0F, 0F, -1F); // Box 619
		attachmentModel[180].setRotationPoint(63F, 5F, 3F);

		attachmentModel[181].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -1F); // Box 620
		attachmentModel[181].setRotationPoint(64F, 4F, 4F);

		attachmentModel[182].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 1F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 1F, 1F, 0F, 1F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F); // Box 621
		attachmentModel[182].setRotationPoint(64F, 3F, 4F);

		attachmentModel[183].addShapeBox(0F, 0F, 0F, 37, 1, 1, 0F, 1.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 1.5F, 0F, 1F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 622
		attachmentModel[183].setRotationPoint(68F, 5F, 2F);

		attachmentModel[184].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 623
		attachmentModel[184].setRotationPoint(73F, 4.5F, 3F);

		attachmentModel[185].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 624
		attachmentModel[185].setRotationPoint(73F, 2.5F, 4.5F);

		attachmentModel[186].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 625
		attachmentModel[186].setRotationPoint(73F, 3.5F, 3.5F);

		attachmentModel[187].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 626
		attachmentModel[187].setRotationPoint(88F, 2.5F, 4.5F);

		attachmentModel[188].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 627
		attachmentModel[188].setRotationPoint(88F, 4.5F, 3F);

		attachmentModel[189].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 628
		attachmentModel[189].setRotationPoint(88F, 3.5F, 3.5F);

		attachmentModel[190].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 629
		attachmentModel[190].setRotationPoint(103F, 2.5F, 4.5F);

		attachmentModel[191].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 630
		attachmentModel[191].setRotationPoint(103F, 3.5F, 3.5F);

		attachmentModel[192].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 631
		attachmentModel[192].setRotationPoint(103F, 4.5F, 3F);

		attachmentModel[193].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 633
		attachmentModel[193].setRotationPoint(73F, 3.5F, -4.5F);

		attachmentModel[194].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 634
		attachmentModel[194].setRotationPoint(73F, 4.5F, -4F);

		attachmentModel[195].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 635
		attachmentModel[195].setRotationPoint(73F, 2.5F, -5.5F);

		attachmentModel[196].addShapeBox(0F, 0F, 0F, 37, 1, 1, 0F, 1.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 1.5F, 0F, -1F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 636
		attachmentModel[196].setRotationPoint(68F, 5F, -3F);

		attachmentModel[197].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 637
		attachmentModel[197].setRotationPoint(88F, 2.5F, -5.5F);

		attachmentModel[198].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 638
		attachmentModel[198].setRotationPoint(88F, 3.5F, -4.5F);

		attachmentModel[199].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 639
		attachmentModel[199].setRotationPoint(88F, 4.5F, -4F);

		attachmentModel[200].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 640
		attachmentModel[200].setRotationPoint(103F, 2.5F, -5.5F);

		attachmentModel[201].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 641
		attachmentModel[201].setRotationPoint(103F, 3.5F, -4.5F);

		attachmentModel[202].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 642
		attachmentModel[202].setRotationPoint(103F, 4.5F, -4F);

		attachmentModel[203].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 1F); // Box 643
		attachmentModel[203].setRotationPoint(64F, 4F, -5F);

		attachmentModel[204].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -1F, 0F, 0F, 1F, 0.5F, 0.5F, 1F, 0.5F, -0.5F, -1F, 0F, 0F, 0F, 0F, -1F, 2.5F, 0F, -1F, 2.5F, 0F, 1F, 0F, 0F, 1F); // Box 644
		attachmentModel[204].setRotationPoint(63F, 5F, -4F);

		attachmentModel[205].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 1F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F); // Box 645
		attachmentModel[205].setRotationPoint(64F, 3F, -5F);

		attachmentModel[206].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 646
		attachmentModel[206].setRotationPoint(70F, -3F, -9F);

		attachmentModel[207].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 648
		attachmentModel[207].setRotationPoint(70F, 1F, -9F);

		attachmentModel[208].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 650
		attachmentModel[208].setRotationPoint(67F, -3F, -9F);

		attachmentModel[209].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 652
		attachmentModel[209].setRotationPoint(66F, -3F, -9F);

		attachmentModel[210].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 654
		attachmentModel[210].setRotationPoint(67F, 1F, -9F);

		attachmentModel[211].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 655
		attachmentModel[211].setRotationPoint(62F, -3F, -9F);

		attachmentModel[212].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 656
		attachmentModel[212].setRotationPoint(60F, -11F, -3F);

		attachmentModel[213].addShapeBox(0F, 0F, 0F, 31, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 657
		attachmentModel[213].setRotationPoint(74F, -8F, 2F);

		attachmentModel[214].addShapeBox(0F, 0F, 0F, 31, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 658
		attachmentModel[214].setRotationPoint(74F, -9F, -3F);

		attachmentModel[215].addShapeBox(0F, 0F, 0F, 31, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 659
		attachmentModel[215].setRotationPoint(74F, -8F, -3F);

		attachmentModel[216].addShapeBox(0F, 0F, 0F, 31, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 660
		attachmentModel[216].setRotationPoint(74F, -6F, 2F);

		attachmentModel[217].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 661
		attachmentModel[217].setRotationPoint(103F, -5.5F, 3F);

		attachmentModel[218].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 662
		attachmentModel[218].setRotationPoint(103F, -4.5F, 3.5F);

		attachmentModel[219].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 663
		attachmentModel[219].setRotationPoint(103F, -3.5F, 4.5F);

		attachmentModel[220].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 664
		attachmentModel[220].setRotationPoint(103F, -5.5F, -4F);

		attachmentModel[221].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 665
		attachmentModel[221].setRotationPoint(103F, -4.5F, -4.5F);

		attachmentModel[222].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 666
		attachmentModel[222].setRotationPoint(103F, -3.5F, -5.5F);

		attachmentModel[223].addShapeBox(0F, 0F, 0F, 31, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 667
		attachmentModel[223].setRotationPoint(74F, -6F, -3F);

		attachmentModel[224].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 668
		attachmentModel[224].setRotationPoint(65F, 8F, -3F);

		attachmentModel[225].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 669
		attachmentModel[225].setRotationPoint(61F, 8F, -3F);

		attachmentModel[226].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 670
		attachmentModel[226].setRotationPoint(73F, 8F, -3F);

		attachmentModel[227].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 671
		attachmentModel[227].setRotationPoint(69F, 8F, -3F);

		attachmentModel[228].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 672
		attachmentModel[228].setRotationPoint(81F, 8F, -3F);

		attachmentModel[229].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 673
		attachmentModel[229].setRotationPoint(77F, 8F, -3F);

		attachmentModel[230].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 674
		attachmentModel[230].setRotationPoint(89F, 8F, -3F);

		attachmentModel[231].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 675
		attachmentModel[231].setRotationPoint(85F, 8F, -3F);

		attachmentModel[232].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 677
		attachmentModel[232].setRotationPoint(93F, 8F, -3F);

		attachmentModel[233].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 678
		attachmentModel[233].setRotationPoint(78F, -11F, -3F);

		attachmentModel[234].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 679
		attachmentModel[234].setRotationPoint(82F, -11F, -3F);

		attachmentModel[235].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 680
		attachmentModel[235].setRotationPoint(90F, -11F, -3F);

		attachmentModel[236].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 681
		attachmentModel[236].setRotationPoint(86F, -11F, -3F);

		attachmentModel[237].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 683
		attachmentModel[237].setRotationPoint(94F, -11F, -3F);

		attachmentModel[238].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 684
		attachmentModel[238].setRotationPoint(74F, -11F, -3F);

		attachmentModel[239].addShapeBox(0F, 0F, 0F, 3, 3, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 3F, -2F, 0F, 3F, -2F, 0F, 0F, -2F, 0F); // Box 685
		attachmentModel[239].setRotationPoint(98F, -11F, -3F);

		attachmentModel[240].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 686
		attachmentModel[240].setRotationPoint(74F, -5.5F, 3F);

		attachmentModel[241].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 1F, 0F, 1F); // Box 687
		attachmentModel[241].setRotationPoint(74F, -4.5F, 3.5F);

		attachmentModel[242].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 688
		attachmentModel[242].setRotationPoint(73F, -3.5F, 4.5F);

		attachmentModel[243].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 689
		attachmentModel[243].setRotationPoint(88F, -4.5F, 3.5F);

		attachmentModel[244].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 690
		attachmentModel[244].setRotationPoint(88F, -5.5F, 3F);

		attachmentModel[245].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 691
		attachmentModel[245].setRotationPoint(88F, -3.5F, 4.5F);

		attachmentModel[246].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 692
		attachmentModel[246].setRotationPoint(74F, -5.5F, -4F);

		attachmentModel[247].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 693
		attachmentModel[247].setRotationPoint(73F, -3.5F, -5.5F);

		attachmentModel[248].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 1F, 0F, -1F); // Box 694
		attachmentModel[248].setRotationPoint(74F, -4.5F, -4.5F);

		attachmentModel[249].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 695
		attachmentModel[249].setRotationPoint(88F, -5.5F, -4F);

		attachmentModel[250].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 696
		attachmentModel[250].setRotationPoint(88F, -4.5F, -4.5F);

		attachmentModel[251].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 697
		attachmentModel[251].setRotationPoint(88F, -3.5F, -5.5F);

		attachmentModel[252].addShapeBox(0F, 0F, 0F, 30, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 698
		attachmentModel[252].setRotationPoint(74F, -10F, -3F);

		flipAll();
	}
}