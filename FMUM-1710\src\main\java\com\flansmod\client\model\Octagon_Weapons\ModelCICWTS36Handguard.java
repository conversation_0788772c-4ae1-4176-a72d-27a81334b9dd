//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICWTS36Handguard
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36Handguard extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelCICWTS36Handguard() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[116];
		attachmentModel[0] = new ModelRendererTurbo(this, 193, 33, textureX, textureY); // Box 57
		attachmentModel[1] = new ModelRendererTurbo(this, 129, 25, textureX, textureY); // Box 59
		attachmentModel[2] = new ModelRendererTurbo(this, 313, 33, textureX, textureY); // Box 62
		attachmentModel[3] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 63
		attachmentModel[4] = new ModelRendererTurbo(this, 153, 25, textureX, textureY); // Box 64
		attachmentModel[5] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 93
		attachmentModel[6] = new ModelRendererTurbo(this, 241, 41, textureX, textureY); // Box 94
		attachmentModel[7] = new ModelRendererTurbo(this, 265, 25, textureX, textureY); // Box 100
		attachmentModel[8] = new ModelRendererTurbo(this, 425, 33, textureX, textureY); // Box 102
		attachmentModel[9] = new ModelRendererTurbo(this, 449, 33, textureX, textureY); // Box 103
		attachmentModel[10] = new ModelRendererTurbo(this, 473, 33, textureX, textureY); // Box 104
		attachmentModel[11] = new ModelRendererTurbo(this, 353, 41, textureX, textureY); // Box 105
		attachmentModel[12] = new ModelRendererTurbo(this, 377, 41, textureX, textureY); // Box 106
		attachmentModel[13] = new ModelRendererTurbo(this, 401, 41, textureX, textureY); // Box 107
		attachmentModel[14] = new ModelRendererTurbo(this, 289, 25, textureX, textureY); // Box 111
		attachmentModel[15] = new ModelRendererTurbo(this, 313, 25, textureX, textureY); // Box 112
		attachmentModel[16] = new ModelRendererTurbo(this, 337, 25, textureX, textureY); // Box 113
		attachmentModel[17] = new ModelRendererTurbo(this, 361, 25, textureX, textureY); // Box 114
		attachmentModel[18] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 115
		attachmentModel[19] = new ModelRendererTurbo(this, 449, 41, textureX, textureY); // Box 116
		attachmentModel[20] = new ModelRendererTurbo(this, 473, 41, textureX, textureY); // Box 117
		attachmentModel[21] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 118
		attachmentModel[22] = new ModelRendererTurbo(this, 25, 49, textureX, textureY); // Box 119
		attachmentModel[23] = new ModelRendererTurbo(this, 49, 49, textureX, textureY); // Box 120
		attachmentModel[24] = new ModelRendererTurbo(this, 73, 49, textureX, textureY); // Box 121
		attachmentModel[25] = new ModelRendererTurbo(this, 97, 49, textureX, textureY); // Box 123
		attachmentModel[26] = new ModelRendererTurbo(this, 121, 49, textureX, textureY); // Box 124
		attachmentModel[27] = new ModelRendererTurbo(this, 385, 25, textureX, textureY); // Box 125
		attachmentModel[28] = new ModelRendererTurbo(this, 409, 25, textureX, textureY); // Box 126
		attachmentModel[29] = new ModelRendererTurbo(this, 65, 81, textureX, textureY); // Box 208
		attachmentModel[30] = new ModelRendererTurbo(this, 505, 41, textureX, textureY); // Box 209
		attachmentModel[31] = new ModelRendererTurbo(this, 289, 57, textureX, textureY); // Box 210
		attachmentModel[32] = new ModelRendererTurbo(this, 297, 57, textureX, textureY); // Box 211
		attachmentModel[33] = new ModelRendererTurbo(this, 369, 57, textureX, textureY); // Box 212
		attachmentModel[34] = new ModelRendererTurbo(this, 377, 57, textureX, textureY); // Box 213
		attachmentModel[35] = new ModelRendererTurbo(this, 401, 57, textureX, textureY); // Box 214
		attachmentModel[36] = new ModelRendererTurbo(this, 505, 57, textureX, textureY); // Box 215
		attachmentModel[37] = new ModelRendererTurbo(this, 465, 65, textureX, textureY); // Box 216
		attachmentModel[38] = new ModelRendererTurbo(this, 505, 65, textureX, textureY); // Box 217
		attachmentModel[39] = new ModelRendererTurbo(this, 385, 73, textureX, textureY); // Box 218
		attachmentModel[40] = new ModelRendererTurbo(this, 481, 73, textureX, textureY); // Box 219
		attachmentModel[41] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 220
		attachmentModel[42] = new ModelRendererTurbo(this, 497, 105, textureX, textureY); // Box 341
		attachmentModel[43] = new ModelRendererTurbo(this, 505, 73, textureX, textureY); // Box 342
		attachmentModel[44] = new ModelRendererTurbo(this, 57, 81, textureX, textureY); // Box 343
		attachmentModel[45] = new ModelRendererTurbo(this, 369, 89, textureX, textureY); // Box 344
		attachmentModel[46] = new ModelRendererTurbo(this, 369, 97, textureX, textureY); // Box 345
		attachmentModel[47] = new ModelRendererTurbo(this, 497, 97, textureX, textureY); // Box 346
		attachmentModel[48] = new ModelRendererTurbo(this, 505, 97, textureX, textureY); // Box 347
		attachmentModel[49] = new ModelRendererTurbo(this, 193, 113, textureX, textureY); // Box 348
		attachmentModel[50] = new ModelRendererTurbo(this, 201, 113, textureX, textureY); // Box 349
		attachmentModel[51] = new ModelRendererTurbo(this, 209, 113, textureX, textureY); // Box 350
		attachmentModel[52] = new ModelRendererTurbo(this, 257, 113, textureX, textureY); // Box 351
		attachmentModel[53] = new ModelRendererTurbo(this, 265, 113, textureX, textureY); // Box 352
		attachmentModel[54] = new ModelRendererTurbo(this, 273, 113, textureX, textureY); // Box 353
		attachmentModel[55] = new ModelRendererTurbo(this, 361, 137, textureX, textureY); // Box 382
		attachmentModel[56] = new ModelRendererTurbo(this, 473, 113, textureX, textureY); // Box 405
		attachmentModel[57] = new ModelRendererTurbo(this, 257, 121, textureX, textureY); // Box 406
		attachmentModel[58] = new ModelRendererTurbo(this, 297, 121, textureX, textureY); // Box 407
		attachmentModel[59] = new ModelRendererTurbo(this, 497, 57, textureX, textureY); // Box 410
		attachmentModel[60] = new ModelRendererTurbo(this, 457, 65, textureX, textureY); // Box 411
		attachmentModel[61] = new ModelRendererTurbo(this, 1, 145, textureX, textureY); // Box 426
		attachmentModel[62] = new ModelRendererTurbo(this, 273, 137, textureX, textureY); // Box 439
		attachmentModel[63] = new ModelRendererTurbo(this, 281, 145, textureX, textureY); // Box 440
		attachmentModel[64] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 441
		attachmentModel[65] = new ModelRendererTurbo(this, 497, 129, textureX, textureY); // Box 416
		attachmentModel[66] = new ModelRendererTurbo(this, 121, 185, textureX, textureY); // Box 417
		attachmentModel[67] = new ModelRendererTurbo(this, 241, 145, textureX, textureY); // Box 418
		attachmentModel[68] = new ModelRendererTurbo(this, 489, 145, textureX, textureY); // Box 448
		attachmentModel[69] = new ModelRendererTurbo(this, 497, 185, textureX, textureY); // Box 449
		attachmentModel[70] = new ModelRendererTurbo(this, 57, 193, textureX, textureY); // Box 450
		attachmentModel[71] = new ModelRendererTurbo(this, 73, 193, textureX, textureY); // Box 451
		attachmentModel[72] = new ModelRendererTurbo(this, 153, 193, textureX, textureY); // Box 452
		attachmentModel[73] = new ModelRendererTurbo(this, 193, 193, textureX, textureY); // Box 453
		attachmentModel[74] = new ModelRendererTurbo(this, 233, 193, textureX, textureY); // Box 454
		attachmentModel[75] = new ModelRendererTurbo(this, 17, 201, textureX, textureY); // Box 455
		attachmentModel[76] = new ModelRendererTurbo(this, 505, 153, textureX, textureY); // Box 456
		attachmentModel[77] = new ModelRendererTurbo(this, 505, 161, textureX, textureY); // Box 457
		attachmentModel[78] = new ModelRendererTurbo(this, 505, 169, textureX, textureY); // Box 458
		attachmentModel[79] = new ModelRendererTurbo(this, 89, 193, textureX, textureY); // Box 459
		attachmentModel[80] = new ModelRendererTurbo(this, 105, 193, textureX, textureY); // Box 460
		attachmentModel[81] = new ModelRendererTurbo(this, 273, 193, textureX, textureY); // Box 461
		attachmentModel[82] = new ModelRendererTurbo(this, 289, 193, textureX, textureY); // Box 462
		attachmentModel[83] = new ModelRendererTurbo(this, 305, 193, textureX, textureY); // Box 463
		attachmentModel[84] = new ModelRendererTurbo(this, 321, 193, textureX, textureY); // Box 464
		attachmentModel[85] = new ModelRendererTurbo(this, 137, 201, textureX, textureY); // Box 465
		attachmentModel[86] = new ModelRendererTurbo(this, 177, 201, textureX, textureY); // Box 466
		attachmentModel[87] = new ModelRendererTurbo(this, 217, 201, textureX, textureY); // Box 467
		attachmentModel[88] = new ModelRendererTurbo(this, 257, 201, textureX, textureY); // Box 468
		attachmentModel[89] = new ModelRendererTurbo(this, 377, 201, textureX, textureY); // Box 469
		attachmentModel[90] = new ModelRendererTurbo(this, 17, 209, textureX, textureY); // Box 470
		attachmentModel[91] = new ModelRendererTurbo(this, 137, 209, textureX, textureY); // Box 471
		attachmentModel[92] = new ModelRendererTurbo(this, 385, 177, textureX, textureY); // Box 472
		attachmentModel[93] = new ModelRendererTurbo(this, 241, 185, textureX, textureY); // Box 473
		attachmentModel[94] = new ModelRendererTurbo(this, 361, 193, textureX, textureY); // Box 474
		attachmentModel[95] = new ModelRendererTurbo(this, 369, 193, textureX, textureY); // Box 475
		attachmentModel[96] = new ModelRendererTurbo(this, 497, 201, textureX, textureY); // Box 476
		attachmentModel[97] = new ModelRendererTurbo(this, 257, 209, textureX, textureY); // Box 477
		attachmentModel[98] = new ModelRendererTurbo(this, 273, 209, textureX, textureY); // Box 478
		attachmentModel[99] = new ModelRendererTurbo(this, 289, 209, textureX, textureY); // Box 479
		attachmentModel[100] = new ModelRendererTurbo(this, 305, 209, textureX, textureY); // Box 480
		attachmentModel[101] = new ModelRendererTurbo(this, 321, 209, textureX, textureY); // Box 481
		attachmentModel[102] = new ModelRendererTurbo(this, 361, 209, textureX, textureY); // Box 482
		attachmentModel[103] = new ModelRendererTurbo(this, 401, 209, textureX, textureY); // Box 483
		attachmentModel[104] = new ModelRendererTurbo(this, 441, 209, textureX, textureY); // Box 484
		attachmentModel[105] = new ModelRendererTurbo(this, 1, 217, textureX, textureY); // Box 485
		attachmentModel[106] = new ModelRendererTurbo(this, 41, 217, textureX, textureY); // Box 486
		attachmentModel[107] = new ModelRendererTurbo(this, 481, 209, textureX, textureY); // Box 487
		attachmentModel[108] = new ModelRendererTurbo(this, 497, 209, textureX, textureY); // Box 488
		attachmentModel[109] = new ModelRendererTurbo(this, 81, 217, textureX, textureY); // Box 489
		attachmentModel[110] = new ModelRendererTurbo(this, 97, 217, textureX, textureY); // Box 490
		attachmentModel[111] = new ModelRendererTurbo(this, 113, 217, textureX, textureY); // Box 491
		attachmentModel[112] = new ModelRendererTurbo(this, 129, 217, textureX, textureY); // Box 492
		attachmentModel[113] = new ModelRendererTurbo(this, 385, 193, textureX, textureY); // Box 493
		attachmentModel[114] = new ModelRendererTurbo(this, 505, 193, textureX, textureY); // Box 494
		attachmentModel[115] = new ModelRendererTurbo(this, 145, 217, textureX, textureY); // Box 495

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 56, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 57
		attachmentModel[0].setRotationPoint(0F, -3F, 7F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 59
		attachmentModel[1].setRotationPoint(55F, -6F, -4F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 62
		attachmentModel[2].setRotationPoint(0F, -6F, 2F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 63
		attachmentModel[3].setRotationPoint(0F, -6F, -3F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 64
		attachmentModel[4].setRotationPoint(55F, -6F, 3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 56, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 93
		attachmentModel[5].setRotationPoint(0F, -3F, -8F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 56, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 94
		attachmentModel[6].setRotationPoint(0F, 5F, -3F);

		attachmentModel[7].addShapeBox(-1F, -1F, 0F, 2, 2, 2, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 100
		attachmentModel[7].setRotationPoint(54F, 8F, -1F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 102
		attachmentModel[8].setRotationPoint(20F, 8F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 103
		attachmentModel[9].setRotationPoint(12F, 8F, -3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 104
		attachmentModel[10].setRotationPoint(16F, 8F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 105
		attachmentModel[11].setRotationPoint(0F, 8F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 106
		attachmentModel[12].setRotationPoint(4F, 8F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 107
		attachmentModel[13].setRotationPoint(8F, 8F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, -0.5F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, -1F, 0F); // Box 111
		attachmentModel[14].setRotationPoint(52F, 8F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 112
		attachmentModel[15].setRotationPoint(52F, 7F, 1F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F); // Box 113
		attachmentModel[16].setRotationPoint(52F, 7F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, -0.5F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, -1F, 0F); // Box 114
		attachmentModel[17].setRotationPoint(52F, 8F, 1F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 115
		attachmentModel[18].setRotationPoint(48F, 8F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 116
		attachmentModel[19].setRotationPoint(24F, 8F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 117
		attachmentModel[20].setRotationPoint(28F, 8F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 118
		attachmentModel[21].setRotationPoint(32F, 8F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 119
		attachmentModel[22].setRotationPoint(36F, 8F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 120
		attachmentModel[23].setRotationPoint(40F, 8F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 121
		attachmentModel[24].setRotationPoint(44F, 8F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 123
		attachmentModel[25].setRotationPoint(6F, 4F, -5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 52, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 124
		attachmentModel[26].setRotationPoint(0F, 7F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 125
		attachmentModel[27].setRotationPoint(53F, 7F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 126
		attachmentModel[28].setRotationPoint(53F, 7F, 1F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 3, 6, 3, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 208
		attachmentModel[29].setRotationPoint(50F, -3F, 8F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 209
		attachmentModel[30].setRotationPoint(46F, -3F, 8F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 210
		attachmentModel[31].setRotationPoint(42F, -3F, 8F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 211
		attachmentModel[32].setRotationPoint(38F, -3F, 8F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 212
		attachmentModel[33].setRotationPoint(34F, -3F, 8F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 213
		attachmentModel[34].setRotationPoint(30F, -3F, 8F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 214
		attachmentModel[35].setRotationPoint(22F, -3F, 8F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 215
		attachmentModel[36].setRotationPoint(14F, -3F, 8F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 216
		attachmentModel[37].setRotationPoint(26F, -3F, 8F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 217
		attachmentModel[38].setRotationPoint(18F, -3F, 8F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 218
		attachmentModel[39].setRotationPoint(10F, -3F, 8F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 219
		attachmentModel[40].setRotationPoint(6F, -3F, 8F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 2, 6, 2, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 220
		attachmentModel[41].setRotationPoint(2F, -3F, 8F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 3, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, -2F, 0F, 0F, -2F); // Box 341
		attachmentModel[42].setRotationPoint(50F, -3F, -9F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 342
		attachmentModel[43].setRotationPoint(46F, -3F, -9F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 343
		attachmentModel[44].setRotationPoint(42F, -3F, -9F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 344
		attachmentModel[45].setRotationPoint(38F, -3F, -9F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 345
		attachmentModel[46].setRotationPoint(34F, -3F, -9F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 346
		attachmentModel[47].setRotationPoint(30F, -3F, -9F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 347
		attachmentModel[48].setRotationPoint(26F, -3F, -9F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 348
		attachmentModel[49].setRotationPoint(18F, -3F, -9F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 349
		attachmentModel[50].setRotationPoint(22F, -3F, -9F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 350
		attachmentModel[51].setRotationPoint(14F, -3F, -9F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 351
		attachmentModel[52].setRotationPoint(10F, -3F, -9F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 352
		attachmentModel[53].setRotationPoint(6F, -3F, -9F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 2, 6, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F); // Box 353
		attachmentModel[54].setRotationPoint(2F, -3F, -9F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 56, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 382
		attachmentModel[55].setRotationPoint(0F, -3F, -7F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 405
		attachmentModel[56].setRotationPoint(40F, -5.5F, 3.5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 406
		attachmentModel[57].setRotationPoint(40F, -4.5F, 4.5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 407
		attachmentModel[58].setRotationPoint(40F, -3.5F, 5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 410
		attachmentModel[59].setRotationPoint(54F, -5.5F, 3.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 411
		attachmentModel[60].setRotationPoint(54F, -3.5F, 5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 56, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[61].setRotationPoint(0F, 5F, 2F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 439
		attachmentModel[62].setRotationPoint(6F, 4F, 3F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 56, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 440
		attachmentModel[63].setRotationPoint(0F, 6F, -3F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 56, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 441
		attachmentModel[64].setRotationPoint(0F, 6F, 2F);

		attachmentModel[65].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 416
		attachmentModel[65].setRotationPoint(9F, 6F, 5F);
		attachmentModel[65].rotateAngleZ = -0.78539816F;

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 2, 2, 10, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 417
		attachmentModel[66].setRotationPoint(8F, 5F, -5F);

		attachmentModel[67].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 418
		attachmentModel[67].setRotationPoint(9F, 6F, -6F);
		attachmentModel[67].rotateAngleZ = -0.78539816F;

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		attachmentModel[68].setRotationPoint(54F, -4.5F, 4.5F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 449
		attachmentModel[69].setRotationPoint(26F, -4.5F, 4.5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 450
		attachmentModel[70].setRotationPoint(26F, -5.5F, 3.5F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 451
		attachmentModel[71].setRotationPoint(26F, -3.5F, 5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 452
		attachmentModel[72].setRotationPoint(0F, -5.5F, 3.5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 453
		attachmentModel[73].setRotationPoint(0F, -4.5F, 4.5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 454
		attachmentModel[74].setRotationPoint(0F, -3.5F, 5F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 455
		attachmentModel[75].setRotationPoint(0F, -6F, 3F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[76].setRotationPoint(54F, -5.5F, -4.5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 457
		attachmentModel[77].setRotationPoint(54F, -4.5F, -5.5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 458
		attachmentModel[78].setRotationPoint(54F, -3.5F, -6F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 459
		attachmentModel[79].setRotationPoint(40F, -4.5F, -5.5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 460
		attachmentModel[80].setRotationPoint(40F, -3.5F, -6F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[81].setRotationPoint(40F, -5.5F, -4.5F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 462
		attachmentModel[82].setRotationPoint(26F, -5.5F, -4.5F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[83].setRotationPoint(26F, -4.5F, -5.5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 464
		attachmentModel[84].setRotationPoint(26F, -3.5F, -6F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 465
		attachmentModel[85].setRotationPoint(0F, -5.5F, -4.5F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 466
		attachmentModel[86].setRotationPoint(0F, -4.5F, -5.5F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 467
		attachmentModel[87].setRotationPoint(0F, -3.5F, -6F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 56, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 468
		attachmentModel[88].setRotationPoint(0F, 2F, -7F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 56, 1, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[89].setRotationPoint(0F, 2F, 5F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 56, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 470
		attachmentModel[90].setRotationPoint(0F, -3F, 5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[91].setRotationPoint(0F, -6F, -4F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 472
		attachmentModel[92].setRotationPoint(54F, 4.5F, 3.5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 473
		attachmentModel[93].setRotationPoint(54F, 3.5F, 4.5F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 474
		attachmentModel[94].setRotationPoint(54F, 2.5F, 5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 475
		attachmentModel[95].setRotationPoint(40F, 4.5F, 3.5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 476
		attachmentModel[96].setRotationPoint(40F, 3.5F, 4.5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 477
		attachmentModel[97].setRotationPoint(40F, 2.5F, 5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 478
		attachmentModel[98].setRotationPoint(26F, 4.5F, 3.5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 479
		attachmentModel[99].setRotationPoint(26F, 3.5F, 4.5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 480
		attachmentModel[100].setRotationPoint(26F, 2.5F, 5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 481
		attachmentModel[101].setRotationPoint(0F, 4.5F, 3.5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 482
		attachmentModel[102].setRotationPoint(0F, 3.5F, 4.5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 483
		attachmentModel[103].setRotationPoint(0F, 2.5F, 5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 484
		attachmentModel[104].setRotationPoint(0F, 2.5F, -6F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 485
		attachmentModel[105].setRotationPoint(0F, 3.5F, -5.5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 486
		attachmentModel[106].setRotationPoint(0F, 4.5F, -4.5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 487
		attachmentModel[107].setRotationPoint(26F, 4.5F, -4.5F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 488
		attachmentModel[108].setRotationPoint(26F, 3.5F, -5.5F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 489
		attachmentModel[109].setRotationPoint(26F, 2.5F, -6F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 490
		attachmentModel[110].setRotationPoint(40F, 4.5F, -4.5F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 491
		attachmentModel[111].setRotationPoint(40F, 3.5F, -5.5F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 492
		attachmentModel[112].setRotationPoint(40F, 2.5F, -6F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 493
		attachmentModel[113].setRotationPoint(54F, 4.5F, -4.5F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 494
		attachmentModel[114].setRotationPoint(54F, 3.5F, -5.5F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 495
		attachmentModel[115].setRotationPoint(54F, 2.5F, -6F);

		flipAll();
	}
}