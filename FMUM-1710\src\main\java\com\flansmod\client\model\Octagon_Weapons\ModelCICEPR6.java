//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICEPR6
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICEPR6 extends ModelAttachment //Same as Filename
{
	int textureX = 64;
	int textureY = 64;

	public ModelCICEPR6() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[41];
		attachmentModel[0] = new ModelRendererTurbo(this, 0, 56, textureX, textureY); // Box 748
		attachmentModel[1] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 749
		attachmentModel[2] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 750
		attachmentModel[3] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 751
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 752
		attachmentModel[5] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 753
		attachmentModel[6] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 754
		attachmentModel[7] = new ModelRendererTurbo(this, 33, 9, textureX, textureY); // Box 755
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 756
		attachmentModel[9] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 761
		attachmentModel[10] = new ModelRendererTurbo(this, 17, 17, textureX, textureY); // Box 762
		attachmentModel[11] = new ModelRendererTurbo(this, 57, 9, textureX, textureY); // Box 763
		attachmentModel[12] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 765
		attachmentModel[13] = new ModelRendererTurbo(this, 41, 17, textureX, textureY); // Box 766
		attachmentModel[14] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 767
		attachmentModel[15] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 768
		attachmentModel[16] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 769
		attachmentModel[17] = new ModelRendererTurbo(this, 9, 25, textureX, textureY); // Box 770
		attachmentModel[18] = new ModelRendererTurbo(this, 41, 25, textureX, textureY); // Box 772
		attachmentModel[19] = new ModelRendererTurbo(this, 57, 25, textureX, textureY); // Box 774
		attachmentModel[20] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 775
		attachmentModel[21] = new ModelRendererTurbo(this, 17, 33, textureX, textureY); // Box 776
		attachmentModel[22] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 777
		attachmentModel[23] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 778
		attachmentModel[24] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 779
		attachmentModel[25] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 780
		attachmentModel[26] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 781
		attachmentModel[27] = new ModelRendererTurbo(this, 9, 41, textureX, textureY); // Box 782
		attachmentModel[28] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 783
		attachmentModel[29] = new ModelRendererTurbo(this, 25, 41, textureX, textureY); // Box 784
		attachmentModel[30] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 785
		attachmentModel[31] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 786
		attachmentModel[32] = new ModelRendererTurbo(this, 57, 41, textureX, textureY); // Box 787
		attachmentModel[33] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 788
		attachmentModel[34] = new ModelRendererTurbo(this, 9, 49, textureX, textureY); // Box 789
		attachmentModel[35] = new ModelRendererTurbo(this, 17, 49, textureX, textureY); // Box 790
		attachmentModel[36] = new ModelRendererTurbo(this, 25, 49, textureX, textureY); // Box 791
		attachmentModel[37] = new ModelRendererTurbo(this, 28, 49, textureX, textureY); // Box 792
		attachmentModel[38] = new ModelRendererTurbo(this, 40, 49, textureX, textureY); // Box 793
		attachmentModel[39] = new ModelRendererTurbo(this, 35, 49, textureX, textureY); // Box 794
		attachmentModel[40] = new ModelRendererTurbo(this, 30, 43, textureX, textureY); // Box 795

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 26, 1, 6, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 748
		attachmentModel[0].setRotationPoint(-7F, -1F, -3F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 5, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 749
		attachmentModel[1].setRotationPoint(-7F, -1F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 750
		attachmentModel[2].setRotationPoint(5F, -2F, -3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F); // Box 751
		attachmentModel[3].setRotationPoint(11F, -1F, -3F);

		attachmentModel[4].addShapeBox(-1F, 0F, 0F, 5, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 752
		attachmentModel[4].setRotationPoint(15F, -1F, -3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 753
		attachmentModel[5].setRotationPoint(9F, -2F, -3F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 754
		attachmentModel[6].setRotationPoint(11.5F, -1F, -3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 755
		attachmentModel[7].setRotationPoint(17F, -2F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 756
		attachmentModel[8].setRotationPoint(14F, -2F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F); // Box 761
		attachmentModel[9].setRotationPoint(12.5F, -1.5F, -1F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 762
		attachmentModel[10].setRotationPoint(11.5F, -1.5F, -1.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F); // Box 763
		attachmentModel[11].setRotationPoint(10.5F, -1.5F, -1F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F); // Box 765
		attachmentModel[12].setRotationPoint(10F, -2F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F); // Box 766
		attachmentModel[13].setRotationPoint(10F, -2F, 1F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F); // Box 767
		attachmentModel[14].setRotationPoint(12F, -1F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F); // Box 768
		attachmentModel[15].setRotationPoint(13F, -2F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 769
		attachmentModel[16].setRotationPoint(13F, -2F, 1F);

		attachmentModel[17].addShapeBox(-1F, 0F, 0F, 9, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 770
		attachmentModel[17].setRotationPoint(2.5F, -1F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 772
		attachmentModel[18].setRotationPoint(-7F, -2F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F); // Box 774
		attachmentModel[19].setRotationPoint(-1.5F, -1.5F, -1F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 775
		attachmentModel[20].setRotationPoint(-0.5F, -1.5F, -1.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F); // Box 776
		attachmentModel[21].setRotationPoint(0.5F, -1.5F, -1F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F); // Box 777
		attachmentModel[22].setRotationPoint(-2F, -2F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 778
		attachmentModel[23].setRotationPoint(-3F, -2F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F); // Box 779
		attachmentModel[24].setRotationPoint(-2F, -2F, 1F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F); // Box 780
		attachmentModel[25].setRotationPoint(-1F, -1F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 781
		attachmentModel[26].setRotationPoint(-0.5F, -1F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F); // Box 782
		attachmentModel[27].setRotationPoint(0F, -1F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F); // Box 783
		attachmentModel[28].setRotationPoint(1F, -2F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 784
		attachmentModel[29].setRotationPoint(1F, -2F, 1F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 785
		attachmentModel[30].setRotationPoint(2F, -2F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 786
		attachmentModel[31].setRotationPoint(12F, -1F, 1F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 787
		attachmentModel[32].setRotationPoint(11.5F, -1F, 1F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 788
		attachmentModel[33].setRotationPoint(11F, -1F, 1F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 789
		attachmentModel[34].setRotationPoint(0F, -1F, 1F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 790
		attachmentModel[35].setRotationPoint(-0.5F, -1F, 1F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 791
		attachmentModel[36].setRotationPoint(-1F, -1F, 1F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 792
		attachmentModel[37].setRotationPoint(-8F, -2F, -2F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 793
		attachmentModel[38].setRotationPoint(19F, -2F, -2F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F); // Box 794
		attachmentModel[39].setRotationPoint(11F, 0F, -1F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F); // Box 795
		attachmentModel[40].setRotationPoint(-1F, 0F, -1F);

		flipAll();
	}
}