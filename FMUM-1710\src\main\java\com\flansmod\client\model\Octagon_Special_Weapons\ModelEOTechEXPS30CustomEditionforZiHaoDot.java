//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: EOTech EXPS3-0 Custom Edition for ZiHao - Dot
// Model Creator: 
// Created on: 26.10.2019 - 21:10:21
// Last changed on: 26.10.2019 - 21:10:21

package com.flansmod.client.model.Octagon_Special_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelDefault;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelEOTechEXPS30CustomEditionforZiHaoDot extends ModelDefault //Same as Filename
{
	int textureX = 64;
	int textureY = 64;

	public ModelEOTechEXPS30CustomEditionforZiHaoDot() //Same as Filename
	{
		defaultModel = new ModelRendererTurbo[22];
		defaultModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 96
		defaultModel[1] = new ModelRendererTurbo(this, 9, 1, textureX, textureY); // Box 97
		defaultModel[2] = new ModelRendererTurbo(this, 17, 1, textureX, textureY); // Box 98
		defaultModel[3] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 99
		defaultModel[4] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 100
		defaultModel[5] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 103
		defaultModel[6] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 105
		defaultModel[7] = new ModelRendererTurbo(this, 57, 1, textureX, textureY); // Box 106
		defaultModel[8] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 110
		defaultModel[9] = new ModelRendererTurbo(this, 9, 9, textureX, textureY); // Box 112
		defaultModel[10] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 114
		defaultModel[11] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 14
		defaultModel[12] = new ModelRendererTurbo(this, 33, 9, textureX, textureY); // Box 15
		defaultModel[13] = new ModelRendererTurbo(this, 41, 9, textureX, textureY); // Box 16
		defaultModel[14] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 19
		defaultModel[15] = new ModelRendererTurbo(this, 57, 9, textureX, textureY); // Box 20
		defaultModel[16] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 21
		defaultModel[17] = new ModelRendererTurbo(this, 9, 17, textureX, textureY); // Box 22
		defaultModel[18] = new ModelRendererTurbo(this, 17, 17, textureX, textureY); // Box 23
		defaultModel[19] = new ModelRendererTurbo(this, 25, 17, textureX, textureY); // Box 24
		defaultModel[20] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 25
		defaultModel[21] = new ModelRendererTurbo(this, 41, 17, textureX, textureY); // Box 26

		defaultModel[0].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, -0.8F, 0F, -0.2F, -0.8F); // Box 96
		defaultModel[0].setRotationPoint(0F, -0.4F, -0.899999999999999F);

		defaultModel[1].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.2F, 0F, -0.5F, -0.2F, 0F, -0.8F, -0.5F, 0F, -0.8F, -0.5F); // Box 97
		defaultModel[1].setRotationPoint(0F, -0.9F, -0.899999999999999F);

		defaultModel[2].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.8F, -0.5F, 0F, -0.8F, -0.5F, 0F, -0.5F, -0.2F, 0F, -0.5F, -0.2F); // Box 98
		defaultModel[2].setRotationPoint(0F, -0.9F, -0.1F);

		defaultModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.8F, -0.5F, 0F, -0.8F, -0.5F, 0F, -0.5F, -0.2F, 0F, -0.5F, -0.2F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 99
		defaultModel[3].setRotationPoint(0F, -0.0999999999999996F, -0.1F);

		defaultModel[4].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, -0.2F, 0F, -0.5F, -0.2F, 0F, -0.8F, -0.5F, 0F, -0.8F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 100
		defaultModel[4].setRotationPoint(0F, -0.0999999999999996F, -0.899999999999999F);

		defaultModel[5].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.8F, 0F, -0.8F, -0.8F); // Box 103
		defaultModel[5].setRotationPoint(0F, -0.0999999999999996F, -0.1F);

		defaultModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 105
		defaultModel[6].setRotationPoint(0F, -0.0999999999999996F, -0.5F);

		defaultModel[7].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F); // Box 106
		defaultModel[7].setRotationPoint(0F, -0.5F, -0.1F);

		defaultModel[8].addShapeBox(0F, -0.1F, -1.2F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.2F, 0F, -0.8F, -0.2F); // Box 110
		defaultModel[8].setRotationPoint(0F, 0F, 0F);
		defaultModel[8].rotateAngleX = -0.78539816F;

		defaultModel[9].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.8F, 0F, -0.8F, -0.8F); // Box 112
		defaultModel[9].setRotationPoint(0F, 1F, -0.1F);

		defaultModel[10].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F); // Box 114
		defaultModel[10].setRotationPoint(0F, 0F, -0.1F);

		defaultModel[11].addShapeBox(0F, 0.4F, -0.6F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2F, 0F, 0F, 0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0.2F, 0F, -0.8F, 0.2F); // Box 14
		defaultModel[11].setRotationPoint(0F, 0F, 0F);

		defaultModel[12].addShapeBox(0F, 0.4F, -0.6F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2F, 0F, 0F, 0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0.2F, 0F, -0.8F, 0.2F); // Box 15
		defaultModel[12].setRotationPoint(0F, 0F, 0F);
		defaultModel[12].rotateAngleX = -2.0943951F;

		defaultModel[13].addShapeBox(0F, 0.4F, -0.6F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2F, 0F, 0F, 0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0.2F, 0F, -0.8F, 0.2F); // Box 16
		defaultModel[13].setRotationPoint(0F, 0F, 0F);
		defaultModel[13].rotateAngleX = -4.1887902F;

		defaultModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2F, 0F, 0F, 0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0.2F, 0F, -0.8F, 0.2F); // Box 19
		defaultModel[14].setRotationPoint(0F, 0F, 0F);
		defaultModel[14].rotateAngleX = -0.78539816F;

		defaultModel[15].addShapeBox(0F, -0.2F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2F, 0F, 0F, 0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0.2F, 0F, -0.8F, 0.2F); // Box 20
		defaultModel[15].setRotationPoint(0F, 0F, 0F);
		defaultModel[15].rotateAngleX = -2.35619449F;

		defaultModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.8F, 0F, -0.8F, -0.8F); // Box 21
		defaultModel[16].setRotationPoint(0F, 1.4F, -0.1F);

		defaultModel[17].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.8F, 0F, -0.8F, -0.8F); // Box 22
		defaultModel[17].setRotationPoint(0F, 1.9F, -0.1F);

		defaultModel[18].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, -0.8F, 0F, -0.2F, -0.8F); // Box 23
		defaultModel[18].setRotationPoint(0F, -0.4F, 0.699999999999999F);

		defaultModel[19].addShapeBox(0F, -0.1F, -1.2F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.2F, 0F, -0.8F, -0.2F); // Box 24
		defaultModel[19].setRotationPoint(0F, 0F, 0F);
		defaultModel[19].rotateAngleX = 0.78539816F;

		defaultModel[20].addShapeBox(0F, -0.1F, -1.2F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.2F, 0F, -0.8F, -0.2F); // Box 25
		defaultModel[20].setRotationPoint(0F, 0F, 0F);
		defaultModel[20].rotateAngleX = 2.35619449F;

		defaultModel[21].addShapeBox(0F, -0.1F, -1.2F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.2F, 0F, -0.8F, -0.2F); // Box 26
		defaultModel[21].setRotationPoint(0F, 0F, 0F);
		defaultModel[21].rotateAngleX = -2.35619449F;

		flipAll();
	}
}