//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: HK416UpperReceiver
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelHK416UpperReceiver extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelHK416UpperReceiver() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[84];
		attachmentModel[0] = new ModelRendererTurbo(this, 329, 9, textureX, textureY); // Box 40
		attachmentModel[1] = new ModelRendererTurbo(this, 473, 9, textureX, textureY); // Box 54
		attachmentModel[2] = new ModelRendererTurbo(this, 281, 17, textureX, textureY); // Box 55
		attachmentModel[3] = new ModelRendererTurbo(this, 409, 17, textureX, textureY); // Box 57
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 70
		attachmentModel[5] = new ModelRendererTurbo(this, 265, 25, textureX, textureY); // Box 81
		attachmentModel[6] = new ModelRendererTurbo(this, 417, 25, textureX, textureY); // Box 87
		attachmentModel[7] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 120
		attachmentModel[8] = new ModelRendererTurbo(this, 305, 41, textureX, textureY); // Box 121
		attachmentModel[9] = new ModelRendererTurbo(this, 281, 25, textureX, textureY); // Box 122
		attachmentModel[10] = new ModelRendererTurbo(this, 433, 25, textureX, textureY); // Box 124
		attachmentModel[11] = new ModelRendererTurbo(this, 209, 49, textureX, textureY); // Box 126
		attachmentModel[12] = new ModelRendererTurbo(this, 377, 49, textureX, textureY); // Box 135
		attachmentModel[13] = new ModelRendererTurbo(this, 409, 49, textureX, textureY); // Box 136
		attachmentModel[14] = new ModelRendererTurbo(this, 465, 17, textureX, textureY); // Box 137
		attachmentModel[15] = new ModelRendererTurbo(this, 153, 55, textureX, textureY); // Box 140
		attachmentModel[16] = new ModelRendererTurbo(this, 385, 41, textureX, textureY); // Box 189
		attachmentModel[17] = new ModelRendererTurbo(this, 465, 73, textureX, textureY); // Box 190
		attachmentModel[18] = new ModelRendererTurbo(this, 489, 73, textureX, textureY); // Box 191
		attachmentModel[19] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 192
		attachmentModel[20] = new ModelRendererTurbo(this, 25, 81, textureX, textureY); // Box 193
		attachmentModel[21] = new ModelRendererTurbo(this, 49, 81, textureX, textureY); // Box 194
		attachmentModel[22] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 195
		attachmentModel[23] = new ModelRendererTurbo(this, 97, 81, textureX, textureY); // Box 196
		attachmentModel[24] = new ModelRendererTurbo(this, 121, 81, textureX, textureY); // Box 197
		attachmentModel[25] = new ModelRendererTurbo(this, 145, 81, textureX, textureY); // Box 198
		attachmentModel[26] = new ModelRendererTurbo(this, 169, 81, textureX, textureY); // Box 199
		attachmentModel[27] = new ModelRendererTurbo(this, 201, 89, textureX, textureY); // Box 342
		attachmentModel[28] = new ModelRendererTurbo(this, 89, 49, textureX, textureY); // Box 350
		attachmentModel[29] = new ModelRendererTurbo(this, 409, 57, textureX, textureY); // Box 351
		attachmentModel[30] = new ModelRendererTurbo(this, 330, 50, textureX, textureY); // Box 389
		attachmentModel[31] = new ModelRendererTurbo(this, 193, 97, textureX, textureY); // Box 396
		attachmentModel[32] = new ModelRendererTurbo(this, 257, 65, textureX, textureY); // Box 397
		attachmentModel[33] = new ModelRendererTurbo(this, 393, 65, textureX, textureY); // Box 395
		attachmentModel[34] = new ModelRendererTurbo(this, 417, 65, textureX, textureY); // Box 401
		attachmentModel[35] = new ModelRendererTurbo(this, 329, 65, textureX, textureY); // Box 403
		attachmentModel[36] = new ModelRendererTurbo(this, 433, 97, textureX, textureY); // Box 531
		attachmentModel[37] = new ModelRendererTurbo(this, 377, 65, textureX, textureY); // Box 532
		attachmentModel[38] = new ModelRendererTurbo(this, 449, 97, textureX, textureY); // Box 533
		attachmentModel[39] = new ModelRendererTurbo(this, 449, 65, textureX, textureY); // Box 534
		attachmentModel[40] = new ModelRendererTurbo(this, 473, 65, textureX, textureY); // Box 535
		attachmentModel[41] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 536
		attachmentModel[42] = new ModelRendererTurbo(this, 241, 73, textureX, textureY); // Box 551
		attachmentModel[43] = new ModelRendererTurbo(this, 161, 105, textureX, textureY); // Box 552
		attachmentModel[44] = new ModelRendererTurbo(this, 337, 105, textureX, textureY); // Box 487
		attachmentModel[45] = new ModelRendererTurbo(this, 497, 97, textureX, textureY); // Box 488
		attachmentModel[46] = new ModelRendererTurbo(this, 449, 105, textureX, textureY); // Box 489
		attachmentModel[47] = new ModelRendererTurbo(this, 281, 137, textureX, textureY); // Box 467
		attachmentModel[48] = new ModelRendererTurbo(this, 377, 137, textureX, textureY); // Box 472
		attachmentModel[49] = new ModelRendererTurbo(this, 425, 113, textureX, textureY); // Box 473
		attachmentModel[50] = new ModelRendererTurbo(this, 433, 121, textureX, textureY); // Box 475
		attachmentModel[51] = new ModelRendererTurbo(this, 1, 145, textureX, textureY); // Box 477
		attachmentModel[52] = new ModelRendererTurbo(this, 473, 129, textureX, textureY); // Box 531
		attachmentModel[53] = new ModelRendererTurbo(this, 489, 129, textureX, textureY); // Box 532
		attachmentModel[54] = new ModelRendererTurbo(this, 249, 145, textureX, textureY); // Box 534
		attachmentModel[55] = new ModelRendererTurbo(this, 313, 145, textureX, textureY); // Box 535
		attachmentModel[56] = new ModelRendererTurbo(this, 361, 145, textureX, textureY); // Box 540
		attachmentModel[57] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 541
		attachmentModel[58] = new ModelRendererTurbo(this, 57, 153, textureX, textureY); // Box 542
		attachmentModel[59] = new ModelRendererTurbo(this, 113, 153, textureX, textureY); // Box 543
		attachmentModel[60] = new ModelRendererTurbo(this, 465, 145, textureX, textureY); // Box 544
		attachmentModel[61] = new ModelRendererTurbo(this, 41, 113, textureX, textureY); // Box 545
		attachmentModel[62] = new ModelRendererTurbo(this, 361, 113, textureX, textureY); // Box 546
		attachmentModel[63] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 547
		attachmentModel[64] = new ModelRendererTurbo(this, 169, 153, textureX, textureY); // Box 548
		attachmentModel[65] = new ModelRendererTurbo(this, 81, 113, textureX, textureY); // Box 554
		attachmentModel[66] = new ModelRendererTurbo(this, 481, 113, textureX, textureY); // Box 555
		attachmentModel[67] = new ModelRendererTurbo(this, 505, 137, textureX, textureY); // Box 556
		attachmentModel[68] = new ModelRendererTurbo(this, 489, 145, textureX, textureY); // Box 557
		attachmentModel[69] = new ModelRendererTurbo(this, 497, 145, textureX, textureY); // Box 558
		attachmentModel[70] = new ModelRendererTurbo(this, 377, 153, textureX, textureY); // Box 561
		attachmentModel[71] = new ModelRendererTurbo(this, 393, 153, textureX, textureY); // Box 562
		attachmentModel[72] = new ModelRendererTurbo(this, 505, 145, textureX, textureY); // Box 563
		attachmentModel[73] = new ModelRendererTurbo(this, 321, 161, textureX, textureY); // Box 586
		attachmentModel[74] = new ModelRendererTurbo(this, 25, 185, textureX, textureY); // Box 671
		attachmentModel[75] = new ModelRendererTurbo(this, 505, 177, textureX, textureY); // Box 557
		attachmentModel[76] = new ModelRendererTurbo(this, 1, 185, textureX, textureY); // Box 558
		attachmentModel[77] = new ModelRendererTurbo(this, 65, 185, textureX, textureY); // Box 561
		attachmentModel[78] = new ModelRendererTurbo(this, 73, 185, textureX, textureY); // Box 562
		attachmentModel[79] = new ModelRendererTurbo(this, 465, 201, textureX, textureY); // Box 651
		attachmentModel[80] = new ModelRendererTurbo(this, 473, 201, textureX, textureY); // Box 652
		attachmentModel[81] = new ModelRendererTurbo(this, 481, 201, textureX, textureY); // Box 653
		attachmentModel[82] = new ModelRendererTurbo(this, 489, 201, textureX, textureY); // Box 654
		attachmentModel[83] = new ModelRendererTurbo(this, 1, 209, textureX, textureY); // Box 655

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 47, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 40
		attachmentModel[0].setRotationPoint(3F, -15.5F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 7, 1, 6, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 54
		attachmentModel[1].setRotationPoint(-5F, -11.5F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 61, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 55
		attachmentModel[2].setRotationPoint(-5F, -8.5F, 4F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 27, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 57
		attachmentModel[3].setRotationPoint(-5F, -9.5F, -4F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 27, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 70
		attachmentModel[4].setRotationPoint(23F, -3.5F, 3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 3, 4, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 81
		attachmentModel[5].setRotationPoint(-1.5F, -2.5F, -2.5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 5, 3, 2, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 1F, -1F, 0F, 1F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F); // Box 87
		attachmentModel[6].setRotationPoint(36F, -11.5F, 3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 28, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 120
		attachmentModel[7].setRotationPoint(-5F, -8.5F, -5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 9, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 121
		attachmentModel[8].setRotationPoint(47F, -8.5F, -5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -0.6667F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 1F); // Box 122
		attachmentModel[9].setRotationPoint(47F, -11.5F, -5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 124
		attachmentModel[10].setRotationPoint(47F, -4.5F, -5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 26, 2, 1, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 126
		attachmentModel[11].setRotationPoint(-3F, -4.5F, -5F);

		attachmentModel[12].addShapeBox(0F, 0F, 1F, 12, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -8F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -8F, 0F, 0F, 0F, 0F, 0.5F); // Box 135
		attachmentModel[12].setRotationPoint(-2F, -8.5F, -9F);
		attachmentModel[12].rotateAngleY = 0.26179939F;

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 16, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 136
		attachmentModel[13].setRotationPoint(-6F, -7.5F, -9F);
		attachmentModel[13].rotateAngleY = 0.26179939F;

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 137
		attachmentModel[14].setRotationPoint(-6F, -7.5F, -10F);
		attachmentModel[14].rotateAngleY = 0.26179939F;

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 53, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 140
		attachmentModel[15].setRotationPoint(-3F, -15.5F, -2F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 189
		attachmentModel[16].setRotationPoint(47F, -17.5F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 190
		attachmentModel[17].setRotationPoint(43F, -17.5F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 191
		attachmentModel[18].setRotationPoint(39F, -17.5F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 192
		attachmentModel[19].setRotationPoint(35F, -17.5F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 193
		attachmentModel[20].setRotationPoint(31F, -17.5F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 194
		attachmentModel[21].setRotationPoint(27F, -17.5F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 195
		attachmentModel[22].setRotationPoint(23F, -17.5F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 196
		attachmentModel[23].setRotationPoint(19F, -17.5F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 197
		attachmentModel[24].setRotationPoint(15F, -17.5F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 198
		attachmentModel[25].setRotationPoint(11F, -17.5F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 4, 1, 6, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 199
		attachmentModel[26].setRotationPoint(5F, -17.5F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 16, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, 0F, 1F, 0F); // Box 342
		attachmentModel[27].setRotationPoint(-2F, -7.5F, -9F);
		attachmentModel[27].rotateAngleY = 0.26179939F;

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 350
		attachmentModel[28].setRotationPoint(-6F, -8.5F, -10F);
		attachmentModel[28].rotateAngleY = 0.26179939F;

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 351
		attachmentModel[29].setRotationPoint(-6F, -5.5F, -10F);
		attachmentModel[29].rotateAngleY = 0.26179939F;

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0.5F, -1F, 0F, 0.5F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 0F); // Box 389
		attachmentModel[30].setRotationPoint(-4F, -15.5F, -2F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 47, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 396
		attachmentModel[31].setRotationPoint(3F, -15.5F, -3F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 397
		attachmentModel[32].setRotationPoint(1F, -15.5F, 2F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 395
		attachmentModel[33].setRotationPoint(30F, -2.5F, 3.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 401
		attachmentModel[34].setRotationPoint(47F, -3.5F, 4F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 403
		attachmentModel[35].setRotationPoint(47F, -4F, -6F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 3, 3, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 531
		attachmentModel[36].setRotationPoint(47F, -2.5F, -3F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 532
		attachmentModel[37].setRotationPoint(46F, -5.5F, -5F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 24, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.3333F, 0F, 0F, 1.3333F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 533
		attachmentModel[38].setRotationPoint(23F, -11.5F, -3F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.3333F, 0F, 0F, 0.3333F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, -1F, 0F, 1F, -1F, 0F, -1F, 1F, 0F, -1F); // Box 534
		attachmentModel[39].setRotationPoint(23F, -9.5F, -4F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.3333F, 0F, 0F, 0.3333F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, -1F, 0F, -1F); // Box 535
		attachmentModel[40].setRotationPoint(46F, -9.5F, -4F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 28, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 536
		attachmentModel[41].setRotationPoint(-5F, -6.5F, -5F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 551
		attachmentModel[42].setRotationPoint(48F, -11.5F, -3F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 8, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 552
		attachmentModel[43].setRotationPoint(48F, -10.5F, -3F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 6, 1, 5, 0F, -1F, 0F, -0.25F, -4F, 0F, -0.25F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -4F, 0F, 0F, 0F, 1F, -1F, 0F, 1F, -1F); // Box 487
		attachmentModel[44].setRotationPoint(17F, -8.5F, -9F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 3, 5, 0F, 3F, 0F, -0.75F, -3F, 0F, -0.75F, 1F, 1F, 1F, 4F, 0F, 1F, 3F, -1F, -0.25F, -3F, -1F, -0.25F, 1F, -1F, -1F, 4F, -1F, 0F); // Box 488
		attachmentModel[45].setRotationPoint(21F, -10.5F, -9F);

		attachmentModel[46].addShapeBox(0F, 1F, 0F, 6, 1, 5, 0F, -1F, 0F, 0F, -4F, 0F, 0F, -0.5F, 1F, 0F, 0F, 1F, 0F, -1F, 0F, 0.25F, -4F, 0F, 0.25F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 489
		attachmentModel[46].setRotationPoint(17F, -12.5F, -8F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 43, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 467
		attachmentModel[47].setRotationPoint(5F, -16.5F, -3F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 54, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 472
		attachmentModel[48].setRotationPoint(-4F, -5.5F, 4F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 26, 2, 1, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F); // Box 473
		attachmentModel[49].setRotationPoint(-3F, -4.5F, 4F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 23, 2, 1, 0F, 3F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 3F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 475
		attachmentModel[50].setRotationPoint(0F, -3.5F, 2.5F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 61, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 477
		attachmentModel[51].setRotationPoint(-5F, -10.5F, 2F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 7, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 531
		attachmentModel[52].setRotationPoint(49F, -11.5F, -2F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 6, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 532
		attachmentModel[53].setRotationPoint(50F, -2.5F, -2F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 27, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 534
		attachmentModel[54].setRotationPoint(23F, -4.5F, 4F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 535
		attachmentModel[55].setRotationPoint(50F, -4.5F, 2F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 50, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 540
		attachmentModel[56].setRotationPoint(-4F, -5.5F, -5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 24, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 541
		attachmentModel[57].setRotationPoint(23F, -3.5F, -4F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 23, 2, 1, 0F, 3F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 3F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 542
		attachmentModel[58].setRotationPoint(0F, -3.5F, -3.5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 24, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 543
		attachmentModel[59].setRotationPoint(23F, -4.5F, -5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 9, 2, 1, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 544
		attachmentModel[60].setRotationPoint(47F, -4.5F, -3F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 545
		attachmentModel[61].setRotationPoint(2F, -11.5F, -3F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 546
		attachmentModel[62].setRotationPoint(2F, -15.5F, -3F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 547
		attachmentModel[63].setRotationPoint(1F, -15.5F, -3F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 20, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 548
		attachmentModel[64].setRotationPoint(3F, -11.5F, -3F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 554
		attachmentModel[65].setRotationPoint(49F, -13F, -2F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 555
		attachmentModel[66].setRotationPoint(22F, -4F, -6F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 556
		attachmentModel[67].setRotationPoint(22F, -4.5F, -5F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 557
		attachmentModel[68].setRotationPoint(2F, -14.5F, 2F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 558
		attachmentModel[69].setRotationPoint(2F, -14.5F, -3F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 7, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 561
		attachmentModel[70].setRotationPoint(49F, -11.5F, 1F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 7, 1, 5, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 562
		attachmentModel[71].setRotationPoint(49F, -11.5F, -2.5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 563
		attachmentModel[72].setRotationPoint(49F, -12F, -2F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 586
		attachmentModel[73].setRotationPoint(48F, 0.5F, -3F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 671
		attachmentModel[74].setRotationPoint(50F, -5.5F, 4F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 557
		attachmentModel[75].setRotationPoint(49F, -17.5F, 1F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 558
		attachmentModel[76].setRotationPoint(48F, -17.5F, 1F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 561
		attachmentModel[77].setRotationPoint(49F, -17.5F, -3F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F); // Box 562
		attachmentModel[78].setRotationPoint(48F, -17.5F, -3F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 651
		attachmentModel[79].setRotationPoint(49F, -12F, 1F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 652
		attachmentModel[80].setRotationPoint(49F, -13F, 1F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 653
		attachmentModel[81].setRotationPoint(49F, -14F, -2F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 654
		attachmentModel[82].setRotationPoint(49F, -14F, 1F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 28, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 655
		attachmentModel[83].setRotationPoint(-5F, -10.5F, -3F);

		flipAll();
	}
}