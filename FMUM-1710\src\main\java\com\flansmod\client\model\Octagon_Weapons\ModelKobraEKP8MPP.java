//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: Kobra EKP-8M-PP
// Model Creator: 
// Created on: 27.10.2019 - 13:16:05
// Last changed on: 27.10.2019 - 13:16:05

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAtSight;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.Vector3f;

public class ModelKobraEKP8MPP extends ModelAtSight //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelKobraEKP8MPP() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[66];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 0
		attachmentModel[1] = new ModelRendererTurbo(this, 65, 1, textureX, textureY); // Box 1
		attachmentModel[2] = new ModelRendererTurbo(this, 81, 1, textureX, textureY); // Box 2
		attachmentModel[3] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 3
		attachmentModel[4] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 4
		attachmentModel[5] = new ModelRendererTurbo(this, 153, 1, textureX, textureY); // Box 5
		attachmentModel[6] = new ModelRendererTurbo(this, 161, 1, textureX, textureY); // Box 6
		attachmentModel[7] = new ModelRendererTurbo(this, 177, 1, textureX, textureY); // Box 7
		attachmentModel[8] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 8
		attachmentModel[9] = new ModelRendererTurbo(this, 65, 9, textureX, textureY); // Box 9
		attachmentModel[10] = new ModelRendererTurbo(this, 81, 9, textureX, textureY); // Box 10
		attachmentModel[11] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 11
		attachmentModel[12] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 12
		attachmentModel[13] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 13
		attachmentModel[14] = new ModelRendererTurbo(this, 81, 17, textureX, textureY); // Box 14
		attachmentModel[15] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 15
		attachmentModel[16] = new ModelRendererTurbo(this, 161, 17, textureX, textureY); // Box 16
		attachmentModel[17] = new ModelRendererTurbo(this, 177, 17, textureX, textureY); // Box 17
		attachmentModel[18] = new ModelRendererTurbo(this, 193, 17, textureX, textureY); // Box 18
		attachmentModel[19] = new ModelRendererTurbo(this, 209, 17, textureX, textureY); // Box 19
		attachmentModel[20] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 20
		attachmentModel[21] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 21
		attachmentModel[22] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 22
		attachmentModel[23] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 23
		attachmentModel[24] = new ModelRendererTurbo(this, 65, 25, textureX, textureY); // Box 24
		attachmentModel[25] = new ModelRendererTurbo(this, 81, 25, textureX, textureY); // Box 25
		attachmentModel[26] = new ModelRendererTurbo(this, 98, 26, textureX, textureY); // Box 26
		attachmentModel[27] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 27
		attachmentModel[28] = new ModelRendererTurbo(this, 129, 25, textureX, textureY); // Box 28
		attachmentModel[29] = new ModelRendererTurbo(this, 145, 25, textureX, textureY); // Box 29
		attachmentModel[30] = new ModelRendererTurbo(this, 161, 25, textureX, textureY); // Box 30
		attachmentModel[31] = new ModelRendererTurbo(this, 177, 25, textureX, textureY); // Box 31
		attachmentModel[32] = new ModelRendererTurbo(this, 193, 25, textureX, textureY); // Box 32
		attachmentModel[33] = new ModelRendererTurbo(this, 209, 25, textureX, textureY); // Box 33
		attachmentModel[34] = new ModelRendererTurbo(this, 225, 25, textureX, textureY); // Box 34
		attachmentModel[35] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 35
		attachmentModel[36] = new ModelRendererTurbo(this, 241, 25, textureX, textureY); // Box 36
		attachmentModel[37] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 37
		attachmentModel[38] = new ModelRendererTurbo(this, 41, 33, textureX, textureY); // Box 38
		attachmentModel[39] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 39
		attachmentModel[40] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 40
		attachmentModel[41] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 41
		attachmentModel[42] = new ModelRendererTurbo(this, 105, 17, textureX, textureY); // Box 42
		attachmentModel[43] = new ModelRendererTurbo(this, 129, 89, textureX, textureY); // Box 139
		attachmentModel[44] = new ModelRendererTurbo(this, 57, 89, textureX, textureY); // Box 140
		attachmentModel[45] = new ModelRendererTurbo(this, 137, 89, textureX, textureY); // Box 139
		attachmentModel[46] = new ModelRendererTurbo(this, 145, 89, textureX, textureY); // Box 140
		attachmentModel[47] = new ModelRendererTurbo(this, 193, 89, textureX, textureY); // Box 141
		attachmentModel[48] = new ModelRendererTurbo(this, 209, 89, textureX, textureY); // Box 142
		attachmentModel[49] = new ModelRendererTurbo(this, 225, 89, textureX, textureY); // Box 143
		attachmentModel[50] = new ModelRendererTurbo(this, 241, 89, textureX, textureY); // Box 144
		attachmentModel[51] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 145
		attachmentModel[52] = new ModelRendererTurbo(this, 17, 97, textureX, textureY); // Box 146
		attachmentModel[53] = new ModelRendererTurbo(this, 33, 97, textureX, textureY); // Box 147
		attachmentModel[54] = new ModelRendererTurbo(this, 121, 89, textureX, textureY); // Box 148
		attachmentModel[55] = new ModelRendererTurbo(this, 89, 97, textureX, textureY); // Box 149
		attachmentModel[56] = new ModelRendererTurbo(this, 121, 97, textureX, textureY); // Box 150
		attachmentModel[57] = new ModelRendererTurbo(this, 97, 97, textureX, textureY); // Box 151
		attachmentModel[58] = new ModelRendererTurbo(this, 137, 97, textureX, textureY); // Box 152
		attachmentModel[59] = new ModelRendererTurbo(this, 201, 97, textureX, textureY); // Box 153
		attachmentModel[60] = new ModelRendererTurbo(this, 217, 97, textureX, textureY); // Box 154
		attachmentModel[61] = new ModelRendererTurbo(this, 233, 97, textureX, textureY); // Box 155
		attachmentModel[62] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 156
		attachmentModel[63] = new ModelRendererTurbo(this, 17, 105, textureX, textureY); // Box 157
		attachmentModel[64] = new ModelRendererTurbo(this, 38, 102, textureX, textureY); // Box 160
		attachmentModel[65] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 67

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 4, 1, 7, 0F, 0F, -0.25F, 0F, 0.5F, -0.25F, 0F, 0.5F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 0
		attachmentModel[0].setRotationPoint(-6F, -1F, -3.5F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 1
		attachmentModel[1].setRotationPoint(-1.5F, 0F, -4.5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 16, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 2
		attachmentModel[2].setRotationPoint(-1.5F, 0F, 3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 11, 6, 10, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 3
		attachmentModel[3].setRotationPoint(-5.5F, -7F, -5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 4
		attachmentModel[4].setRotationPoint(-4.5F, -8F, 0.5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 5
		attachmentModel[5].setRotationPoint(-3.5F, -7F, -6F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 5, 1, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 6
		attachmentModel[6].setRotationPoint(0.5F, -8F, -3.5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 24, 5, 10, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 7
		attachmentModel[7].setRotationPoint(5.5F, -6F, -5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 8
		attachmentModel[8].setRotationPoint(-3.5F, -3F, -6F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 3, 1, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 9
		attachmentModel[9].setRotationPoint(-3.5F, -8F, -0.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 13, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 10
		attachmentModel[10].setRotationPoint(12.5F, -7F, -5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 13, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 11
		attachmentModel[11].setRotationPoint(12.5F, -7F, 3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 7, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 2F); // Box 12
		attachmentModel[12].setRotationPoint(5.5F, -7F, -5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 7, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 13
		attachmentModel[13].setRotationPoint(5.5F, -7F, 3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 14
		attachmentModel[14].setRotationPoint(-4.5F, -6F, -6F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 15
		attachmentModel[15].setRotationPoint(22.5F, -8F, 4F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 16
		attachmentModel[16].setRotationPoint(25.5F, -8F, 4F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 17
		attachmentModel[17].setRotationPoint(25.5F, -8F, -5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0.8571F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8571F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F); // Box 18
		attachmentModel[18].setRotationPoint(24.5F, -12F, 4F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0.8571F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8571F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F); // Box 19
		attachmentModel[19].setRotationPoint(24.5F, -12F, -5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 5, 3, 3, 0F, 0.8571F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8571F, 0F, -1F, 0F, 0F, -1F, 0F, -2F, 0F, 0.2857F, -2F, 0F); // Box 20
		attachmentModel[20].setRotationPoint(24.5F, -15F, -5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 5, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0.8571F, -3F, 0F, 0.2857F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -1F, 0.8571F, 0F, -1F); // Box 21
		attachmentModel[21].setRotationPoint(24.5F, -15F, 2F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2857F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2857F, 0F, 0F); // Box 22
		attachmentModel[22].setRotationPoint(24.5F, -15F, -2F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 23
		attachmentModel[23].setRotationPoint(-0.5F, -1F, 4F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 24
		attachmentModel[24].setRotationPoint(-0.5F, 1F, 4F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 25
		attachmentModel[25].setRotationPoint(-0.5F, 0F, 3.5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 26
		attachmentModel[26].setRotationPoint(11.5F, 0F, 4F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 27
		attachmentModel[27].setRotationPoint(11.5F, -1F, 4F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 28
		attachmentModel[28].setRotationPoint(11.5F, 1F, 4F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 29
		attachmentModel[29].setRotationPoint(-8F, -3F, 1.5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, -2F, 0F, 0.5F, 1F, 0F, 0.5F, 1F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 30
		attachmentModel[30].setRotationPoint(-8F, -6F, 1.5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 9, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 31
		attachmentModel[31].setRotationPoint(1.5F, -6.5F, -6F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 2, 1, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 32
		attachmentModel[32].setRotationPoint(-8F, -2F, -3.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 33
		attachmentModel[33].setRotationPoint(-8F, -3F, -3.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, -2F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0.5F, -2F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 34
		attachmentModel[34].setRotationPoint(-8F, -6F, -3.5F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 7, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0.5F, 0F); // Box 35
		attachmentModel[35].setRotationPoint(10.5F, -6.5F, -6F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 36
		attachmentModel[36].setRotationPoint(23F, -6F, -7F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 5, 3, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 37
		attachmentModel[37].setRotationPoint(22F, -5F, -7F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 38
		attachmentModel[38].setRotationPoint(-1.5F, 0F, -3.5F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 39
		attachmentModel[39].setRotationPoint(23F, -2F, -7F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 40
		attachmentModel[40].setRotationPoint(9.5F, 0F, -3.5F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F); // Box 41
		attachmentModel[41].setRotationPoint(22.5F, -8F, -5F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 42
		attachmentModel[42].setRotationPoint(-0.5F, -8F, 0.5F);

		attachmentModel[43].addShapeBox(-0.5F, -2F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 139
		attachmentModel[43].setRotationPoint(18F, -3.75F, -7.5F);
		attachmentModel[43].rotateAngleZ = 0.78539816F;

		attachmentModel[44].addShapeBox(-0.5F, -0.5F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.01F, 0F, 0F, 0.01F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.01F, 0F, 0F, 0.01F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 140
		attachmentModel[44].setRotationPoint(18F, -3.75F, -7.5F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 139
		attachmentModel[45].setRotationPoint(9.5F, 0F, -4.5F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 16, 1, 8, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 140
		attachmentModel[46].setRotationPoint(-1.5F, -1F, -4.5F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 3, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 141
		attachmentModel[47].setRotationPoint(17.5F, -6.5F, -6F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F); // Box 142
		attachmentModel[48].setRotationPoint(-8F, -1F, 1.25F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F); // Box 143
		attachmentModel[49].setRotationPoint(-8F, -1F, -3.25F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0.5F, 0F, -0.25F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 144
		attachmentModel[50].setRotationPoint(-8F, -1F, -1.25F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0.5F, 0F, -0.25F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 145
		attachmentModel[51].setRotationPoint(19F, -1F, -1.25F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F); // Box 146
		attachmentModel[52].setRotationPoint(19F, -1F, 1.25F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F); // Box 147
		attachmentModel[53].setRotationPoint(19F, -1F, -3.25F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F); // Box 148
		attachmentModel[54].setRotationPoint(3.5F, -5.25F, -7F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F); // Box 149
		attachmentModel[55].setRotationPoint(7.5F, -5.25F, -7F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 150
		attachmentModel[56].setRotationPoint(16F, -4.75F, -6.5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 151
		attachmentModel[57].setRotationPoint(17F, -5.75F, -6.5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 152
		attachmentModel[58].setRotationPoint(17F, -2.75F, -6.5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 153
		attachmentModel[59].setRotationPoint(22F, -5F, 4.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 154
		attachmentModel[60].setRotationPoint(23F, -6F, 4.5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 155
		attachmentModel[61].setRotationPoint(23F, -2F, 4.5F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 156
		attachmentModel[62].setRotationPoint(22F, -3F, 4.5F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 157
		attachmentModel[63].setRotationPoint(22F, -4F, 4.25F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 29, 1, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.75F, 0F, 0F, -0.75F, 0F, 0F, -0.75F, 0F, 0F, -0.75F, 0F); // Box 160
		attachmentModel[64].setRotationPoint(-8F, -1F, -3.5F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 4, 1, 7, 0F, 0.5F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0F, 0.5F, -0.25F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 67
		attachmentModel[65].setRotationPoint(15F, -1F, -3.5F);

		pupilRefCenter.set(26.5F /16F, 10F / 16F, 0F);
		reticleAt[0].set(4096F, 10F / 160F, 0F);
		reticleScale = 0.15F;

		reticleBorder = new Vector3f[6];
		reticleBorder[0] = new Vector3f(26.5F / 160F, 12F / 160F, -4F / 160F);
		reticleBorder[1] = new Vector3f(26.5F / 160F, 14F / 160F, -2F / 160F);
		reticleBorder[2] = new Vector3f(26.5F / 160F, 14F / 160F, 2F / 160F);
		reticleBorder[3] = new Vector3f(26.5F / 160F, 12F / 160F, 4F / 160F);
		reticleBorder[4] = new Vector3f(26.5F / 160F, 6F / 160F, 4F / 160F);
		reticleBorder[5] = new Vector3f(26.5F / 160F, 6F / 160F, -4F / 160F);

		flipAll();
	}
}