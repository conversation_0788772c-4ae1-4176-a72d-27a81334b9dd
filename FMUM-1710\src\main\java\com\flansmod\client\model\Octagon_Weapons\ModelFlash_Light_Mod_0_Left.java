//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2019 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: Flash Light Mod 0 Left
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelFlash_Light_Mod_0_Left extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 512;

	public ModelFlash_Light_Mod_0_Left() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[47];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 487
		attachmentModel[1] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 488
		attachmentModel[2] = new ModelRendererTurbo(this, 57, 1, textureX, textureY); // Box 489
		attachmentModel[3] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 490
		attachmentModel[4] = new ModelRendererTurbo(this, 89, 1, textureX, textureY); // Box 491
		attachmentModel[5] = new ModelRendererTurbo(this, 129, 1, textureX, textureY); // Box 492
		attachmentModel[6] = new ModelRendererTurbo(this, 169, 1, textureX, textureY); // Box 493
		attachmentModel[7] = new ModelRendererTurbo(this, 185, 1, textureX, textureY); // Box 494
		attachmentModel[8] = new ModelRendererTurbo(this, 201, 1, textureX, textureY); // Box 495
		attachmentModel[9] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 496
		attachmentModel[10] = new ModelRendererTurbo(this, 217, 1, textureX, textureY); // Box 497
		attachmentModel[11] = new ModelRendererTurbo(this, 225, 1, textureX, textureY); // Box 498
		attachmentModel[12] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 499
		attachmentModel[13] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 500
		attachmentModel[14] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 501
		attachmentModel[15] = new ModelRendererTurbo(this, 257, 1, textureX, textureY); // Box 502
		attachmentModel[16] = new ModelRendererTurbo(this, 305, 1, textureX, textureY); // Box 503
		attachmentModel[17] = new ModelRendererTurbo(this, 321, 1, textureX, textureY); // Box 504
		attachmentModel[18] = new ModelRendererTurbo(this, 337, 1, textureX, textureY); // Box 505
		attachmentModel[19] = new ModelRendererTurbo(this, 177, 1, textureX, textureY); // Box 509
		attachmentModel[20] = new ModelRendererTurbo(this, 353, 1, textureX, textureY); // Box 510
		attachmentModel[21] = new ModelRendererTurbo(this, 361, 1, textureX, textureY); // Box 511
		attachmentModel[22] = new ModelRendererTurbo(this, 193, 1, textureX, textureY); // Box 512
		attachmentModel[23] = new ModelRendererTurbo(this, 297, 1, textureX, textureY); // Box 513
		attachmentModel[24] = new ModelRendererTurbo(this, 313, 1, textureX, textureY); // Box 514
		attachmentModel[25] = new ModelRendererTurbo(this, 377, 1, textureX, textureY); // Box 515
		attachmentModel[26] = new ModelRendererTurbo(this, 385, 1, textureX, textureY); // Box 516
		attachmentModel[27] = new ModelRendererTurbo(this, 417, 1, textureX, textureY); // Box 517
		attachmentModel[28] = new ModelRendererTurbo(this, 433, 1, textureX, textureY); // Box 518
		attachmentModel[29] = new ModelRendererTurbo(this, 449, 1, textureX, textureY); // Box 519
		attachmentModel[30] = new ModelRendererTurbo(this, 465, 1, textureX, textureY); // Box 520
		attachmentModel[31] = new ModelRendererTurbo(this, 369, 1, textureX, textureY); // Box 521
		attachmentModel[32] = new ModelRendererTurbo(this, 425, 1, textureX, textureY); // Box 522
		attachmentModel[33] = new ModelRendererTurbo(this, 441, 1, textureX, textureY); // Box 523
		attachmentModel[34] = new ModelRendererTurbo(this, 481, 1, textureX, textureY); // Box 524
		attachmentModel[35] = new ModelRendererTurbo(this, 489, 1, textureX, textureY); // Box 525
		attachmentModel[36] = new ModelRendererTurbo(this, 497, 1, textureX, textureY); // Box 526
		attachmentModel[37] = new ModelRendererTurbo(this, 505, 1, textureX, textureY); // Box 527
		attachmentModel[38] = new ModelRendererTurbo(this, 457, 1, textureX, textureY); // Box 528
		attachmentModel[39] = new ModelRendererTurbo(this, 473, 1, textureX, textureY); // Box 529
		attachmentModel[40] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 530
		attachmentModel[41] = new ModelRendererTurbo(this, 33, 9, textureX, textureY); // Box 531
		attachmentModel[42] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 532
		attachmentModel[43] = new ModelRendererTurbo(this, 65, 9, textureX, textureY); // Box 533
		attachmentModel[44] = new ModelRendererTurbo(this, 169, 9, textureX, textureY); // Box 534
		attachmentModel[45] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 535
		attachmentModel[46] = new ModelRendererTurbo(this, 75, 8, textureX, textureY); // Box 46

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 10, 8, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 487
		attachmentModel[0].setRotationPoint(-5F, -4F, 0F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 10, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 488
		attachmentModel[1].setRotationPoint(-5F, -4F, -2F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 10, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 489
		attachmentModel[2].setRotationPoint(-5F, 3F, -2F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 490
		attachmentModel[3].setRotationPoint(-7F, -3.5F, 5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 16, 8, 2, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 491
		attachmentModel[4].setRotationPoint(-6F, -4F, 3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 16, 8, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F); // Box 492
		attachmentModel[5].setRotationPoint(-6F, -4F, 9F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 493
		attachmentModel[6].setRotationPoint(10F, -3.5F, 5.5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 494
		attachmentModel[7].setRotationPoint(10F, 2.5F, 5.5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 495
		attachmentModel[8].setRotationPoint(10F, -1.5F, 3.5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 496
		attachmentModel[9].setRotationPoint(10F, -1.5F, 9.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 497
		attachmentModel[10].setRotationPoint(10F, -3.5F, 8.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 498
		attachmentModel[11].setRotationPoint(10F, -3.5F, 3.5F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 499
		attachmentModel[12].setRotationPoint(10F, 1.5F, 3.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F); // Box 500
		attachmentModel[13].setRotationPoint(10F, 1.5F, 8.5F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 501
		attachmentModel[14].setRotationPoint(-7F, -2F, 3.5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 16, 8, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 502
		attachmentModel[15].setRotationPoint(-6F, -4F, 5F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 503
		attachmentModel[16].setRotationPoint(-7F, -0.5F, 5.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 504
		attachmentModel[17].setRotationPoint(-7F, -1.5F, 5.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 505
		attachmentModel[18].setRotationPoint(-7F, 0.5F, 5.5F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 0F, -1F, 1.5F, 0F, -1F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 509
		attachmentModel[19].setRotationPoint(-7F, -3F, 8F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 510
		attachmentModel[20].setRotationPoint(-7F, -2F, 9F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 511
		attachmentModel[21].setRotationPoint(-7F, 2F, 5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -1F, 1.5F, 0F, -1F, 1.5F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 512
		attachmentModel[22].setRotationPoint(-7F, -3F, 5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1.5F, 0F, -1F, 1.5F, 0F, 0.5F, -1F, 0F, 0.5F, -1F); // Box 513
		attachmentModel[23].setRotationPoint(-7F, 2F, 5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 0F, -1F, 1.5F, 0F, -1F, 1.5F); // Box 514
		attachmentModel[24].setRotationPoint(-7F, 2F, 8F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 3, 2, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 515
		attachmentModel[25].setRotationPoint(-8F, -2F, 6F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 10, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 516
		attachmentModel[26].setRotationPoint(-5F, -3F, 1F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 517
		attachmentModel[27].setRotationPoint(-2F, -5.5F, 5F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 518
		attachmentModel[28].setRotationPoint(-3F, -5.5F, 5F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 519
		attachmentModel[29].setRotationPoint(0F, -5.5F, 5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 520
		attachmentModel[30].setRotationPoint(-0.5F, -5.5F, 5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 521
		attachmentModel[31].setRotationPoint(-1.5F, -5.5F, 5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 522
		attachmentModel[32].setRotationPoint(-1.5F, -5.5F, 8F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 523
		attachmentModel[33].setRotationPoint(-1.5F, -5F, 6F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 4, 2, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F); // Box 524
		attachmentModel[34].setRotationPoint(-3F, -2F, 10.5F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 4, 2, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 525
		attachmentModel[35].setRotationPoint(0F, -2F, 10.5F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 4, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 526
		attachmentModel[36].setRotationPoint(-2F, -2F, 10.5F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 4, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 527
		attachmentModel[37].setRotationPoint(-0.5F, -2F, 10.5F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 528
		attachmentModel[38].setRotationPoint(-1.5F, -2F, 10.5F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 529
		attachmentModel[39].setRotationPoint(-1.5F, 1F, 10.5F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 530
		attachmentModel[40].setRotationPoint(-1.5F, -1F, 11F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 2, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 531
		attachmentModel[41].setRotationPoint(-2F, -5F, 9F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 2, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -2F, 0F, 0F, -2F, 0F); // Box 532
		attachmentModel[42].setRotationPoint(-2F, -5F, 2F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F); // Box 533
		attachmentModel[43].setRotationPoint(-1.5F, 4F, 0F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 534
		attachmentModel[44].setRotationPoint(-1.5F, 4F, -2F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 535
		attachmentModel[45].setRotationPoint(-1.5F, 3.5F, -1F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 46
		attachmentModel[46].setRotationPoint(9.5F, -2.5F, 4.5F);



		flipAll();
	}
}