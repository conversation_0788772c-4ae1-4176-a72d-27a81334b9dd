//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICEPR9
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICEPR9 extends ModelAttachment //Same as Filename
{
	int textureX = 128;
	int textureY = 64;

	public ModelCICEPR9() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[59];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 748
		attachmentModel[1] = new ModelRendererTurbo(this, 97, 1, textureX, textureY); // Box 749
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 750
		attachmentModel[3] = new ModelRendererTurbo(this, 89, 1, textureX, textureY); // Box 751
		attachmentModel[4] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 752
		attachmentModel[5] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 753
		attachmentModel[6] = new ModelRendererTurbo(this, 121, 1, textureX, textureY); // Box 754
		attachmentModel[7] = new ModelRendererTurbo(this, 65, 9, textureX, textureY); // Box 755
		attachmentModel[8] = new ModelRendererTurbo(this, 89, 9, textureX, textureY); // Box 756
		attachmentModel[9] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 761
		attachmentModel[10] = new ModelRendererTurbo(this, 81, 9, textureX, textureY); // Box 762
		attachmentModel[11] = new ModelRendererTurbo(this, 105, 9, textureX, textureY); // Box 763
		attachmentModel[12] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 765
		attachmentModel[13] = new ModelRendererTurbo(this, 121, 9, textureX, textureY); // Box 766
		attachmentModel[14] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 767
		attachmentModel[15] = new ModelRendererTurbo(this, 9, 17, textureX, textureY); // Box 768
		attachmentModel[16] = new ModelRendererTurbo(this, 17, 17, textureX, textureY); // Box 769
		attachmentModel[17] = new ModelRendererTurbo(this, 25, 17, textureX, textureY); // Box 770
		attachmentModel[18] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 772
		attachmentModel[19] = new ModelRendererTurbo(this, 73, 17, textureX, textureY); // Box 774
		attachmentModel[20] = new ModelRendererTurbo(this, 81, 17, textureX, textureY); // Box 775
		attachmentModel[21] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 776
		attachmentModel[22] = new ModelRendererTurbo(this, 105, 17, textureX, textureY); // Box 777
		attachmentModel[23] = new ModelRendererTurbo(this, 113, 17, textureX, textureY); // Box 778
		attachmentModel[24] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 779
		attachmentModel[25] = new ModelRendererTurbo(this, 9, 25, textureX, textureY); // Box 780
		attachmentModel[26] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 781
		attachmentModel[27] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 782
		attachmentModel[28] = new ModelRendererTurbo(this, 33, 25, textureX, textureY); // Box 783
		attachmentModel[29] = new ModelRendererTurbo(this, 41, 25, textureX, textureY); // Box 784
		attachmentModel[30] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 785
		attachmentModel[31] = new ModelRendererTurbo(this, 65, 25, textureX, textureY); // Box 786
		attachmentModel[32] = new ModelRendererTurbo(this, 73, 25, textureX, textureY); // Box 787
		attachmentModel[33] = new ModelRendererTurbo(this, 81, 25, textureX, textureY); // Box 788
		attachmentModel[34] = new ModelRendererTurbo(this, 89, 25, textureX, textureY); // Box 789
		attachmentModel[35] = new ModelRendererTurbo(this, 97, 25, textureX, textureY); // Box 790
		attachmentModel[36] = new ModelRendererTurbo(this, 105, 25, textureX, textureY); // Box 791
		attachmentModel[37] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 792
		attachmentModel[38] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 793
		attachmentModel[39] = new ModelRendererTurbo(this, 9, 33, textureX, textureY); // Box 794
		attachmentModel[40] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 795
		attachmentModel[41] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 41
		attachmentModel[42] = new ModelRendererTurbo(this, 65, 33, textureX, textureY); // Box 42
		attachmentModel[43] = new ModelRendererTurbo(this, 89, 33, textureX, textureY); // Box 43
		attachmentModel[44] = new ModelRendererTurbo(this, 121, 25, textureX, textureY); // Box 44
		attachmentModel[45] = new ModelRendererTurbo(this, 81, 33, textureX, textureY); // Box 45
		attachmentModel[46] = new ModelRendererTurbo(this, 105, 33, textureX, textureY); // Box 46
		attachmentModel[47] = new ModelRendererTurbo(this, 121, 33, textureX, textureY); // Box 47
		attachmentModel[48] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 48
		attachmentModel[49] = new ModelRendererTurbo(this, 9, 41, textureX, textureY); // Box 49
		attachmentModel[50] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 50
		attachmentModel[51] = new ModelRendererTurbo(this, 25, 41, textureX, textureY); // Box 51
		attachmentModel[52] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 52
		attachmentModel[53] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 53
		attachmentModel[54] = new ModelRendererTurbo(this, 57, 41, textureX, textureY); // Box 54
		attachmentModel[55] = new ModelRendererTurbo(this, 65, 41, textureX, textureY); // Box 55
		attachmentModel[56] = new ModelRendererTurbo(this, 73, 41, textureX, textureY); // Box 56
		attachmentModel[57] = new ModelRendererTurbo(this, 81, 41, textureX, textureY); // Box 57
		attachmentModel[58] = new ModelRendererTurbo(this, 89, 41, textureX, textureY); // Box 58

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 38, 1, 6, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 748
		attachmentModel[0].setRotationPoint(-7F, -1F, -3F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 5, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 749
		attachmentModel[1].setRotationPoint(-7F, -1F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 750
		attachmentModel[2].setRotationPoint(17F, -2F, -3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F); // Box 751
		attachmentModel[3].setRotationPoint(23F, -1F, -3F);

		attachmentModel[4].addShapeBox(-1F, 0F, 0F, 5, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 752
		attachmentModel[4].setRotationPoint(27F, -1F, -3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 753
		attachmentModel[5].setRotationPoint(21F, -2F, -3F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 754
		attachmentModel[6].setRotationPoint(23.5F, -1F, -3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 755
		attachmentModel[7].setRotationPoint(29F, -2F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 756
		attachmentModel[8].setRotationPoint(26F, -2F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F); // Box 761
		attachmentModel[9].setRotationPoint(24.5F, -1.5F, -1F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 762
		attachmentModel[10].setRotationPoint(23.5F, -1.5F, -1.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F); // Box 763
		attachmentModel[11].setRotationPoint(22.5F, -1.5F, -1F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F); // Box 765
		attachmentModel[12].setRotationPoint(22F, -2F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F); // Box 766
		attachmentModel[13].setRotationPoint(22F, -2F, 1F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F); // Box 767
		attachmentModel[14].setRotationPoint(24F, -1F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F); // Box 768
		attachmentModel[15].setRotationPoint(25F, -2F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 769
		attachmentModel[16].setRotationPoint(25F, -2F, 1F);

		attachmentModel[17].addShapeBox(-1F, 0F, 0F, 9, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 770
		attachmentModel[17].setRotationPoint(14.5F, -1F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 772
		attachmentModel[18].setRotationPoint(-7F, -2F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F); // Box 774
		attachmentModel[19].setRotationPoint(10.5F, -1.5F, -1F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 775
		attachmentModel[20].setRotationPoint(11.5F, -1.5F, -1.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F); // Box 776
		attachmentModel[21].setRotationPoint(12.5F, -1.5F, -1F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F); // Box 777
		attachmentModel[22].setRotationPoint(10F, -2F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 778
		attachmentModel[23].setRotationPoint(9F, -2F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F); // Box 779
		attachmentModel[24].setRotationPoint(10F, -2F, 1F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F); // Box 780
		attachmentModel[25].setRotationPoint(11F, -1F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 781
		attachmentModel[26].setRotationPoint(11.5F, -1F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F); // Box 782
		attachmentModel[27].setRotationPoint(12F, -1F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F); // Box 783
		attachmentModel[28].setRotationPoint(13F, -2F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 784
		attachmentModel[29].setRotationPoint(13F, -2F, 1F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 785
		attachmentModel[30].setRotationPoint(14F, -2F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 786
		attachmentModel[31].setRotationPoint(24F, -1F, 1F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 787
		attachmentModel[32].setRotationPoint(23.5F, -1F, 1F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 788
		attachmentModel[33].setRotationPoint(23F, -1F, 1F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 789
		attachmentModel[34].setRotationPoint(12F, -1F, 1F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 790
		attachmentModel[35].setRotationPoint(11.5F, -1F, 1F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 791
		attachmentModel[36].setRotationPoint(11F, -1F, 1F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 792
		attachmentModel[37].setRotationPoint(-8F, -2F, -2F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 793
		attachmentModel[38].setRotationPoint(31F, -2F, -2F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F); // Box 794
		attachmentModel[39].setRotationPoint(23F, 0F, -1F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F); // Box 795
		attachmentModel[40].setRotationPoint(11F, 0F, -1F);

		attachmentModel[41].addShapeBox(-1F, 0F, 0F, 9, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 41
		attachmentModel[41].setRotationPoint(2.5F, -1F, -3F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 42
		attachmentModel[42].setRotationPoint(5F, -2F, -3F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 43
		attachmentModel[43].setRotationPoint(2F, -2F, -3F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 44
		attachmentModel[44].setRotationPoint(1F, -2F, 1F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F); // Box 45
		attachmentModel[45].setRotationPoint(0.5F, -1.5F, -1F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 46
		attachmentModel[46].setRotationPoint(-0.5F, -1.5F, -1.5F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F); // Box 47
		attachmentModel[47].setRotationPoint(-1.5F, -1.5F, -1F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F); // Box 48
		attachmentModel[48].setRotationPoint(-2F, -2F, 1F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 49
		attachmentModel[49].setRotationPoint(-1F, -1F, 1F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 50
		attachmentModel[50].setRotationPoint(-0.5F, -1F, 1F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 51
		attachmentModel[51].setRotationPoint(0F, -1F, 1F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[52].setRotationPoint(-3F, -2F, -3F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F); // Box 53
		attachmentModel[53].setRotationPoint(-2F, -2F, -3F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 54
		attachmentModel[54].setRotationPoint(-0.5F, -1F, -3F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F); // Box 55
		attachmentModel[55].setRotationPoint(-1F, -1F, -3F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0F); // Box 56
		attachmentModel[56].setRotationPoint(1F, -2F, -3F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F); // Box 57
		attachmentModel[57].setRotationPoint(0F, -1F, -3F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 0F); // Box 58
		attachmentModel[58].setRotationPoint(-1F, 0F, -1F);

		flipAll();
	}
}