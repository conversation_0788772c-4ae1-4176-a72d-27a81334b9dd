//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: 9mm Rectangle Muzzle Brake Mod 0
// Model Creator: 
// Created on: 24.08.2019 - 09:46:57
// Last changed on: 24.08.2019 - 09:46:57

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.Vector3f;

public class Model9mmRectangleMuzzleBrakeMod0 extends ModelAttachment //Same as Filename
{
	int textureX = 64;
	int textureY = 64;

	public Model9mmRectangleMuzzleBrakeMod0() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[53];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 211
		attachmentModel[1] = new ModelRendererTurbo(this, 9, 1, textureX, textureY); // Box 212
		attachmentModel[2] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 213
		attachmentModel[3] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 215
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 216
		attachmentModel[5] = new ModelRendererTurbo(this, 9, 9, textureX, textureY); // Box 217
		attachmentModel[6] = new ModelRendererTurbo(this, 41, 9, textureX, textureY); // Box 218
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 234
		attachmentModel[8] = new ModelRendererTurbo(this, 17, 17, textureX, textureY); // Box 235
		attachmentModel[9] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 236
		attachmentModel[10] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 237
		attachmentModel[11] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 239
		attachmentModel[12] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 242
		attachmentModel[13] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 243
		attachmentModel[14] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 244
		attachmentModel[15] = new ModelRendererTurbo(this, 12, 27, textureX, textureY); // Box 31
		attachmentModel[16] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 31
		attachmentModel[17] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 32
		attachmentModel[18] = new ModelRendererTurbo(this, 17, 33, textureX, textureY); // Box 33
		attachmentModel[19] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 34
		attachmentModel[20] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 35
		attachmentModel[21] = new ModelRendererTurbo(this, 25, 59, textureX, textureY); // Box 36
		attachmentModel[22] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 37
		attachmentModel[23] = new ModelRendererTurbo(this, 41, 25, textureX, textureY); // Box 38
		attachmentModel[24] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 39
		attachmentModel[25] = new ModelRendererTurbo(this, 1, 38, textureX, textureY); // Box 40
		attachmentModel[26] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 41
		attachmentModel[27] = new ModelRendererTurbo(this, 25, 41, textureX, textureY); // Box 42
		attachmentModel[28] = new ModelRendererTurbo(this, 10, 43, textureX, textureY); // Box 43
		attachmentModel[29] = new ModelRendererTurbo(this, 41, 41, textureX, textureY); // Box 44
		attachmentModel[30] = new ModelRendererTurbo(this, 10, 39, textureX, textureY); // Box 45
		attachmentModel[31] = new ModelRendererTurbo(this, 57, 41, textureX, textureY); // Box 46
		attachmentModel[32] = new ModelRendererTurbo(this, 1, 51, textureX, textureY); // Box 47
		attachmentModel[33] = new ModelRendererTurbo(this, 9, 51, textureX, textureY); // Box 48
		attachmentModel[34] = new ModelRendererTurbo(this, 17, 51, textureX, textureY); // Box 49
		attachmentModel[35] = new ModelRendererTurbo(this, 25, 49, textureX, textureY); // Box 50
		attachmentModel[36] = new ModelRendererTurbo(this, 41, 49, textureX, textureY); // Box 51
		attachmentModel[37] = new ModelRendererTurbo(this, 57, 49, textureX, textureY); // Box 52
		attachmentModel[38] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 53
		attachmentModel[39] = new ModelRendererTurbo(this, 9, 57, textureX, textureY); // Box 54
		attachmentModel[40] = new ModelRendererTurbo(this, 17, 57, textureX, textureY); // Box 55
		attachmentModel[41] = new ModelRendererTurbo(this, 57, 57, textureX, textureY); // Box 56
		attachmentModel[42] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 57
		attachmentModel[43] = new ModelRendererTurbo(this, 1, 13, textureX, textureY); // Box 58
		attachmentModel[44] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 59
		attachmentModel[45] = new ModelRendererTurbo(this, 34, 22, textureX, textureY); // Box 60
		attachmentModel[46] = new ModelRendererTurbo(this, 27, 22, textureX, textureY); // Box 61
		attachmentModel[47] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 62
		attachmentModel[48] = new ModelRendererTurbo(this, 24, 50, textureX, textureY); // Box 63
		attachmentModel[49] = new ModelRendererTurbo(this, 34, 26, textureX, textureY); // Box 64
		attachmentModel[50] = new ModelRendererTurbo(this, 27, 26, textureX, textureY); // Box 65
		attachmentModel[51] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 66
		attachmentModel[52] = new ModelRendererTurbo(this, 1, 27, textureX, textureY); // Box 67

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 211
		attachmentModel[0].setRotationPoint(0F, -1F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 212
		attachmentModel[1].setRotationPoint(0F, 2F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 213
		attachmentModel[2].setRotationPoint(0F, -1F, -3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 215
		attachmentModel[3].setRotationPoint(0F, -3F, 1F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 216
		attachmentModel[4].setRotationPoint(0F, -3F, -1F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 13, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 217
		attachmentModel[5].setRotationPoint(0F, 3F, -2F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 218
		attachmentModel[6].setRotationPoint(0F, 1F, 2F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 4, 2, 6, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 234
		attachmentModel[7].setRotationPoint(9F, 1F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 235
		attachmentModel[8].setRotationPoint(9F, -1F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 236
		attachmentModel[9].setRotationPoint(9F, -3F, -1F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 237
		attachmentModel[10].setRotationPoint(9F, -1F, 1F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 239
		attachmentModel[11].setRotationPoint(8F, 1F, -2F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 242
		attachmentModel[12].setRotationPoint(4F, 2F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 243
		attachmentModel[13].setRotationPoint(6F, 2F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 244
		attachmentModel[14].setRotationPoint(8F, 2F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 31
		attachmentModel[15].setRotationPoint(0F, 1F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 4, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 31
		attachmentModel[16].setRotationPoint(9F, -2F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 4, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 32
		attachmentModel[17].setRotationPoint(9F, -2F, 1F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 4, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 33
		attachmentModel[18].setRotationPoint(9F, 1F, 1F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 4, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 34
		attachmentModel[19].setRotationPoint(9F, 1F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 35
		attachmentModel[20].setRotationPoint(8F, 1F, -2F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 36
		attachmentModel[21].setRotationPoint(8F, 1F, 1F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 37
		attachmentModel[22].setRotationPoint(6F, 1F, 1F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 38
		attachmentModel[23].setRotationPoint(6F, 1F, -2F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 39
		attachmentModel[24].setRotationPoint(6F, 1F, -2F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 40
		attachmentModel[25].setRotationPoint(4F, 1F, 1F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 41
		attachmentModel[26].setRotationPoint(4F, 1F, -2F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 42
		attachmentModel[27].setRotationPoint(4F, 1F, -2F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 43
		attachmentModel[28].setRotationPoint(7F, 1F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 44
		attachmentModel[29].setRotationPoint(7F, -1F, -3F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 45
		attachmentModel[30].setRotationPoint(7F, -2F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 46
		attachmentModel[31].setRotationPoint(7F, -3F, -1F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 47
		attachmentModel[32].setRotationPoint(7F, -2F, 1F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 48
		attachmentModel[33].setRotationPoint(7F, -1F, 1F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 49
		attachmentModel[34].setRotationPoint(7F, 1F, 1F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 2, 6, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 50
		attachmentModel[35].setRotationPoint(7F, 1F, -3F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 2, 6, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 51
		attachmentModel[36].setRotationPoint(5F, 1F, -3F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 52
		attachmentModel[37].setRotationPoint(5F, 1F, 1F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 53
		attachmentModel[38].setRotationPoint(5F, -1F, 1F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 54
		attachmentModel[39].setRotationPoint(5F, -2F, 1F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 55
		attachmentModel[40].setRotationPoint(5F, -3F, -1F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 56
		attachmentModel[41].setRotationPoint(5F, -2F, -3F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 57
		attachmentModel[42].setRotationPoint(5F, -1F, -3F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 58
		attachmentModel[43].setRotationPoint(5F, 1F, -3F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 1, 2, 6, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 59
		attachmentModel[44].setRotationPoint(3F, 1F, -3F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 60
		attachmentModel[45].setRotationPoint(3F, 1F, -3F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 61
		attachmentModel[46].setRotationPoint(3F, 1F, 1F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 62
		attachmentModel[47].setRotationPoint(3F, -1F, 1F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 63
		attachmentModel[48].setRotationPoint(3F, -1F, -3F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 64
		attachmentModel[49].setRotationPoint(3F, -2F, -3F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 65
		attachmentModel[50].setRotationPoint(3F, -2F, 1F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 66
		attachmentModel[51].setRotationPoint(3F, -3F, -1F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 67
		attachmentModel[52].setRotationPoint(0F, -3F, -2F);

		muzzleFlashPoint = new Vector3f(4F / 16F, 0F, 0F);

		flipAll();
	}
}