//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: HK416Handguard
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelHK416Handguard extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelHK416Handguard() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[154];
		attachmentModel[0] = new ModelRendererTurbo(this, 161, 9, textureX, textureY); // Box 9
		attachmentModel[1] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 41
		attachmentModel[2] = new ModelRendererTurbo(this, 433, 9, textureX, textureY); // Box 49
		attachmentModel[3] = new ModelRendererTurbo(this, 137, 17, textureX, textureY); // Box 52
		attachmentModel[4] = new ModelRendererTurbo(this, 457, 49, textureX, textureY); // Box 158
		attachmentModel[5] = new ModelRendererTurbo(this, 265, 41, textureX, textureY); // Box 159
		attachmentModel[6] = new ModelRendererTurbo(this, 57, 57, textureX, textureY); // Box 160
		attachmentModel[7] = new ModelRendererTurbo(this, 81, 57, textureX, textureY); // Box 161
		attachmentModel[8] = new ModelRendererTurbo(this, 393, 57, textureX, textureY); // Box 162
		attachmentModel[9] = new ModelRendererTurbo(this, 417, 57, textureX, textureY); // Box 163
		attachmentModel[10] = new ModelRendererTurbo(this, 441, 57, textureX, textureY); // Box 164
		attachmentModel[11] = new ModelRendererTurbo(this, 473, 57, textureX, textureY); // Box 165
		attachmentModel[12] = new ModelRendererTurbo(this, 257, 65, textureX, textureY); // Box 166
		attachmentModel[13] = new ModelRendererTurbo(this, 281, 65, textureX, textureY); // Box 167
		attachmentModel[14] = new ModelRendererTurbo(this, 305, 65, textureX, textureY); // Box 168
		attachmentModel[15] = new ModelRendererTurbo(this, 329, 65, textureX, textureY); // Box 169
		attachmentModel[16] = new ModelRendererTurbo(this, 353, 65, textureX, textureY); // Box 170
		attachmentModel[17] = new ModelRendererTurbo(this, 377, 65, textureX, textureY); // Box 171
		attachmentModel[18] = new ModelRendererTurbo(this, 401, 65, textureX, textureY); // Box 172
		attachmentModel[19] = new ModelRendererTurbo(this, 425, 65, textureX, textureY); // Box 173
		attachmentModel[20] = new ModelRendererTurbo(this, 449, 65, textureX, textureY); // Box 174
		attachmentModel[21] = new ModelRendererTurbo(this, 473, 65, textureX, textureY); // Box 175
		attachmentModel[22] = new ModelRendererTurbo(this, 129, 73, textureX, textureY); // Box 176
		attachmentModel[23] = new ModelRendererTurbo(this, 153, 73, textureX, textureY); // Box 177
		attachmentModel[24] = new ModelRendererTurbo(this, 177, 73, textureX, textureY); // Box 178
		attachmentModel[25] = new ModelRendererTurbo(this, 201, 73, textureX, textureY); // Box 179
		attachmentModel[26] = new ModelRendererTurbo(this, 225, 73, textureX, textureY); // Box 180
		attachmentModel[27] = new ModelRendererTurbo(this, 249, 73, textureX, textureY); // Box 181
		attachmentModel[28] = new ModelRendererTurbo(this, 273, 73, textureX, textureY); // Box 182
		attachmentModel[29] = new ModelRendererTurbo(this, 297, 73, textureX, textureY); // Box 183
		attachmentModel[30] = new ModelRendererTurbo(this, 321, 73, textureX, textureY); // Box 184
		attachmentModel[31] = new ModelRendererTurbo(this, 345, 73, textureX, textureY); // Box 185
		attachmentModel[32] = new ModelRendererTurbo(this, 369, 73, textureX, textureY); // Box 186
		attachmentModel[33] = new ModelRendererTurbo(this, 393, 73, textureX, textureY); // Box 187
		attachmentModel[34] = new ModelRendererTurbo(this, 417, 73, textureX, textureY); // Box 188
		attachmentModel[35] = new ModelRendererTurbo(this, 425, 33, textureX, textureY); // Box 202
		attachmentModel[36] = new ModelRendererTurbo(this, 201, 41, textureX, textureY); // Box 203
		attachmentModel[37] = new ModelRendererTurbo(this, 505, 41, textureX, textureY); // Box 204
		attachmentModel[38] = new ModelRendererTurbo(this, 129, 49, textureX, textureY); // Box 205
		attachmentModel[39] = new ModelRendererTurbo(this, 505, 49, textureX, textureY); // Box 206
		attachmentModel[40] = new ModelRendererTurbo(this, 497, 57, textureX, textureY); // Box 207
		attachmentModel[41] = new ModelRendererTurbo(this, 505, 57, textureX, textureY); // Box 208
		attachmentModel[42] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 209
		attachmentModel[43] = new ModelRendererTurbo(this, 497, 65, textureX, textureY); // Box 210
		attachmentModel[44] = new ModelRendererTurbo(this, 505, 65, textureX, textureY); // Box 211
		attachmentModel[45] = new ModelRendererTurbo(this, 441, 73, textureX, textureY); // Box 212
		attachmentModel[46] = new ModelRendererTurbo(this, 193, 81, textureX, textureY); // Box 213
		attachmentModel[47] = new ModelRendererTurbo(this, 201, 81, textureX, textureY); // Box 214
		attachmentModel[48] = new ModelRendererTurbo(this, 209, 81, textureX, textureY); // Box 215
		attachmentModel[49] = new ModelRendererTurbo(this, 353, 81, textureX, textureY); // Box 216
		attachmentModel[50] = new ModelRendererTurbo(this, 361, 81, textureX, textureY); // Box 217
		attachmentModel[51] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 219
		attachmentModel[52] = new ModelRendererTurbo(this, 377, 81, textureX, textureY); // Box 220
		attachmentModel[53] = new ModelRendererTurbo(this, 393, 81, textureX, textureY); // Box 221
		attachmentModel[54] = new ModelRendererTurbo(this, 409, 81, textureX, textureY); // Box 222
		attachmentModel[55] = new ModelRendererTurbo(this, 417, 81, textureX, textureY); // Box 223
		attachmentModel[56] = new ModelRendererTurbo(this, 425, 81, textureX, textureY); // Box 224
		attachmentModel[57] = new ModelRendererTurbo(this, 433, 81, textureX, textureY); // Box 225
		attachmentModel[58] = new ModelRendererTurbo(this, 441, 81, textureX, textureY); // Box 226
		attachmentModel[59] = new ModelRendererTurbo(this, 449, 81, textureX, textureY); // Box 227
		attachmentModel[60] = new ModelRendererTurbo(this, 457, 81, textureX, textureY); // Box 228
		attachmentModel[61] = new ModelRendererTurbo(this, 465, 81, textureX, textureY); // Box 229
		attachmentModel[62] = new ModelRendererTurbo(this, 473, 81, textureX, textureY); // Box 230
		attachmentModel[63] = new ModelRendererTurbo(this, 481, 81, textureX, textureY); // Box 231
		attachmentModel[64] = new ModelRendererTurbo(this, 489, 81, textureX, textureY); // Box 232
		attachmentModel[65] = new ModelRendererTurbo(this, 497, 81, textureX, textureY); // Box 233
		attachmentModel[66] = new ModelRendererTurbo(this, 505, 81, textureX, textureY); // Box 234
		attachmentModel[67] = new ModelRendererTurbo(this, 145, 89, textureX, textureY); // Box 235
		attachmentModel[68] = new ModelRendererTurbo(this, 169, 73, textureX, textureY); // Box 503
		attachmentModel[69] = new ModelRendererTurbo(this, 81, 113, textureX, textureY); // Box 456
		attachmentModel[70] = new ModelRendererTurbo(this, 225, 113, textureX, textureY); // Box 457
		attachmentModel[71] = new ModelRendererTurbo(this, 1, 121, textureX, textureY); // Box 458
		attachmentModel[72] = new ModelRendererTurbo(this, 145, 121, textureX, textureY); // Box 459
		attachmentModel[73] = new ModelRendererTurbo(this, 289, 121, textureX, textureY); // Box 460
		attachmentModel[74] = new ModelRendererTurbo(this, 1, 129, textureX, textureY); // Box 461
		attachmentModel[75] = new ModelRendererTurbo(this, 145, 129, textureX, textureY); // Box 462
		attachmentModel[76] = new ModelRendererTurbo(this, 289, 129, textureX, textureY); // Box 463
		attachmentModel[77] = new ModelRendererTurbo(this, 1, 137, textureX, textureY); // Box 464
		attachmentModel[78] = new ModelRendererTurbo(this, 145, 137, textureX, textureY); // Box 465
		attachmentModel[79] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 468
		attachmentModel[80] = new ModelRendererTurbo(this, 393, 97, textureX, textureY); // Box 469
		attachmentModel[81] = new ModelRendererTurbo(this, 33, 113, textureX, textureY); // Box 479
		attachmentModel[82] = new ModelRendererTurbo(this, 497, 113, textureX, textureY); // Box 483
		attachmentModel[83] = new ModelRendererTurbo(this, 433, 129, textureX, textureY); // Box 498
		attachmentModel[84] = new ModelRendererTurbo(this, 385, 113, textureX, textureY); // Box 511
		attachmentModel[85] = new ModelRendererTurbo(this, 497, 121, textureX, textureY); // Box 512
		attachmentModel[86] = new ModelRendererTurbo(this, 457, 129, textureX, textureY); // Box 513
		attachmentModel[87] = new ModelRendererTurbo(this, 121, 81, textureX, textureY); // Box 514
		attachmentModel[88] = new ModelRendererTurbo(this, 81, 97, textureX, textureY); // Box 515
		attachmentModel[89] = new ModelRendererTurbo(this, 505, 97, textureX, textureY); // Box 516
		attachmentModel[90] = new ModelRendererTurbo(this, 129, 145, textureX, textureY); // Box 517
		attachmentModel[91] = new ModelRendererTurbo(this, 73, 105, textureX, textureY); // Box 530
		attachmentModel[92] = new ModelRendererTurbo(this, 353, 161, textureX, textureY); // Box 627
		attachmentModel[93] = new ModelRendererTurbo(this, 57, 185, textureX, textureY); // Box 559
		attachmentModel[94] = new ModelRendererTurbo(this, 441, 161, textureX, textureY); // Box 560
		attachmentModel[95] = new ModelRendererTurbo(this, 113, 185, textureX, textureY); // Box 585
		attachmentModel[96] = new ModelRendererTurbo(this, 129, 185, textureX, textureY); // Box 586
		attachmentModel[97] = new ModelRendererTurbo(this, 145, 185, textureX, textureY); // Box 587
		attachmentModel[98] = new ModelRendererTurbo(this, 209, 185, textureX, textureY); // Box 588
		attachmentModel[99] = new ModelRendererTurbo(this, 225, 185, textureX, textureY); // Box 589
		attachmentModel[100] = new ModelRendererTurbo(this, 241, 185, textureX, textureY); // Box 590
		attachmentModel[101] = new ModelRendererTurbo(this, 257, 185, textureX, textureY); // Box 591
		attachmentModel[102] = new ModelRendererTurbo(this, 297, 185, textureX, textureY); // Box 592
		attachmentModel[103] = new ModelRendererTurbo(this, 337, 185, textureX, textureY); // Box 593
		attachmentModel[104] = new ModelRendererTurbo(this, 449, 177, textureX, textureY); // Box 594
		attachmentModel[105] = new ModelRendererTurbo(this, 473, 177, textureX, textureY); // Box 595
		attachmentModel[106] = new ModelRendererTurbo(this, 161, 185, textureX, textureY); // Box 596
		attachmentModel[107] = new ModelRendererTurbo(this, 377, 185, textureX, textureY); // Box 597
		attachmentModel[108] = new ModelRendererTurbo(this, 409, 185, textureX, textureY); // Box 598
		attachmentModel[109] = new ModelRendererTurbo(this, 417, 185, textureX, textureY); // Box 599
		attachmentModel[110] = new ModelRendererTurbo(this, 425, 185, textureX, textureY); // Box 600
		attachmentModel[111] = new ModelRendererTurbo(this, 441, 185, textureX, textureY); // Box 601
		attachmentModel[112] = new ModelRendererTurbo(this, 457, 185, textureX, textureY); // Box 602
		attachmentModel[113] = new ModelRendererTurbo(this, 473, 185, textureX, textureY); // Box 603
		attachmentModel[114] = new ModelRendererTurbo(this, 489, 185, textureX, textureY); // Box 604
		attachmentModel[115] = new ModelRendererTurbo(this, 33, 193, textureX, textureY); // Box 605
		attachmentModel[116] = new ModelRendererTurbo(this, 49, 193, textureX, textureY); // Box 606
		attachmentModel[117] = new ModelRendererTurbo(this, 65, 193, textureX, textureY); // Box 607
		attachmentModel[118] = new ModelRendererTurbo(this, 81, 193, textureX, textureY); // Box 608
		attachmentModel[119] = new ModelRendererTurbo(this, 105, 193, textureX, textureY); // Box 609
		attachmentModel[120] = new ModelRendererTurbo(this, 145, 193, textureX, textureY); // Box 610
		attachmentModel[121] = new ModelRendererTurbo(this, 185, 193, textureX, textureY); // Box 611
		attachmentModel[122] = new ModelRendererTurbo(this, 225, 193, textureX, textureY); // Box 612
		attachmentModel[123] = new ModelRendererTurbo(this, 241, 193, textureX, textureY); // Box 613
		attachmentModel[124] = new ModelRendererTurbo(this, 257, 193, textureX, textureY); // Box 614
		attachmentModel[125] = new ModelRendererTurbo(this, 505, 185, textureX, textureY); // Box 615
		attachmentModel[126] = new ModelRendererTurbo(this, 273, 193, textureX, textureY); // Box 616
		attachmentModel[127] = new ModelRendererTurbo(this, 281, 193, textureX, textureY); // Box 617
		attachmentModel[128] = new ModelRendererTurbo(this, 289, 193, textureX, textureY); // Box 618
		attachmentModel[129] = new ModelRendererTurbo(this, 305, 193, textureX, textureY); // Box 619
		attachmentModel[130] = new ModelRendererTurbo(this, 321, 193, textureX, textureY); // Box 620
		attachmentModel[131] = new ModelRendererTurbo(this, 337, 193, textureX, textureY); // Box 621
		attachmentModel[132] = new ModelRendererTurbo(this, 353, 193, textureX, textureY); // Box 622
		attachmentModel[133] = new ModelRendererTurbo(this, 369, 193, textureX, textureY); // Box 623
		attachmentModel[134] = new ModelRendererTurbo(this, 385, 193, textureX, textureY); // Box 624
		attachmentModel[135] = new ModelRendererTurbo(this, 425, 193, textureX, textureY); // Box 625
		attachmentModel[136] = new ModelRendererTurbo(this, 465, 193, textureX, textureY); // Box 626
		attachmentModel[137] = new ModelRendererTurbo(this, 1, 201, textureX, textureY); // Box 627
		attachmentModel[138] = new ModelRendererTurbo(this, 121, 201, textureX, textureY); // Box 628
		attachmentModel[139] = new ModelRendererTurbo(this, 161, 201, textureX, textureY); // Box 629
		attachmentModel[140] = new ModelRendererTurbo(this, 201, 201, textureX, textureY); // Box 630
		attachmentModel[141] = new ModelRendererTurbo(this, 241, 201, textureX, textureY); // Box 631
		attachmentModel[142] = new ModelRendererTurbo(this, 257, 201, textureX, textureY); // Box 632
		attachmentModel[143] = new ModelRendererTurbo(this, 273, 201, textureX, textureY); // Box 633
		attachmentModel[144] = new ModelRendererTurbo(this, 289, 201, textureX, textureY); // Box 634
		attachmentModel[145] = new ModelRendererTurbo(this, 305, 201, textureX, textureY); // Box 635
		attachmentModel[146] = new ModelRendererTurbo(this, 321, 201, textureX, textureY); // Box 636
		attachmentModel[147] = new ModelRendererTurbo(this, 505, 193, textureX, textureY); // Box 637
		attachmentModel[148] = new ModelRendererTurbo(this, 337, 201, textureX, textureY); // Box 638
		attachmentModel[149] = new ModelRendererTurbo(this, 345, 201, textureX, textureY); // Box 639
		attachmentModel[150] = new ModelRendererTurbo(this, 353, 201, textureX, textureY); // Box 640
		attachmentModel[151] = new ModelRendererTurbo(this, 361, 201, textureX, textureY); // Box 641
		attachmentModel[152] = new ModelRendererTurbo(this, 369, 201, textureX, textureY); // Box 642
		attachmentModel[153] = new ModelRendererTurbo(this, 97, 217, textureX, textureY); // Box 609

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 67, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 9
		attachmentModel[0].setRotationPoint(0F, -6F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 60, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 41
		attachmentModel[1].setRotationPoint(0F, -10F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 49
		attachmentModel[2].setRotationPoint(6F, 4F, -5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 67, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[3].setRotationPoint(0F, 7F, -3F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 3, 3, 6, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F); // Box 158
		attachmentModel[4].setRotationPoint(61F, 8F, -3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 159
		attachmentModel[5].setRotationPoint(57F, 8F, -3F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 160
		attachmentModel[6].setRotationPoint(53F, 8F, -3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 161
		attachmentModel[7].setRotationPoint(49F, 8F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 162
		attachmentModel[8].setRotationPoint(45F, 8F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 163
		attachmentModel[9].setRotationPoint(41F, 8F, -3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 164
		attachmentModel[10].setRotationPoint(37F, 8F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 165
		attachmentModel[11].setRotationPoint(33F, 8F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 166
		attachmentModel[12].setRotationPoint(29F, 8F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 167
		attachmentModel[13].setRotationPoint(25F, 8F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 168
		attachmentModel[14].setRotationPoint(21F, 8F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 169
		attachmentModel[15].setRotationPoint(17F, 8F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 170
		attachmentModel[16].setRotationPoint(13F, 8F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 171
		attachmentModel[17].setRotationPoint(9F, 8F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 172
		attachmentModel[18].setRotationPoint(5F, 8F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 173
		attachmentModel[19].setRotationPoint(1F, 8F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 3, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 4F, -2F, 0F, 4F, -2F, 0F, 0F, -2F, 0F); // Box 174
		attachmentModel[20].setRotationPoint(60F, -11F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 175
		attachmentModel[21].setRotationPoint(56F, -11F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 176
		attachmentModel[22].setRotationPoint(52F, -11F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 177
		attachmentModel[23].setRotationPoint(48F, -11F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 178
		attachmentModel[24].setRotationPoint(44F, -11F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 179
		attachmentModel[25].setRotationPoint(40F, -11F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 180
		attachmentModel[26].setRotationPoint(36F, -11F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 181
		attachmentModel[27].setRotationPoint(32F, -11F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 182
		attachmentModel[28].setRotationPoint(28F, -11F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 183
		attachmentModel[29].setRotationPoint(24F, -11F, -3F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 184
		attachmentModel[30].setRotationPoint(20F, -11F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 185
		attachmentModel[31].setRotationPoint(16F, -11F, -3F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 186
		attachmentModel[32].setRotationPoint(12F, -11F, -3F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 187
		attachmentModel[33].setRotationPoint(8F, -11F, -3F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 188
		attachmentModel[34].setRotationPoint(4F, -11F, -3F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 202
		attachmentModel[35].setRotationPoint(58F, -3F, 8F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 203
		attachmentModel[36].setRotationPoint(54F, -3F, 8F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 204
		attachmentModel[37].setRotationPoint(50F, -3F, 8F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 205
		attachmentModel[38].setRotationPoint(46F, -3F, 8F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 206
		attachmentModel[39].setRotationPoint(42F, -3F, 8F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 207
		attachmentModel[40].setRotationPoint(38F, -3F, 8F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 208
		attachmentModel[41].setRotationPoint(34F, -3F, 8F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 209
		attachmentModel[42].setRotationPoint(30F, -3F, 8F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 210
		attachmentModel[43].setRotationPoint(26F, -3F, 8F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 211
		attachmentModel[44].setRotationPoint(22F, -3F, 8F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 212
		attachmentModel[45].setRotationPoint(18F, -3F, 8F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 213
		attachmentModel[46].setRotationPoint(14F, -3F, 8F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 214
		attachmentModel[47].setRotationPoint(10F, -3F, 8F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 67, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 215
		attachmentModel[48].setRotationPoint(0F, -3F, 7F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 216
		attachmentModel[49].setRotationPoint(6F, -3F, 8F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 2, 6, 2, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 217
		attachmentModel[50].setRotationPoint(2F, -3F, 8F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 67, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 219
		attachmentModel[51].setRotationPoint(0F, -3F, -8F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 2, 6, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F); // Box 220
		attachmentModel[52].setRotationPoint(2F, -3F, -9F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 3, 6, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F); // Box 221
		attachmentModel[53].setRotationPoint(62F, -3F, -10F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 222
		attachmentModel[54].setRotationPoint(58F, -3F, -9F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 223
		attachmentModel[55].setRotationPoint(54F, -3F, -9F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 224
		attachmentModel[56].setRotationPoint(50F, -3F, -9F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 225
		attachmentModel[57].setRotationPoint(46F, -3F, -9F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 226
		attachmentModel[58].setRotationPoint(42F, -3F, -9F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 227
		attachmentModel[59].setRotationPoint(38F, -3F, -9F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 228
		attachmentModel[60].setRotationPoint(34F, -3F, -9F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 229
		attachmentModel[61].setRotationPoint(30F, -3F, -9F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 230
		attachmentModel[62].setRotationPoint(26F, -3F, -9F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 231
		attachmentModel[63].setRotationPoint(22F, -3F, -9F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 232
		attachmentModel[64].setRotationPoint(18F, -3F, -9F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 233
		attachmentModel[65].setRotationPoint(14F, -3F, -9F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 234
		attachmentModel[66].setRotationPoint(10F, -3F, -9F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 235
		attachmentModel[67].setRotationPoint(6F, -3F, -9F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 503
		attachmentModel[68].setRotationPoint(50F, -4.5F, 3.5F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 67, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[69].setRotationPoint(0F, -9F, -3F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 67, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 457
		attachmentModel[70].setRotationPoint(0F, -8F, -3F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 67, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 458
		attachmentModel[71].setRotationPoint(0F, -8F, 2F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 67, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 459
		attachmentModel[72].setRotationPoint(0F, -6F, -3F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 67, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 460
		attachmentModel[73].setRotationPoint(0F, -3F, -7F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 67, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[74].setRotationPoint(0F, 2F, -7F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 67, 1, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 462
		attachmentModel[75].setRotationPoint(0F, 2F, 5F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 67, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[76].setRotationPoint(0F, -3F, 5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 67, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 464
		attachmentModel[77].setRotationPoint(0F, 6F, -3F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 67, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 465
		attachmentModel[78].setRotationPoint(0F, 6F, 2F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 468
		attachmentModel[79].setRotationPoint(0F, -11F, -3F);

		attachmentModel[80].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[80].setRotationPoint(9F, 6F, 5F);
		attachmentModel[80].rotateAngleZ = -0.78539816F;

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 479
		attachmentModel[81].setRotationPoint(50F, -5.5F, 3F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 483
		attachmentModel[82].setRotationPoint(50F, -3.5F, 4.5F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 498
		attachmentModel[83].setRotationPoint(6F, 4F, 3F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 511
		attachmentModel[84].setRotationPoint(64F, 4F, 4F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 1F, 0F, -1F); // Box 512
		attachmentModel[85].setRotationPoint(64F, 5F, 3F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 513
		attachmentModel[86].setRotationPoint(64F, 3F, 5F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0.5F, 0F, 1F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F); // Box 514
		attachmentModel[87].setRotationPoint(57F, 3F, 4F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, -0.5F, -0.5F); // Box 515
		attachmentModel[88].setRotationPoint(57F, 4F, 4F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0.5F, -0.5F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0.5F, 0.5F, 1.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 1.5F, 0F, -1F); // Box 516
		attachmentModel[89].setRotationPoint(57F, 5F, 3F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, -1F, 1.5F, 0F, -1F, 1.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 517
		attachmentModel[90].setRotationPoint(0F, 5F, 2F);

		attachmentModel[91].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 530
		attachmentModel[91].setRotationPoint(9F, 6F, -6F);
		attachmentModel[91].rotateAngleZ = -0.78539816F;

		attachmentModel[92].addShapeBox(-1F, -1F, 0F, 2, 2, 10, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 627
		attachmentModel[92].setRotationPoint(9F, 6F, -5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 559
		attachmentModel[93].setRotationPoint(-1F, -11F, -1F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0F, 0F, 0F); // Box 560
		attachmentModel[94].setRotationPoint(-2F, -11F, -0.5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 585
		attachmentModel[95].setRotationPoint(35F, -5.5F, 3F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 586
		attachmentModel[96].setRotationPoint(35F, -4.5F, 3.5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 587
		attachmentModel[97].setRotationPoint(35F, -3.5F, 4.5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 588
		attachmentModel[98].setRotationPoint(20F, -5.5F, 3F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 589
		attachmentModel[99].setRotationPoint(20F, -4.5F, 3.5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 590
		attachmentModel[100].setRotationPoint(20F, -3.5F, 4.5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 591
		attachmentModel[101].setRotationPoint(0F, -4.5F, 3.5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 592
		attachmentModel[102].setRotationPoint(0F, -5.5F, 3F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 593
		attachmentModel[103].setRotationPoint(0F, -3.5F, 4.5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 594
		attachmentModel[104].setRotationPoint(65F, -3.5F, 4.5F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 595
		attachmentModel[105].setRotationPoint(65F, -4.5F, 3.5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 596
		attachmentModel[106].setRotationPoint(65F, -5.5F, 3F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 597
		attachmentModel[107].setRotationPoint(65F, -5.5F, -4F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 598
		attachmentModel[108].setRotationPoint(65F, -4.5F, -4.5F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 599
		attachmentModel[109].setRotationPoint(65F, -3.5F, -5.5F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 600
		attachmentModel[110].setRotationPoint(50F, -5.5F, -4F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 601
		attachmentModel[111].setRotationPoint(50F, -4.5F, -4.5F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 602
		attachmentModel[112].setRotationPoint(50F, -3.5F, -5.5F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 603
		attachmentModel[113].setRotationPoint(35F, -5.5F, -4F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 604
		attachmentModel[114].setRotationPoint(35F, -4.5F, -4.5F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 605
		attachmentModel[115].setRotationPoint(35F, -3.5F, -5.5F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 606
		attachmentModel[116].setRotationPoint(20F, -5.5F, -4F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 607
		attachmentModel[117].setRotationPoint(20F, -4.5F, -4.5F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 608
		attachmentModel[118].setRotationPoint(20F, -3.5F, -5.5F);

		attachmentModel[119].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 609
		attachmentModel[119].setRotationPoint(0F, -5.5F, -4F);

		attachmentModel[120].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 610
		attachmentModel[120].setRotationPoint(0F, -4.5F, -4.5F);

		attachmentModel[121].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 611
		attachmentModel[121].setRotationPoint(0F, -3.5F, -5.5F);

		attachmentModel[122].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 612
		attachmentModel[122].setRotationPoint(64F, 4F, -5F);

		attachmentModel[123].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 613
		attachmentModel[123].setRotationPoint(64F, 3F, -6F);

		attachmentModel[124].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 1F, 0F, 1F); // Box 614
		attachmentModel[124].setRotationPoint(64F, 5F, -4F);

		attachmentModel[125].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 615
		attachmentModel[125].setRotationPoint(50F, 2.5F, 4.5F);

		attachmentModel[126].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 616
		attachmentModel[126].setRotationPoint(50F, 3.5F, 3.5F);

		attachmentModel[127].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 617
		attachmentModel[127].setRotationPoint(50F, 4.5F, 3F);

		attachmentModel[128].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 618
		attachmentModel[128].setRotationPoint(35F, 3.5F, 3.5F);

		attachmentModel[129].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 619
		attachmentModel[129].setRotationPoint(35F, 4.5F, 3F);

		attachmentModel[130].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 620
		attachmentModel[130].setRotationPoint(35F, 2.5F, 4.5F);

		attachmentModel[131].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 621
		attachmentModel[131].setRotationPoint(20F, 4.5F, 3F);

		attachmentModel[132].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 622
		attachmentModel[132].setRotationPoint(20F, 3.5F, 3.5F);

		attachmentModel[133].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 623
		attachmentModel[133].setRotationPoint(20F, 2.5F, 4.5F);

		attachmentModel[134].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 624
		attachmentModel[134].setRotationPoint(0F, 4.5F, 3F);

		attachmentModel[135].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 625
		attachmentModel[135].setRotationPoint(0F, 3.5F, 3.5F);

		attachmentModel[136].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 626
		attachmentModel[136].setRotationPoint(0F, 2.5F, 4.5F);

		attachmentModel[137].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, 1F, 1.5F, 0F, 1F, 1.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 627
		attachmentModel[137].setRotationPoint(0F, 5F, -3F);

		attachmentModel[138].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 628
		attachmentModel[138].setRotationPoint(0F, 4.5F, -4F);

		attachmentModel[139].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 629
		attachmentModel[139].setRotationPoint(0F, 3.5F, -4.5F);

		attachmentModel[140].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 630
		attachmentModel[140].setRotationPoint(0F, 2.5F, -5.5F);

		attachmentModel[141].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 631
		attachmentModel[141].setRotationPoint(20F, 4.5F, -4F);

		attachmentModel[142].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 632
		attachmentModel[142].setRotationPoint(20F, 3.5F, -4.5F);

		attachmentModel[143].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 633
		attachmentModel[143].setRotationPoint(20F, 2.5F, -5.5F);

		attachmentModel[144].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 634
		attachmentModel[144].setRotationPoint(35F, 4.5F, -4F);

		attachmentModel[145].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 635
		attachmentModel[145].setRotationPoint(35F, 3.5F, -4.5F);

		attachmentModel[146].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 636
		attachmentModel[146].setRotationPoint(35F, 2.5F, -5.5F);

		attachmentModel[147].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 637
		attachmentModel[147].setRotationPoint(50F, 4.5F, -4F);

		attachmentModel[148].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 638
		attachmentModel[148].setRotationPoint(50F, 2.5F, -5.5F);

		attachmentModel[149].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 639
		attachmentModel[149].setRotationPoint(50F, 3.5F, -4.5F);

		attachmentModel[150].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0.5F, 0.5F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0.5F, -0.5F, 1.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 1.5F, 0F, 1F); // Box 640
		attachmentModel[150].setRotationPoint(57F, 5F, -4F);

		attachmentModel[151].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, -0.5F, 0.5F); // Box 641
		attachmentModel[151].setRotationPoint(57F, 4F, -5F);

		attachmentModel[152].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, 0.5F, 0F, -1F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F); // Box 642
		attachmentModel[152].setRotationPoint(57F, 3F, -5F);

		attachmentModel[153].addShapeBox(0F, 0F, 0F, 3, 6, 2, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 609
		attachmentModel[153].setRotationPoint(62F, -3F, 8F);

		flipAll();
	}
}