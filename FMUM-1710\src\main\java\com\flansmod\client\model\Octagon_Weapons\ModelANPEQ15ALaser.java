//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: AN/PEQ15-A Laser
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelDefault;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelANPEQ15ALaser extends ModelDefault //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelANPEQ15ALaser() //Same as Filename
	{
		defaultModel = new ModelRendererTurbo[20];
		defaultModel[0] = new ModelRendererTurbo(this, 116, 57, textureX, textureY); // Box 572
		defaultModel[1] = new ModelRendererTurbo(this, 116, 57, textureX, textureY); // Box 574
		defaultModel[2] = new ModelRendererTurbo(this, 116, 57, textureX, textureY); // Box 575
		defaultModel[3] = new ModelRendererTurbo(this, 116, 57, textureX, textureY); // Box 576
		defaultModel[4] = new ModelRendererTurbo(this, 116, 57, textureX, textureY); // Box 577
		defaultModel[5] = new ModelRendererTurbo(this, 114, 57, textureX, textureY); // Box 578
		defaultModel[6] = new ModelRendererTurbo(this, 114, 57, textureX, textureY); // Box 579
		defaultModel[7] = new ModelRendererTurbo(this, 114, 57, textureX, textureY); // Box 580
		defaultModel[8] = new ModelRendererTurbo(this, 114, 57, textureX, textureY); // Box 581
		defaultModel[9] = new ModelRendererTurbo(this, 114, 57, textureX, textureY); // Box 582
		defaultModel[10] = new ModelRendererTurbo(this, 114, 55, textureX, textureY); // Box 10
		defaultModel[11] = new ModelRendererTurbo(this, 116, 55, textureX, textureY); // Box 11
		defaultModel[12] = new ModelRendererTurbo(this, 114, 55, textureX, textureY); // Box 12
		defaultModel[13] = new ModelRendererTurbo(this, 116, 55, textureX, textureY); // Box 13
		defaultModel[14] = new ModelRendererTurbo(this, 116, 55, textureX, textureY); // Box 14
		defaultModel[15] = new ModelRendererTurbo(this, 114, 55, textureX, textureY); // Box 15
		defaultModel[16] = new ModelRendererTurbo(this, 114, 55, textureX, textureY); // Box 16
		defaultModel[17] = new ModelRendererTurbo(this, 116, 55, textureX, textureY); // Box 17
		defaultModel[18] = new ModelRendererTurbo(this, 116, 55, textureX, textureY); // Box 18
		defaultModel[19] = new ModelRendererTurbo(this, 114, 55, textureX, textureY); // Box 19

		defaultModel[0].addShapeBox(0F, -1F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F); // Box 572
		defaultModel[0].setRotationPoint(0F, 0F, 0F);

		defaultModel[1].addShapeBox(0F, -1F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 574
		defaultModel[1].setRotationPoint(160F, 0F, 0F);

		defaultModel[2].addShapeBox(0F, -1F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F); // Box 575
		defaultModel[2].setRotationPoint(152F, 0F, 0F);

		defaultModel[3].addShapeBox(0F, -1F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 576
		defaultModel[3].setRotationPoint(148F, 0F, 0F);

		defaultModel[4].addShapeBox(0F, -1F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F); // Box 577
		defaultModel[4].setRotationPoint(135F, 0F, 0F);

		defaultModel[5].addShapeBox(0F, 0F, -1F, 1, 0, 2, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F); // Box 578
		defaultModel[5].setRotationPoint(0F, 0F, 0F);

		defaultModel[6].addShapeBox(0F, 0F, -1F, 1, 0, 2, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F); // Box 579
		defaultModel[6].setRotationPoint(135F, 0F, 0F);

		defaultModel[7].addShapeBox(0F, 0F, -1F, 1, 0, 2, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 580
		defaultModel[7].setRotationPoint(148F, 0F, 0F);

		defaultModel[8].addShapeBox(0F, 0F, -1F, 1, 0, 2, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F); // Box 581
		defaultModel[8].setRotationPoint(152F, 0F, 0F);

		defaultModel[9].addShapeBox(0F, 0F, -1F, 1, 0, 2, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 582
		defaultModel[9].setRotationPoint(160F, 0F, 0F);

		defaultModel[10].addShapeBox(0F, 0F, 0F, 1, 0, 2, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F); // Box 10
		defaultModel[10].setRotationPoint(0F, 0F, -1F);

		defaultModel[11].addShapeBox(0F, 0F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 132F, 0F, 0F, 132F, 0F, 0F, 0F, 0F, 0F); // Box 11
		defaultModel[11].setRotationPoint(0F, -1F, 0F);

		defaultModel[12].addShapeBox(0F, 0F, 0F, 1, 0, 2, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F); // Box 12
		defaultModel[12].setRotationPoint(135F, 0F, -1F);

		defaultModel[13].addShapeBox(0F, 0F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 11F, 0F, 0F, 11F, 0F, 0F, 0F, 0F, 0F); // Box 13
		defaultModel[13].setRotationPoint(135F, -1F, 0F);

		defaultModel[14].addShapeBox(0F, 0F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 14
		defaultModel[14].setRotationPoint(148F, -1F, 0F);

		defaultModel[15].addShapeBox(0F, 0F, 0F, 1, 0, 2, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 15
		defaultModel[15].setRotationPoint(148F, 0F, -1F);

		defaultModel[16].addShapeBox(0F, 0F, 0F, 1, 0, 2, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F); // Box 16
		defaultModel[16].setRotationPoint(152F, 0F, -1F);

		defaultModel[17].addShapeBox(0F, 0F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F); // Box 17
		defaultModel[17].setRotationPoint(152F, -1F, 0F);

		defaultModel[18].addShapeBox(0F, 0F, 0F, 1, 2, 0, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 18
		defaultModel[18].setRotationPoint(160F, -1F, 0F);

		defaultModel[19].addShapeBox(0F, 0F, 0F, 1, 0, 2, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 19
		defaultModel[19].setRotationPoint(160F, 0F, -1F);
	}
}