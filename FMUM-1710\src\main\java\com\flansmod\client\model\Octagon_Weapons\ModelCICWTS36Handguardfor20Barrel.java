//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC WTS-36 Handguard for 20 Barrel
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36Handguardfor20Barrel extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelCICWTS36Handguardfor20Barrel() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[188];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 57
		attachmentModel[1] = new ModelRendererTurbo(this, 193, 1, textureX, textureY); // Box 59
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 62
		attachmentModel[3] = new ModelRendererTurbo(this, 65, 9, textureX, textureY); // Box 63
		attachmentModel[4] = new ModelRendererTurbo(this, 201, 1, textureX, textureY); // Box 64
		attachmentModel[5] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 93
		attachmentModel[6] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 94
		attachmentModel[7] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 100
		attachmentModel[8] = new ModelRendererTurbo(this, 217, 1, textureX, textureY); // Box 102
		attachmentModel[9] = new ModelRendererTurbo(this, 121, 9, textureX, textureY); // Box 103
		attachmentModel[10] = new ModelRendererTurbo(this, 145, 9, textureX, textureY); // Box 104
		attachmentModel[11] = new ModelRendererTurbo(this, 169, 9, textureX, textureY); // Box 105
		attachmentModel[12] = new ModelRendererTurbo(this, 193, 9, textureX, textureY); // Box 106
		attachmentModel[13] = new ModelRendererTurbo(this, 217, 9, textureX, textureY); // Box 107
		attachmentModel[14] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 111
		attachmentModel[15] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 112
		attachmentModel[16] = new ModelRendererTurbo(this, 137, 9, textureX, textureY); // Box 113
		attachmentModel[17] = new ModelRendererTurbo(this, 161, 9, textureX, textureY); // Box 114
		attachmentModel[18] = new ModelRendererTurbo(this, 193, 17, textureX, textureY); // Box 115
		attachmentModel[19] = new ModelRendererTurbo(this, 217, 17, textureX, textureY); // Box 116
		attachmentModel[20] = new ModelRendererTurbo(this, 185, 25, textureX, textureY); // Box 117
		attachmentModel[21] = new ModelRendererTurbo(this, 209, 25, textureX, textureY); // Box 118
		attachmentModel[22] = new ModelRendererTurbo(this, 233, 25, textureX, textureY); // Box 119
		attachmentModel[23] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 120
		attachmentModel[24] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 121
		attachmentModel[25] = new ModelRendererTurbo(this, 233, 8, textureX, textureY); // Box 123
		attachmentModel[26] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 124
		attachmentModel[27] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 125
		attachmentModel[28] = new ModelRendererTurbo(this, 209, 9, textureX, textureY); // Box 126
		attachmentModel[29] = new ModelRendererTurbo(this, 241, 33, textureX, textureY); // Box 208
		attachmentModel[30] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 209
		attachmentModel[31] = new ModelRendererTurbo(this, 249, 17, textureX, textureY); // Box 210
		attachmentModel[32] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 211
		attachmentModel[33] = new ModelRendererTurbo(this, 9, 41, textureX, textureY); // Box 212
		attachmentModel[34] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 213
		attachmentModel[35] = new ModelRendererTurbo(this, 25, 41, textureX, textureY); // Box 214
		attachmentModel[36] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 215
		attachmentModel[37] = new ModelRendererTurbo(this, 41, 41, textureX, textureY); // Box 216
		attachmentModel[38] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 217
		attachmentModel[39] = new ModelRendererTurbo(this, 57, 41, textureX, textureY); // Box 218
		attachmentModel[40] = new ModelRendererTurbo(this, 65, 41, textureX, textureY); // Box 219
		attachmentModel[41] = new ModelRendererTurbo(this, 73, 41, textureX, textureY); // Box 220
		attachmentModel[42] = new ModelRendererTurbo(this, 89, 41, textureX, textureY); // Box 341
		attachmentModel[43] = new ModelRendererTurbo(this, 105, 41, textureX, textureY); // Box 342
		attachmentModel[44] = new ModelRendererTurbo(this, 113, 41, textureX, textureY); // Box 343
		attachmentModel[45] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 344
		attachmentModel[46] = new ModelRendererTurbo(this, 129, 41, textureX, textureY); // Box 345
		attachmentModel[47] = new ModelRendererTurbo(this, 137, 41, textureX, textureY); // Box 346
		attachmentModel[48] = new ModelRendererTurbo(this, 145, 41, textureX, textureY); // Box 347
		attachmentModel[49] = new ModelRendererTurbo(this, 153, 41, textureX, textureY); // Box 348
		attachmentModel[50] = new ModelRendererTurbo(this, 161, 41, textureX, textureY); // Box 349
		attachmentModel[51] = new ModelRendererTurbo(this, 169, 41, textureX, textureY); // Box 350
		attachmentModel[52] = new ModelRendererTurbo(this, 177, 41, textureX, textureY); // Box 351
		attachmentModel[53] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 352
		attachmentModel[54] = new ModelRendererTurbo(this, 87, 149, textureX, textureY); // Box 353
		attachmentModel[55] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 382
		attachmentModel[56] = new ModelRendererTurbo(this, 209, 17, textureX, textureY); // Box 405
		attachmentModel[57] = new ModelRendererTurbo(this, 201, 25, textureX, textureY); // Box 406
		attachmentModel[58] = new ModelRendererTurbo(this, 225, 25, textureX, textureY); // Box 407
		attachmentModel[59] = new ModelRendererTurbo(this, 233, 17, textureX, textureY); // Box 410
		attachmentModel[60] = new ModelRendererTurbo(this, 249, 25, textureX, textureY); // Box 411
		attachmentModel[61] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 426
		attachmentModel[62] = new ModelRendererTurbo(this, 209, 41, textureX, textureY); // Box 439
		attachmentModel[63] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 440
		attachmentModel[64] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 441
		attachmentModel[65] = new ModelRendererTurbo(this, 17, 33, textureX, textureY); // Box 416
		attachmentModel[66] = new ModelRendererTurbo(this, 105, 163, textureX, textureY); // Box 417
		attachmentModel[67] = new ModelRendererTurbo(this, 41, 33, textureX, textureY); // Box 418
		attachmentModel[68] = new ModelRendererTurbo(this, 233, 33, textureX, textureY); // Box 448
		attachmentModel[69] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 449
		attachmentModel[70] = new ModelRendererTurbo(this, 17, 49, textureX, textureY); // Box 450
		attachmentModel[71] = new ModelRendererTurbo(this, 33, 49, textureX, textureY); // Box 451
		attachmentModel[72] = new ModelRendererTurbo(this, 105, 49, textureX, textureY); // Box 452
		attachmentModel[73] = new ModelRendererTurbo(this, 145, 49, textureX, textureY); // Box 453
		attachmentModel[74] = new ModelRendererTurbo(this, 201, 49, textureX, textureY); // Box 454
		attachmentModel[75] = new ModelRendererTurbo(this, 193, 57, textureX, textureY); // Box 455
		attachmentModel[76] = new ModelRendererTurbo(this, 233, 41, textureX, textureY); // Box 456
		attachmentModel[77] = new ModelRendererTurbo(this, 49, 49, textureX, textureY); // Box 457
		attachmentModel[78] = new ModelRendererTurbo(this, 57, 49, textureX, textureY); // Box 458
		attachmentModel[79] = new ModelRendererTurbo(this, 241, 49, textureX, textureY); // Box 459
		attachmentModel[80] = new ModelRendererTurbo(this, 193, 65, textureX, textureY); // Box 460
		attachmentModel[81] = new ModelRendererTurbo(this, 209, 65, textureX, textureY); // Box 461
		attachmentModel[82] = new ModelRendererTurbo(this, 225, 65, textureX, textureY); // Box 462
		attachmentModel[83] = new ModelRendererTurbo(this, 241, 65, textureX, textureY); // Box 463
		attachmentModel[84] = new ModelRendererTurbo(this, 193, 73, textureX, textureY); // Box 464
		attachmentModel[85] = new ModelRendererTurbo(this, 209, 73, textureX, textureY); // Box 465
		attachmentModel[86] = new ModelRendererTurbo(this, 193, 81, textureX, textureY); // Box 466
		attachmentModel[87] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 467
		attachmentModel[88] = new ModelRendererTurbo(this, 41, 89, textureX, textureY); // Box 468
		attachmentModel[89] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 469
		attachmentModel[90] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 470
		attachmentModel[91] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 471
		attachmentModel[92] = new ModelRendererTurbo(this, 65, 49, textureX, textureY); // Box 472
		attachmentModel[93] = new ModelRendererTurbo(this, 185, 49, textureX, textureY); // Box 473
		attachmentModel[94] = new ModelRendererTurbo(this, 249, 73, textureX, textureY); // Box 474
		attachmentModel[95] = new ModelRendererTurbo(this, 233, 81, textureX, textureY); // Box 475
		attachmentModel[96] = new ModelRendererTurbo(this, 233, 89, textureX, textureY); // Box 476
		attachmentModel[97] = new ModelRendererTurbo(this, 193, 97, textureX, textureY); // Box 477
		attachmentModel[98] = new ModelRendererTurbo(this, 209, 97, textureX, textureY); // Box 478
		attachmentModel[99] = new ModelRendererTurbo(this, 225, 97, textureX, textureY); // Box 479
		attachmentModel[100] = new ModelRendererTurbo(this, 241, 97, textureX, textureY); // Box 480
		attachmentModel[101] = new ModelRendererTurbo(this, 193, 105, textureX, textureY); // Box 481
		attachmentModel[102] = new ModelRendererTurbo(this, 121, 113, textureX, textureY); // Box 482
		attachmentModel[103] = new ModelRendererTurbo(this, 161, 113, textureX, textureY); // Box 483
		attachmentModel[104] = new ModelRendererTurbo(this, 201, 113, textureX, textureY); // Box 484
		attachmentModel[105] = new ModelRendererTurbo(this, 1, 121, textureX, textureY); // Box 485
		attachmentModel[106] = new ModelRendererTurbo(this, 41, 121, textureX, textureY); // Box 486
		attachmentModel[107] = new ModelRendererTurbo(this, 233, 105, textureX, textureY); // Box 487
		attachmentModel[108] = new ModelRendererTurbo(this, 241, 113, textureX, textureY); // Box 488
		attachmentModel[109] = new ModelRendererTurbo(this, 81, 121, textureX, textureY); // Box 489
		attachmentModel[110] = new ModelRendererTurbo(this, 97, 121, textureX, textureY); // Box 490
		attachmentModel[111] = new ModelRendererTurbo(this, 113, 121, textureX, textureY); // Box 491
		attachmentModel[112] = new ModelRendererTurbo(this, 129, 121, textureX, textureY); // Box 492
		attachmentModel[113] = new ModelRendererTurbo(this, 249, 81, textureX, textureY); // Box 493
		attachmentModel[114] = new ModelRendererTurbo(this, 249, 89, textureX, textureY); // Box 494
		attachmentModel[115] = new ModelRendererTurbo(this, 249, 105, textureX, textureY); // Box 495
		attachmentModel[116] = new ModelRendererTurbo(this, 145, 121, textureX, textureY); // Box 459
		attachmentModel[117] = new ModelRendererTurbo(this, 161, 121, textureX, textureY); // Box 460
		attachmentModel[118] = new ModelRendererTurbo(this, 177, 121, textureX, textureY); // Box 461
		attachmentModel[119] = new ModelRendererTurbo(this, 193, 121, textureX, textureY); // Box 462
		attachmentModel[120] = new ModelRendererTurbo(this, 209, 121, textureX, textureY); // Box 463
		attachmentModel[121] = new ModelRendererTurbo(this, 225, 121, textureX, textureY); // Box 464
		attachmentModel[122] = new ModelRendererTurbo(this, 241, 121, textureX, textureY); // Box 465
		attachmentModel[123] = new ModelRendererTurbo(this, 1, 129, textureX, textureY); // Box 466
		attachmentModel[124] = new ModelRendererTurbo(this, 17, 129, textureX, textureY); // Box 467
		attachmentModel[125] = new ModelRendererTurbo(this, 33, 129, textureX, textureY); // Box 468
		attachmentModel[126] = new ModelRendererTurbo(this, 49, 129, textureX, textureY); // Box 469
		attachmentModel[127] = new ModelRendererTurbo(this, 65, 129, textureX, textureY); // Box 470
		attachmentModel[128] = new ModelRendererTurbo(this, 81, 129, textureX, textureY); // Box 471
		attachmentModel[129] = new ModelRendererTurbo(this, 89, 129, textureX, textureY); // Box 472
		attachmentModel[130] = new ModelRendererTurbo(this, 97, 129, textureX, textureY); // Box 473
		attachmentModel[131] = new ModelRendererTurbo(this, 105, 129, textureX, textureY); // Box 474
		attachmentModel[132] = new ModelRendererTurbo(this, 113, 129, textureX, textureY); // Box 475
		attachmentModel[133] = new ModelRendererTurbo(this, 121, 129, textureX, textureY); // Box 476
		attachmentModel[134] = new ModelRendererTurbo(this, 129, 129, textureX, textureY); // Box 477
		attachmentModel[135] = new ModelRendererTurbo(this, 145, 129, textureX, textureY); // Box 478
		attachmentModel[136] = new ModelRendererTurbo(this, 161, 129, textureX, textureY); // Box 479
		attachmentModel[137] = new ModelRendererTurbo(this, 177, 129, textureX, textureY); // Box 480
		attachmentModel[138] = new ModelRendererTurbo(this, 193, 129, textureX, textureY); // Box 481
		attachmentModel[139] = new ModelRendererTurbo(this, 209, 129, textureX, textureY); // Box 482
		attachmentModel[140] = new ModelRendererTurbo(this, 217, 129, textureX, textureY); // Box 483
		attachmentModel[141] = new ModelRendererTurbo(this, 1, 137, textureX, textureY); // Box 484
		attachmentModel[142] = new ModelRendererTurbo(this, 25, 137, textureX, textureY); // Box 485
		attachmentModel[143] = new ModelRendererTurbo(this, 49, 137, textureX, textureY); // Box 486
		attachmentModel[144] = new ModelRendererTurbo(this, 73, 137, textureX, textureY); // Box 487
		attachmentModel[145] = new ModelRendererTurbo(this, 97, 137, textureX, textureY); // Box 488
		attachmentModel[146] = new ModelRendererTurbo(this, 145, 137, textureX, textureY); // Box 490
		attachmentModel[147] = new ModelRendererTurbo(this, 169, 137, textureX, textureY); // Box 491
		attachmentModel[148] = new ModelRendererTurbo(this, 241, 129, textureX, textureY); // Box 492
		attachmentModel[149] = new ModelRendererTurbo(this, 249, 129, textureX, textureY); // Box 493
		attachmentModel[150] = new ModelRendererTurbo(this, 193, 137, textureX, textureY); // Box 494
		attachmentModel[151] = new ModelRendererTurbo(this, 201, 137, textureX, textureY); // Box 495
		attachmentModel[152] = new ModelRendererTurbo(this, 209, 137, textureX, textureY); // Box 496
		attachmentModel[153] = new ModelRendererTurbo(this, 217, 137, textureX, textureY); // Box 497
		attachmentModel[154] = new ModelRendererTurbo(this, 225, 137, textureX, textureY); // Box 498
		attachmentModel[155] = new ModelRendererTurbo(this, 233, 137, textureX, textureY); // Box 499
		attachmentModel[156] = new ModelRendererTurbo(this, 241, 137, textureX, textureY); // Box 500
		attachmentModel[157] = new ModelRendererTurbo(this, 249, 137, textureX, textureY); // Box 501
		attachmentModel[158] = new ModelRendererTurbo(this, 1, 145, textureX, textureY); // Box 502
		attachmentModel[159] = new ModelRendererTurbo(this, 9, 145, textureX, textureY); // Box 503
		attachmentModel[160] = new ModelRendererTurbo(this, 17, 145, textureX, textureY); // Box 504
		attachmentModel[161] = new ModelRendererTurbo(this, 25, 145, textureX, textureY); // Box 505
		attachmentModel[162] = new ModelRendererTurbo(this, 33, 145, textureX, textureY); // Box 506
		attachmentModel[163] = new ModelRendererTurbo(this, 41, 145, textureX, textureY); // Box 507
		attachmentModel[164] = new ModelRendererTurbo(this, 17, 137, textureX, textureY); // Box 512
		attachmentModel[165] = new ModelRendererTurbo(this, 41, 137, textureX, textureY); // Box 513
		attachmentModel[166] = new ModelRendererTurbo(this, 65, 137, textureX, textureY); // Box 514
		attachmentModel[167] = new ModelRendererTurbo(this, 65, 145, textureX, textureY); // Box 515
		attachmentModel[168] = new ModelRendererTurbo(this, 121, 145, textureX, textureY); // Box 516
		attachmentModel[169] = new ModelRendererTurbo(this, 193, 145, textureX, textureY); // Box 517
		attachmentModel[170] = new ModelRendererTurbo(this, 217, 145, textureX, textureY); // Box 518
		attachmentModel[171] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 519
		attachmentModel[172] = new ModelRendererTurbo(this, 25, 153, textureX, textureY); // Box 520
		attachmentModel[173] = new ModelRendererTurbo(this, 49, 153, textureX, textureY); // Box 521
		attachmentModel[174] = new ModelRendererTurbo(this, 73, 153, textureX, textureY); // Box 522
		attachmentModel[175] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 524
		attachmentModel[176] = new ModelRendererTurbo(this, 121, 161, textureX, textureY); // Box 525
		attachmentModel[177] = new ModelRendererTurbo(this, 1, 169, textureX, textureY); // Box 526
		attachmentModel[178] = new ModelRendererTurbo(this, 89, 137, textureX, textureY); // Box 527
		attachmentModel[179] = new ModelRendererTurbo(this, 113, 137, textureX, textureY); // Box 528
		attachmentModel[180] = new ModelRendererTurbo(this, 137, 137, textureX, textureY); // Box 529
		attachmentModel[181] = new ModelRendererTurbo(this, 161, 137, textureX, textureY); // Box 530
		attachmentModel[182] = new ModelRendererTurbo(this, 209, 145, textureX, textureY); // Box 531
		attachmentModel[183] = new ModelRendererTurbo(this, 233, 145, textureX, textureY); // Box 532
		attachmentModel[184] = new ModelRendererTurbo(this, 17, 153, textureX, textureY); // Box 533
		attachmentModel[185] = new ModelRendererTurbo(this, 41, 153, textureX, textureY); // Box 534
		attachmentModel[186] = new ModelRendererTurbo(this, 65, 153, textureX, textureY); // Box 535
		attachmentModel[187] = new ModelRendererTurbo(this, 182, 141, textureX, textureY); // Box 187

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 89, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 57
		attachmentModel[0].setRotationPoint(0F, -3F, 7F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 59
		attachmentModel[1].setRotationPoint(55F, -6F, -4F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 26, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 62
		attachmentModel[2].setRotationPoint(63F, -6F, 3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 26, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 63
		attachmentModel[3].setRotationPoint(63F, -6F, -4F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 64
		attachmentModel[4].setRotationPoint(55F, -6F, 3F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 89, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 93
		attachmentModel[5].setRotationPoint(0F, -3F, -8F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 89, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 94
		attachmentModel[6].setRotationPoint(0F, 5F, -3F);

		attachmentModel[7].addShapeBox(-1F, -1F, 0F, 2, 2, 2, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 100
		attachmentModel[7].setRotationPoint(87F, 8F, -1F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 102
		attachmentModel[8].setRotationPoint(20F, 8F, -3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 103
		attachmentModel[9].setRotationPoint(12F, 8F, -3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 104
		attachmentModel[10].setRotationPoint(16F, 8F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 105
		attachmentModel[11].setRotationPoint(0F, 8F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 106
		attachmentModel[12].setRotationPoint(4F, 8F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 107
		attachmentModel[13].setRotationPoint(8F, 8F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -1F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0.5F, -1F, 0F); // Box 111
		attachmentModel[14].setRotationPoint(86F, 8F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 112
		attachmentModel[15].setRotationPoint(85F, 7F, 1F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F); // Box 113
		attachmentModel[16].setRotationPoint(85F, 7F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -1F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0.5F, -1F, 0F); // Box 114
		attachmentModel[17].setRotationPoint(86F, 8F, 1F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 115
		attachmentModel[18].setRotationPoint(48F, 8F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 116
		attachmentModel[19].setRotationPoint(24F, 8F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 117
		attachmentModel[20].setRotationPoint(28F, 8F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 118
		attachmentModel[21].setRotationPoint(32F, 8F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 119
		attachmentModel[22].setRotationPoint(36F, 8F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 120
		attachmentModel[23].setRotationPoint(40F, 8F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 121
		attachmentModel[24].setRotationPoint(44F, 8F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 123
		attachmentModel[25].setRotationPoint(6F, 4F, -5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 85, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 124
		attachmentModel[26].setRotationPoint(0F, 7F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 125
		attachmentModel[27].setRotationPoint(86F, 7F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 126
		attachmentModel[28].setRotationPoint(86F, 7F, 1F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 3, 6, 3, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 208
		attachmentModel[29].setRotationPoint(83F, -3F, 8F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 209
		attachmentModel[30].setRotationPoint(47F, -3F, 8F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 210
		attachmentModel[31].setRotationPoint(43F, -3F, 8F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 211
		attachmentModel[32].setRotationPoint(39F, -3F, 8F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 212
		attachmentModel[33].setRotationPoint(35F, -3F, 8F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 213
		attachmentModel[34].setRotationPoint(31F, -3F, 8F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 214
		attachmentModel[35].setRotationPoint(23F, -3F, 8F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 215
		attachmentModel[36].setRotationPoint(15F, -3F, 8F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 216
		attachmentModel[37].setRotationPoint(27F, -3F, 8F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 217
		attachmentModel[38].setRotationPoint(19F, -3F, 8F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 218
		attachmentModel[39].setRotationPoint(11F, -3F, 8F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 219
		attachmentModel[40].setRotationPoint(7F, -3F, 8F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 3, 6, 2, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 220
		attachmentModel[41].setRotationPoint(2F, -3F, 8F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 3, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, -2F, 0F, 0F, -2F); // Box 341
		attachmentModel[42].setRotationPoint(83F, -3F, -9F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 342
		attachmentModel[43].setRotationPoint(47F, -3F, -9F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 343
		attachmentModel[44].setRotationPoint(43F, -3F, -9F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 344
		attachmentModel[45].setRotationPoint(39F, -3F, -9F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 345
		attachmentModel[46].setRotationPoint(35F, -3F, -9F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 346
		attachmentModel[47].setRotationPoint(31F, -3F, -9F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 347
		attachmentModel[48].setRotationPoint(27F, -3F, -9F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 348
		attachmentModel[49].setRotationPoint(19F, -3F, -9F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 349
		attachmentModel[50].setRotationPoint(23F, -3F, -9F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 350
		attachmentModel[51].setRotationPoint(15F, -3F, -9F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 351
		attachmentModel[52].setRotationPoint(11F, -3F, -9F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 352
		attachmentModel[53].setRotationPoint(7F, -3F, -9F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 3, 6, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F); // Box 353
		attachmentModel[54].setRotationPoint(2F, -3F, -9F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 89, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 382
		attachmentModel[55].setRotationPoint(0F, -3F, -7F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 405
		attachmentModel[56].setRotationPoint(40F, -5.5F, 3.5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 406
		attachmentModel[57].setRotationPoint(40F, -4.5F, 4.5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 407
		attachmentModel[58].setRotationPoint(40F, -3.5F, 5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 410
		attachmentModel[59].setRotationPoint(87F, -5.5F, 3.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 411
		attachmentModel[60].setRotationPoint(87F, -3.5F, 5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 89, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[61].setRotationPoint(0F, 5F, 2F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 6, 4, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 439
		attachmentModel[62].setRotationPoint(6F, 4F, 3F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 89, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 440
		attachmentModel[63].setRotationPoint(0F, 6F, -3F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 89, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 441
		attachmentModel[64].setRotationPoint(0F, 6F, 2F);

		attachmentModel[65].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 416
		attachmentModel[65].setRotationPoint(9F, 6F, 5F);
		attachmentModel[65].rotateAngleZ = -0.78539816F;

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 2, 2, 10, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 417
		attachmentModel[66].setRotationPoint(8F, 5F, -5F);

		attachmentModel[67].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 418
		attachmentModel[67].setRotationPoint(9F, 6F, -6F);
		attachmentModel[67].rotateAngleZ = -0.78539816F;

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		attachmentModel[68].setRotationPoint(87F, -4.5F, 4.5F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 449
		attachmentModel[69].setRotationPoint(26F, -4.5F, 4.5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 450
		attachmentModel[70].setRotationPoint(26F, -5.5F, 3.5F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 451
		attachmentModel[71].setRotationPoint(26F, -3.5F, 5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 452
		attachmentModel[72].setRotationPoint(0F, -5.5F, 3.5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 453
		attachmentModel[73].setRotationPoint(0F, -4.5F, 4.5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 454
		attachmentModel[74].setRotationPoint(0F, -3.5F, 5F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 26, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 455
		attachmentModel[75].setRotationPoint(63F, -7F, 2F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[76].setRotationPoint(87F, -5.5F, -4.5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 457
		attachmentModel[77].setRotationPoint(87F, -4.5F, -5.5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 458
		attachmentModel[78].setRotationPoint(87F, -3.5F, -6F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 459
		attachmentModel[79].setRotationPoint(40F, -4.5F, -5.5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 460
		attachmentModel[80].setRotationPoint(40F, -3.5F, -6F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[81].setRotationPoint(40F, -5.5F, -4.5F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 462
		attachmentModel[82].setRotationPoint(26F, -5.5F, -4.5F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[83].setRotationPoint(26F, -4.5F, -5.5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 464
		attachmentModel[84].setRotationPoint(26F, -3.5F, -6F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 465
		attachmentModel[85].setRotationPoint(0F, -5.5F, -4.5F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 466
		attachmentModel[86].setRotationPoint(0F, -4.5F, -5.5F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 467
		attachmentModel[87].setRotationPoint(0F, -3.5F, -6F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 89, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 468
		attachmentModel[88].setRotationPoint(0F, 2F, -7F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 89, 1, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[89].setRotationPoint(0F, 2F, 5F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 89, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 470
		attachmentModel[90].setRotationPoint(0F, -3F, 5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[91].setRotationPoint(0F, -6F, -4F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 472
		attachmentModel[92].setRotationPoint(87F, 4.5F, 3.5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 473
		attachmentModel[93].setRotationPoint(87F, 3.5F, 4.5F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 474
		attachmentModel[94].setRotationPoint(87F, 2.5F, 5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 475
		attachmentModel[95].setRotationPoint(40F, 4.5F, 3.5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 476
		attachmentModel[96].setRotationPoint(40F, 3.5F, 4.5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 477
		attachmentModel[97].setRotationPoint(40F, 2.5F, 5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 478
		attachmentModel[98].setRotationPoint(26F, 4.5F, 3.5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 479
		attachmentModel[99].setRotationPoint(26F, 3.5F, 4.5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 480
		attachmentModel[100].setRotationPoint(26F, 2.5F, 5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 481
		attachmentModel[101].setRotationPoint(0F, 4.5F, 3.5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 482
		attachmentModel[102].setRotationPoint(0F, 3.5F, 4.5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 483
		attachmentModel[103].setRotationPoint(0F, 2.5F, 5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 484
		attachmentModel[104].setRotationPoint(0F, 2.5F, -6F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 485
		attachmentModel[105].setRotationPoint(0F, 3.5F, -5.5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 17, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 486
		attachmentModel[106].setRotationPoint(0F, 4.5F, -4.5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 487
		attachmentModel[107].setRotationPoint(26F, 4.5F, -4.5F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 488
		attachmentModel[108].setRotationPoint(26F, 3.5F, -5.5F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 489
		attachmentModel[109].setRotationPoint(26F, 2.5F, -6F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 490
		attachmentModel[110].setRotationPoint(40F, 4.5F, -4.5F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 491
		attachmentModel[111].setRotationPoint(40F, 3.5F, -5.5F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 492
		attachmentModel[112].setRotationPoint(40F, 2.5F, -6F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 493
		attachmentModel[113].setRotationPoint(87F, 4.5F, -4.5F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 494
		attachmentModel[114].setRotationPoint(87F, 3.5F, -5.5F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 495
		attachmentModel[115].setRotationPoint(87F, 2.5F, -6F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 459
		attachmentModel[116].setRotationPoint(54F, 4.5F, 3.5F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 460
		attachmentModel[117].setRotationPoint(54F, 3.5F, 4.5F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 461
		attachmentModel[118].setRotationPoint(54F, 2.5F, 5F);

		attachmentModel[119].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 462
		attachmentModel[119].setRotationPoint(54F, 2.5F, -6F);

		attachmentModel[120].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 463
		attachmentModel[120].setRotationPoint(54F, 3.5F, -5.5F);

		attachmentModel[121].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 464
		attachmentModel[121].setRotationPoint(54F, 4.5F, -4.5F);

		attachmentModel[122].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 465
		attachmentModel[122].setRotationPoint(76F, 2.5F, -6F);

		attachmentModel[123].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 466
		attachmentModel[123].setRotationPoint(76F, 3.5F, -5.5F);

		attachmentModel[124].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 467
		attachmentModel[124].setRotationPoint(76F, 4.5F, -4.5F);

		attachmentModel[125].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 468
		attachmentModel[125].setRotationPoint(76F, 2.5F, 5F);

		attachmentModel[126].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 469
		attachmentModel[126].setRotationPoint(76F, 3.5F, 4.5F);

		attachmentModel[127].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 470
		attachmentModel[127].setRotationPoint(76F, 4.5F, 3.5F);

		attachmentModel[128].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[128].setRotationPoint(54F, -5.5F, -4.5F);

		attachmentModel[129].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 472
		attachmentModel[129].setRotationPoint(54F, -4.5F, -5.5F);

		attachmentModel[130].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 473
		attachmentModel[130].setRotationPoint(54F, -3.5F, -6F);

		attachmentModel[131].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 474
		attachmentModel[131].setRotationPoint(54F, -5.5F, 3.5F);

		attachmentModel[132].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 475
		attachmentModel[132].setRotationPoint(54F, -4.5F, 4.5F);

		attachmentModel[133].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 476
		attachmentModel[133].setRotationPoint(54F, -3.5F, 5F);

		attachmentModel[134].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 477
		attachmentModel[134].setRotationPoint(64F, 2.5F, -6F);

		attachmentModel[135].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 478
		attachmentModel[135].setRotationPoint(64F, 3.5F, -5.5F);

		attachmentModel[136].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 479
		attachmentModel[136].setRotationPoint(64F, 4.5F, -4.5F);

		attachmentModel[137].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 480
		attachmentModel[137].setRotationPoint(64F, 2.5F, 5F);

		attachmentModel[138].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 481
		attachmentModel[138].setRotationPoint(64F, 3.5F, 4.5F);

		attachmentModel[139].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F); // Box 482
		attachmentModel[139].setRotationPoint(64F, 4.5F, 3.5F);

		attachmentModel[140].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 483
		attachmentModel[140].setRotationPoint(56F, 8F, -3F);

		attachmentModel[141].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 484
		attachmentModel[141].setRotationPoint(52F, 8F, -3F);

		attachmentModel[142].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 485
		attachmentModel[142].setRotationPoint(64F, 8F, -3F);

		attachmentModel[143].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 486
		attachmentModel[143].setRotationPoint(60F, 8F, -3F);

		attachmentModel[144].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 487
		attachmentModel[144].setRotationPoint(72F, 8F, -3F);

		attachmentModel[145].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 488
		attachmentModel[145].setRotationPoint(68F, 8F, -3F);

		attachmentModel[146].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 490
		attachmentModel[146].setRotationPoint(76F, 8F, -3F);

		attachmentModel[147].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 491
		attachmentModel[147].setRotationPoint(80F, 8F, -3F);

		attachmentModel[148].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 492
		attachmentModel[148].setRotationPoint(55F, -3F, -9F);

		attachmentModel[149].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 493
		attachmentModel[149].setRotationPoint(51F, -3F, -9F);

		attachmentModel[150].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 494
		attachmentModel[150].setRotationPoint(51F, -3F, 8F);

		attachmentModel[151].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 495
		attachmentModel[151].setRotationPoint(55F, -3F, 8F);

		attachmentModel[152].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 496
		attachmentModel[152].setRotationPoint(63F, -3F, -9F);

		attachmentModel[153].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 497
		attachmentModel[153].setRotationPoint(59F, -3F, -9F);

		attachmentModel[154].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 498
		attachmentModel[154].setRotationPoint(59F, -3F, 8F);

		attachmentModel[155].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 499
		attachmentModel[155].setRotationPoint(63F, -3F, 8F);

		attachmentModel[156].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 500
		attachmentModel[156].setRotationPoint(71F, -3F, -9F);

		attachmentModel[157].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 501
		attachmentModel[157].setRotationPoint(67F, -3F, -9F);

		attachmentModel[158].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 502
		attachmentModel[158].setRotationPoint(67F, -3F, 8F);

		attachmentModel[159].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 503
		attachmentModel[159].setRotationPoint(71F, -3F, 8F);

		attachmentModel[160].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 504
		attachmentModel[160].setRotationPoint(79F, -3F, -9F);

		attachmentModel[161].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 505
		attachmentModel[161].setRotationPoint(75F, -3F, -9F);

		attachmentModel[162].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 506
		attachmentModel[162].setRotationPoint(75F, -3F, 8F);

		attachmentModel[163].addShapeBox(0F, 0F, 0F, 2, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 507
		attachmentModel[163].setRotationPoint(79F, -3F, 8F);

		attachmentModel[164].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 512
		attachmentModel[164].setRotationPoint(63F, -4.5F, 4.5F);

		attachmentModel[165].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 513
		attachmentModel[165].setRotationPoint(62F, -3.5F, 5F);

		attachmentModel[166].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 514
		attachmentModel[166].setRotationPoint(63F, -5.5F, 3.5F);

		attachmentModel[167].addShapeBox(0F, 0F, 0F, 26, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 515
		attachmentModel[167].setRotationPoint(63F, -7F, -3F);

		attachmentModel[168].addShapeBox(0F, 0F, 0F, 26, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 516
		attachmentModel[168].setRotationPoint(63F, -8F, -3F);

		attachmentModel[169].addShapeBox(0F, 0F, 0F, 3, 2, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 2F, -1F, 0F, 2F, -1F, 0F, 0F, -1F, 0F); // Box 517
		attachmentModel[169].setRotationPoint(84F, -9F, -3F);

		attachmentModel[170].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 518
		attachmentModel[170].setRotationPoint(80F, -9F, -3F);

		attachmentModel[171].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 519
		attachmentModel[171].setRotationPoint(76F, -9F, -3F);

		attachmentModel[172].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 520
		attachmentModel[172].setRotationPoint(68F, -9F, -3F);

		attachmentModel[173].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 521
		attachmentModel[173].setRotationPoint(72F, -9F, -3F);

		attachmentModel[174].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 522
		attachmentModel[174].setRotationPoint(63F, -9F, -3F);

		attachmentModel[175].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 524
		attachmentModel[175].setRotationPoint(0F, -6F, 3F);

		attachmentModel[176].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 525
		attachmentModel[176].setRotationPoint(0F, -6F, 2F);

		attachmentModel[177].addShapeBox(0F, 0F, 0F, 55, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 526
		attachmentModel[177].setRotationPoint(0F, -6F, -3F);

		attachmentModel[178].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 527
		attachmentModel[178].setRotationPoint(76F, -3.5F, 5F);

		attachmentModel[179].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 528
		attachmentModel[179].setRotationPoint(76F, -4.5F, 4.5F);

		attachmentModel[180].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 529
		attachmentModel[180].setRotationPoint(76F, -5.5F, 3.5F);

		attachmentModel[181].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 530
		attachmentModel[181].setRotationPoint(76F, -5.5F, -4.5F);

		attachmentModel[182].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 531
		attachmentModel[182].setRotationPoint(76F, -4.5F, -5.5F);

		attachmentModel[183].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 532
		attachmentModel[183].setRotationPoint(76F, -3.5F, -6F);

		attachmentModel[184].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 533
		attachmentModel[184].setRotationPoint(63F, -4.5F, -5.5F);

		attachmentModel[185].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 534
		attachmentModel[185].setRotationPoint(63F, -5.5F, -4.5F);

		attachmentModel[186].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 535
		attachmentModel[186].setRotationPoint(62F, -3.5F, -6F);

		attachmentModel[187].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 187
		attachmentModel[187].setRotationPoint(84F, 8F, -3F);

		flipAll();
	}
}