//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC WTS-36 MK1 Handguard
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36MK1HandguardShell extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelCICWTS36MK1HandguardShell() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[54];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 427
		attachmentModel[1] = new ModelRendererTurbo(this, 97, 153, textureX, textureY); // Box 428
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 429
		attachmentModel[3] = new ModelRendererTurbo(this, 89, 161, textureX, textureY); // Box 430
		attachmentModel[4] = new ModelRendererTurbo(this, 161, 161, textureX, textureY); // Box 431
		attachmentModel[5] = new ModelRendererTurbo(this, 201, 153, textureX, textureY); // Box 432
		attachmentModel[6] = new ModelRendererTurbo(this, 225, 145, textureX, textureY); // Box 433
		attachmentModel[7] = new ModelRendererTurbo(this, 113, 113, textureX, textureY); // Box 434
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 169, textureX, textureY); // Box 435
		attachmentModel[9] = new ModelRendererTurbo(this, 137, 105, textureX, textureY); // Box 436
		attachmentModel[10] = new ModelRendererTurbo(this, 41, 113, textureX, textureY); // Box 437
		attachmentModel[11] = new ModelRendererTurbo(this, 25, 169, textureX, textureY); // Box 438
		attachmentModel[12] = new ModelRendererTurbo(this, 33, 169, textureX, textureY); // Box 439
		attachmentModel[13] = new ModelRendererTurbo(this, 225, 105, textureX, textureY); // Box 440
		attachmentModel[14] = new ModelRendererTurbo(this, 65, 169, textureX, textureY); // Box 441
		attachmentModel[15] = new ModelRendererTurbo(this, 89, 169, textureX, textureY); // Box 442
		attachmentModel[16] = new ModelRendererTurbo(this, 169, 169, textureX, textureY); // Box 443
		attachmentModel[17] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 444
		attachmentModel[18] = new ModelRendererTurbo(this, 89, 177, textureX, textureY); // Box 445
		attachmentModel[19] = new ModelRendererTurbo(this, 177, 177, textureX, textureY); // Box 446
		attachmentModel[20] = new ModelRendererTurbo(this, 25, 177, textureX, textureY); // Box 447
		attachmentModel[21] = new ModelRendererTurbo(this, 201, 177, textureX, textureY); // Box 448
		attachmentModel[22] = new ModelRendererTurbo(this, 225, 177, textureX, textureY); // Box 449
		attachmentModel[23] = new ModelRendererTurbo(this, 1, 185, textureX, textureY); // Box 450
		attachmentModel[24] = new ModelRendererTurbo(this, 65, 113, textureX, textureY); // Box 451
		attachmentModel[25] = new ModelRendererTurbo(this, 145, 121, textureX, textureY); // Box 452
		attachmentModel[26] = new ModelRendererTurbo(this, 41, 185, textureX, textureY); // Box 453
		attachmentModel[27] = new ModelRendererTurbo(this, 73, 185, textureX, textureY); // Box 454
		attachmentModel[28] = new ModelRendererTurbo(this, 105, 185, textureX, textureY); // Box 455
		attachmentModel[29] = new ModelRendererTurbo(this, 129, 185, textureX, textureY); // Box 456
		attachmentModel[30] = new ModelRendererTurbo(this, 153, 185, textureX, textureY); // Box 457
		attachmentModel[31] = new ModelRendererTurbo(this, 177, 185, textureX, textureY); // Box 458
		attachmentModel[32] = new ModelRendererTurbo(this, 201, 185, textureX, textureY); // Box 459
		attachmentModel[33] = new ModelRendererTurbo(this, 1, 193, textureX, textureY); // Box 460
		attachmentModel[34] = new ModelRendererTurbo(this, 113, 193, textureX, textureY); // Box 461
		attachmentModel[35] = new ModelRendererTurbo(this, 153, 193, textureX, textureY); // Box 462
		attachmentModel[36] = new ModelRendererTurbo(this, 225, 185, textureX, textureY); // Box 463
		attachmentModel[37] = new ModelRendererTurbo(this, 1, 201, textureX, textureY); // Box 464
		attachmentModel[38] = new ModelRendererTurbo(this, 89, 201, textureX, textureY); // Box 465
		attachmentModel[39] = new ModelRendererTurbo(this, 193, 113, textureX, textureY); // Box 466
		attachmentModel[40] = new ModelRendererTurbo(this, 177, 201, textureX, textureY); // Box 467
		attachmentModel[41] = new ModelRendererTurbo(this, 1, 209, textureX, textureY); // Box 468
		attachmentModel[42] = new ModelRendererTurbo(this, 81, 209, textureX, textureY); // Box 469
		attachmentModel[43] = new ModelRendererTurbo(this, 73, 113, textureX, textureY); // Box 470
		attachmentModel[44] = new ModelRendererTurbo(this, 161, 209, textureX, textureY); // Box 471
		attachmentModel[45] = new ModelRendererTurbo(this, 1, 217, textureX, textureY); // Box 472
		attachmentModel[46] = new ModelRendererTurbo(this, 105, 113, textureX, textureY); // Box 473
		attachmentModel[47] = new ModelRendererTurbo(this, 73, 233, textureX, textureY); // Box 435
		attachmentModel[48] = new ModelRendererTurbo(this, 169, 233, textureX, textureY); // Box 436
		attachmentModel[49] = new ModelRendererTurbo(this, 201, 233, textureX, textureY); // Box 437
		attachmentModel[50] = new ModelRendererTurbo(this, 1, 241, textureX, textureY); // Box 438
		attachmentModel[51] = new ModelRendererTurbo(this, 33, 241, textureX, textureY); // Box 439
		attachmentModel[52] = new ModelRendererTurbo(this, 65, 241, textureX, textureY); // Box 440
		attachmentModel[53] = new ModelRendererTurbo(this, 97, 241, textureX, textureY); // Box 441

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 42, 1, 5, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F); // Box 427
		attachmentModel[0].setRotationPoint(-6F, 1F, -6F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 42, 2, 1, 0F, 0F, -3F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, -3F, -1F, 0F, 3F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 2F, 0F); // Box 428
		attachmentModel[1].setRotationPoint(-6F, 0F, -7F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 40, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 429
		attachmentModel[2].setRotationPoint(-2F, -6F, -8F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 31, 1, 1, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F); // Box 430
		attachmentModel[3].setRotationPoint(7F, -7F, -8F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 37, 3, 1, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 431
		attachmentModel[4].setRotationPoint(7F, -12F, -7F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 18, 2, 1, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 432
		attachmentModel[5].setRotationPoint(-12F, -12F, -8F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 433
		attachmentModel[6].setRotationPoint(-2F, -7F, -8F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 434
		attachmentModel[7].setRotationPoint(6F, -12F, -8F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 3, 1, 12, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 435
		attachmentModel[8].setRotationPoint(-9F, 4F, -6F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 436
		attachmentModel[9].setRotationPoint(-10F, 4F, -6F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 437
		attachmentModel[10].setRotationPoint(-13F, 4F, -6F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 7, 2, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 438
		attachmentModel[11].setRotationPoint(-13F, 3F, -7F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 9, 1, 12, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 439
		attachmentModel[12].setRotationPoint(36F, 1F, -6F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F); // Box 440
		attachmentModel[13].setRotationPoint(36F, 0F, -8F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 7, 8, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 441
		attachmentModel[14].setRotationPoint(38F, -7F, -7F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 38, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 442
		attachmentModel[15].setRotationPoint(7F, -9F, -7F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 40, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 443
		attachmentModel[16].setRotationPoint(-2F, -4F, -8F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 444
		attachmentModel[17].setRotationPoint(36F, -3F, -8F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 42, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 1F, 0F); // Box 445
		attachmentModel[18].setRotationPoint(-6F, -3F, -8F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 7, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 446
		attachmentModel[19].setRotationPoint(-13F, -3F, -8F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 7, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 447
		attachmentModel[20].setRotationPoint(-13F, -4F, -8F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 7, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		attachmentModel[21].setRotationPoint(-13F, -6F, -8F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 7, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 449
		attachmentModel[22].setRotationPoint(-13F, -7F, -8F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 450
		attachmentModel[23].setRotationPoint(-13F, -10F, -8F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 451
		attachmentModel[24].setRotationPoint(-10F, 4F, 4F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 452
		attachmentModel[25].setRotationPoint(-13F, 4F, 4F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 7, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 453
		attachmentModel[26].setRotationPoint(36F, 0F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 12, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 454
		attachmentModel[27].setRotationPoint(-6F, 0F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 7, 2, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 455
		attachmentModel[28].setRotationPoint(-13F, 3F, 6F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 7, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[29].setRotationPoint(-13F, -3F, 7F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 7, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 457
		attachmentModel[30].setRotationPoint(-13F, -4F, 7F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 7, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 458
		attachmentModel[31].setRotationPoint(-13F, -6F, 7F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 7, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 459
		attachmentModel[32].setRotationPoint(-13F, -7F, 7F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 460
		attachmentModel[33].setRotationPoint(-13F, -10F, 7F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 18, 2, 1, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 461
		attachmentModel[34].setRotationPoint(-12F, -12F, 7F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 42, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 1F, 0F); // Box 462
		attachmentModel[35].setRotationPoint(-6F, -3F, 7F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[36].setRotationPoint(-2F, -7F, 7F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 40, 2, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 464
		attachmentModel[37].setRotationPoint(-2F, -6F, 7F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 40, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 465
		attachmentModel[38].setRotationPoint(-2F, -4F, 7F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 466
		attachmentModel[39].setRotationPoint(6F, -12F, 7F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 31, 1, 1, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 467
		attachmentModel[40].setRotationPoint(7F, -7F, 7F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 38, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 468
		attachmentModel[41].setRotationPoint(7F, -9F, 6F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 37, 3, 1, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[42].setRotationPoint(7F, -12F, 6F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 470
		attachmentModel[43].setRotationPoint(36F, -3F, 7F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 7, 8, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[44].setRotationPoint(38F, -7F, 6F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 42, 2, 1, 0F, 0F, -3F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, -3F, 1F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 3F, -1F); // Box 472
		attachmentModel[45].setRotationPoint(-6F, 0F, 6F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 473
		attachmentModel[46].setRotationPoint(36F, 0F, 7F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 42, 1, 5, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F); // Box 435
		attachmentModel[47].setRotationPoint(-6F, 1F, 1F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 14, 1, 1, 0F, 0F, -1F, 0F, 0F, 0F, 0F, -1F, -0.0476F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -1F, 0.0476F, 0F, 0F, 1F, 0F); // Box 436
		attachmentModel[48].setRotationPoint(-6F, 3F, -1F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 14, 1, 1, 0F, 0F, -1F, 0F, -1F, -0.0476F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, -1F, 0.0476F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 437
		attachmentModel[49].setRotationPoint(-6F, 3F, 0F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 14, 1, 1, 0F, -1F, 0.0476F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -1F, -0.0476F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 438
		attachmentModel[50].setRotationPoint(22F, 2F, 0F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 14, 1, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, -1F, 0.0476F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, -0.0476F, 0F); // Box 439
		attachmentModel[51].setRotationPoint(22F, 2F, -1F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 13, 1, 1, 0F, -1F, -0.9524F, 0F, 0F, -0.0476F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, -1F, 0.9524F, 0F, 0F, 0.0476F, 0F, 1F, 0F, 0F, 0F, 1F, 0F); // Box 440
		attachmentModel[52].setRotationPoint(8F, 2F, 0F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 13, 1, 1, 0F, 0F, -1F, 0F, 1F, 0F, 0F, 0F, -0.0476F, 0F, -1F, -0.9524F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 0F, 0.0476F, 0F, -1F, 0.9524F, 0F); // Box 441
		attachmentModel[53].setRotationPoint(8F, 2F, -1F);
	}
}