//This File was created with the Minecraft-SMP Modelling Toolbox *******
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: G33
// Model Creator: 
// Created on: 27.10.2019 - 13:16:05
// Last changed on: 27.10.2019 - 13:16:05

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelG33Flipped extends ModelAttachment //Same as Filename
{
	int textureX = 128;
	int textureY = 128;

	public ModelG33Flipped() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[92];
		attachmentModel[0] = new ModelRendererTurbo(this, 113, 17, textureX, textureY); // Box 89
		attachmentModel[1] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 90
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 91
		attachmentModel[3] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 92
		attachmentModel[4] = new ModelRendererTurbo(this, 81, 25, textureX, textureY); // Box 93
		attachmentModel[5] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 94
		attachmentModel[6] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 95
		attachmentModel[7] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 96
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 97
		attachmentModel[9] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 98
		attachmentModel[10] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 99
		attachmentModel[11] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 100
		attachmentModel[12] = new ModelRendererTurbo(this, 65, 33, textureX, textureY); // Box 101
		attachmentModel[13] = new ModelRendererTurbo(this, 33, 88, textureX, textureY); // Box 102
		attachmentModel[14] = new ModelRendererTurbo(this, 49, 49, textureX, textureY); // Box 103
		attachmentModel[15] = new ModelRendererTurbo(this, 49, 97, textureX, textureY); // Box 104
		attachmentModel[16] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 105
		attachmentModel[17] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 106
		attachmentModel[18] = new ModelRendererTurbo(this, 49, 105, textureX, textureY); // Box 107
		attachmentModel[19] = new ModelRendererTurbo(this, 33, 65, textureX, textureY); // Box 108
		attachmentModel[20] = new ModelRendererTurbo(this, 73, 65, textureX, textureY); // Box 109
		attachmentModel[21] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 110
		attachmentModel[22] = new ModelRendererTurbo(this, 33, 81, textureX, textureY); // Box 111
		attachmentModel[23] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 112
		attachmentModel[24] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 113
		attachmentModel[25] = new ModelRendererTurbo(this, 113, 89, textureX, textureY); // Box 114
		attachmentModel[26] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 115
		attachmentModel[27] = new ModelRendererTurbo(this, 49, 73, textureX, textureY); // Box 116
		attachmentModel[28] = new ModelRendererTurbo(this, 41, 73, textureX, textureY); // Box 117
		attachmentModel[29] = new ModelRendererTurbo(this, 113, 65, textureX, textureY); // Box 118
		attachmentModel[30] = new ModelRendererTurbo(this, 113, 57, textureX, textureY); // Box 119
		attachmentModel[31] = new ModelRendererTurbo(this, 120, 57, textureX, textureY); // Box 120
		attachmentModel[32] = new ModelRendererTurbo(this, 25, 113, textureX, textureY); // Box 121
		attachmentModel[33] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 122
		attachmentModel[34] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 123
		attachmentModel[35] = new ModelRendererTurbo(this, 97, 73, textureX, textureY); // Box 124
		attachmentModel[36] = new ModelRendererTurbo(this, 65, 73, textureX, textureY); // Box 125
		attachmentModel[37] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 126
		attachmentModel[38] = new ModelRendererTurbo(this, 121, 81, textureX, textureY); // Box 127
		attachmentModel[39] = new ModelRendererTurbo(this, 73, 57, textureX, textureY); // Box 128
		attachmentModel[40] = new ModelRendererTurbo(this, 97, 81, textureX, textureY); // Box 129
		attachmentModel[41] = new ModelRendererTurbo(this, 105, 57, textureX, textureY); // Box 130
		attachmentModel[42] = new ModelRendererTurbo(this, 105, 81, textureX, textureY); // Box 131
		attachmentModel[43] = new ModelRendererTurbo(this, 113, 81, textureX, textureY); // Box 132
		attachmentModel[44] = new ModelRendererTurbo(this, 57, 81, textureX, textureY); // Box 133
		attachmentModel[45] = new ModelRendererTurbo(this, 41, 81, textureX, textureY); // Box 134
		attachmentModel[46] = new ModelRendererTurbo(this, 49, 81, textureX, textureY); // Box 135
		attachmentModel[47] = new ModelRendererTurbo(this, 89, 105, textureX, textureY); // Box 136
		attachmentModel[48] = new ModelRendererTurbo(this, 81, 41, textureX, textureY); // Box 137
		attachmentModel[49] = new ModelRendererTurbo(this, 49, 57, textureX, textureY); // Box 138
		attachmentModel[50] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 139
		attachmentModel[51] = new ModelRendererTurbo(this, 81, 57, textureX, textureY); // Box 140
		attachmentModel[52] = new ModelRendererTurbo(this, 121, 25, textureX, textureY); // Box 141
		attachmentModel[53] = new ModelRendererTurbo(this, 79, 17, textureX, textureY); // Box 142
		attachmentModel[54] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 143
		attachmentModel[55] = new ModelRendererTurbo(this, 89, 81, textureX, textureY); // Box 144
		attachmentModel[56] = new ModelRendererTurbo(this, 105, 49, textureX, textureY); // Box 145
		attachmentModel[57] = new ModelRendererTurbo(this, 65, 81, textureX, textureY); // Box 146
		attachmentModel[58] = new ModelRendererTurbo(this, 97, 49, textureX, textureY); // Box 147
		attachmentModel[59] = new ModelRendererTurbo(this, 121, 49, textureX, textureY); // Box 148
		attachmentModel[60] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 149
		attachmentModel[61] = new ModelRendererTurbo(this, 81, 81, textureX, textureY); // Box 150
		attachmentModel[62] = new ModelRendererTurbo(this, 88, 16, textureX, textureY); // Box 151
		attachmentModel[63] = new ModelRendererTurbo(this, 81, 9, textureX, textureY); // Box 152
		attachmentModel[64] = new ModelRendererTurbo(this, 41, 17, textureX, textureY); // Box 153
		attachmentModel[65] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 154
		attachmentModel[66] = new ModelRendererTurbo(this, 97, 9, textureX, textureY); // Box 155
		attachmentModel[67] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 156
		attachmentModel[68] = new ModelRendererTurbo(this, 17, 17, textureX, textureY); // Box 157
		attachmentModel[69] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 158
		attachmentModel[70] = new ModelRendererTurbo(this, 65, 9, textureX, textureY); // Box 159
		attachmentModel[71] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 160
		attachmentModel[72] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 161
		attachmentModel[73] = new ModelRendererTurbo(this, 70, 16, textureX, textureY); // Box 162
		attachmentModel[74] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 163
		attachmentModel[75] = new ModelRendererTurbo(this, 60, 1, textureX, textureY); // Box 164
		attachmentModel[76] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 165
		attachmentModel[77] = new ModelRendererTurbo(this, 81, 1, textureX, textureY); // Box 166
		attachmentModel[78] = new ModelRendererTurbo(this, 71, 1, textureX, textureY); // Box 167
		attachmentModel[79] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 168
		attachmentModel[80] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 169
		attachmentModel[81] = new ModelRendererTurbo(this, 105, 1, textureX, textureY); // Box 170
		attachmentModel[82] = new ModelRendererTurbo(this, 97, 1, textureX, textureY); // Box 171
		attachmentModel[83] = new ModelRendererTurbo(this, 121, 1, textureX, textureY); // Box 172
		attachmentModel[84] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 173
		attachmentModel[85] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 174
		attachmentModel[86] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 175
		attachmentModel[87] = new ModelRendererTurbo(this, 41, 9, textureX, textureY); // Box 176
		attachmentModel[88] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 177
		attachmentModel[89] = new ModelRendererTurbo(this, 39, 107, textureX, textureY); // Box 178
		attachmentModel[90] = new ModelRendererTurbo(this, 23, 89, textureX, textureY); // Box 179
		attachmentModel[91] = new ModelRendererTurbo(this, 77, 105, textureX, textureY); // Box 180

		attachmentModel[0].addShapeBox(0F, -1.5F, -0.5F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 89
		attachmentModel[0].setRotationPoint(-0.5F, -3.5F, -3.5F);
		attachmentModel[0].rotateAngleX = 1.57079633F;

		attachmentModel[1].addShapeBox(0F, -0.5F, -1.5F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 90
		attachmentModel[1].setRotationPoint(-0.5F, -3.5F, -3.5F);
		attachmentModel[1].rotateAngleX = 1.57079633F;

		attachmentModel[2].addShapeBox(0F, 0.5F, -0.5F, 3, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 91
		attachmentModel[2].setRotationPoint(-0.5F, -3.5F, -3.5F);
		attachmentModel[2].rotateAngleX = 1.57079633F;

		attachmentModel[3].addShapeBox(0F, -4F, 1.5F, 11, 5, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 92
		attachmentModel[3].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[3].rotateAngleX = 1.57079633F;

		attachmentModel[4].addShapeBox(0F, -4F, 2F, 11, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 93
		attachmentModel[4].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[4].rotateAngleX = 1.57079633F;

		attachmentModel[5].addShapeBox(0F, -4F, 4.5F, 11, 5, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 94
		attachmentModel[5].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[5].rotateAngleX = 1.57079633F;

		attachmentModel[6].addShapeBox(0F, -5F, 6F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 0.5F, 0F, -1.5F, 0.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F); // Box 95
		attachmentModel[6].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[6].rotateAngleX = 1.57079633F;

		attachmentModel[7].addShapeBox(0F, -8F, 6F, 11, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 96
		attachmentModel[7].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[7].rotateAngleX = 1.57079633F;

		attachmentModel[8].addShapeBox(0F, -10F, 6F, 11, 2, 1, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 97
		attachmentModel[8].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[8].rotateAngleX = 1.57079633F;

		attachmentModel[9].addShapeBox(0F, -10F, 2F, 11, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 98
		attachmentModel[9].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[9].rotateAngleX = 1.57079633F;

		attachmentModel[10].addShapeBox(0F, -9F, 1F, 1, 5, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 99
		attachmentModel[10].setRotationPoint(5F, -3.5F, -3.5F);
		attachmentModel[10].rotateAngleX = 1.57079633F;

		attachmentModel[11].addShapeBox(0F, -10F, 0F, 11, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 100
		attachmentModel[11].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[11].rotateAngleX = 1.57079633F;

		attachmentModel[12].addShapeBox(0F, -8F, 0F, 11, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 101
		attachmentModel[12].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[12].rotateAngleX = 1.57079633F;

		attachmentModel[13].addShapeBox(0F, -5F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -1.5F, 0.5F, 0F, -1.5F, 0.5F); // Box 102
		attachmentModel[13].setRotationPoint(-4.5F, -3.5F, -3.5F);
		attachmentModel[13].rotateAngleX = 1.57079633F;

		attachmentModel[14].addShapeBox(0F, -8F, 7F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 103
		attachmentModel[14].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[14].rotateAngleX = 1.57079633F;

		attachmentModel[15].addShapeBox(0F, -11F, 7F, 19, 3, 1, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 104
		attachmentModel[15].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[15].rotateAngleX = 1.57079633F;

		attachmentModel[16].addShapeBox(0F, -11F, 2F, 19, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 105
		attachmentModel[16].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[16].rotateAngleX = 1.57079633F;

		attachmentModel[17].addShapeBox(0F, -11F, -1F, 19, 3, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 106
		attachmentModel[17].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[17].rotateAngleX = 1.57079633F;

		attachmentModel[18].addShapeBox(0F, -12F, -2F, 15, 4, 1, 0F, 0.5F, 0F, -3F, 0F, 0F, -3F, 1F, -1F, 3F, 0.5F, -1F, 3F, 0.5F, -1F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F); // Box 107
		attachmentModel[18].setRotationPoint(-16.5F, -3.5F, -3.5F);
		attachmentModel[18].rotateAngleX = 1.57079633F;

		attachmentModel[19].addShapeBox(0F, -12F, 8F, 15, 4, 1, 0F, 0.5F, -1F, 3F, 1F, -1F, 3F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F); // Box 108
		attachmentModel[19].setRotationPoint(-16.5F, -3.5F, -3.5F);
		attachmentModel[19].rotateAngleX = 1.57079633F;

		attachmentModel[20].addShapeBox(0F, -9F, 8F, 15, 5, 1, 0F, 0.5F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 109
		attachmentModel[20].setRotationPoint(-16.5F, -3.5F, -3.5F);
		attachmentModel[20].rotateAngleX = 1.57079633F;

		attachmentModel[21].addShapeBox(0F, -8F, -1F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 110
		attachmentModel[21].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[21].rotateAngleX = 1.57079633F;

		attachmentModel[22].addShapeBox(0F, -8F, -2F, 1, 3, 1, 0F, 0.5F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 111
		attachmentModel[22].setRotationPoint(-1.5F, -3.5F, -3.5F);
		attachmentModel[22].rotateAngleX = 1.57079633F;

		attachmentModel[23].addShapeBox(0F, -5F, -1F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F); // Box 112
		attachmentModel[23].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[23].rotateAngleX = 1.57079633F;

		attachmentModel[24].addShapeBox(0F, -5F, 7F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 113
		attachmentModel[24].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[24].rotateAngleX = 1.57079633F;

		attachmentModel[25].addShapeBox(0F, -5F, 8F, 2, 4, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 3F, 0.5F, -1F, 3F, -0.5F, 0F, -3F, 0F, 0F, -3F); // Box 114
		attachmentModel[25].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[25].rotateAngleX = 1.57079633F;

		attachmentModel[26].addShapeBox(0F, -3F, 7F, 1, 1, 1, 0F, -0.5F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -1F, -1.5F, 0.5F, 0F, -1.75F, 0.25F, 0F, -0.75F, -0.25F, -1F, -0.5F, -0.5F); // Box 115
		attachmentModel[26].setRotationPoint(-4F, -3.5F, -3.5F);
		attachmentModel[26].rotateAngleX = 1.57079633F;

		attachmentModel[27].addShapeBox(0F, -5F, 7F, 4, 2, 2, 0F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F); // Box 116
		attachmentModel[27].setRotationPoint(-7.5F, -3.5F, -3.5F);
		attachmentModel[27].rotateAngleX = 1.57079633F;

		attachmentModel[28].addShapeBox(0F, -3F, 5F, 1, 1, 1, 0F, -1F, -0.5F, -0.5F, 0F, -1F, 0F, 0F, -2F, 0F, -1F, -1.5F, 0.5F, -0.5F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 1F, 0F, -0.5F, 1F, 0F); // Box 117
		attachmentModel[28].setRotationPoint(-4F, -3.5F, -3.5F);
		attachmentModel[28].rotateAngleX = 1.57079633F;

		attachmentModel[29].addShapeBox(0F, -3.5F, 7F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.7071F, -0.2929F, 0F, -0.7071F, -0.2929F, 0F, -0.5858F, 1.4142F, 0F, -0.5858F, 1.4142F, 0F, 0.1213F, -1.7071F, 0F, 0.1213F, -1.7071F); // Box 118
		attachmentModel[29].setRotationPoint(-5.5F, -3.5F, -3.5F);
		attachmentModel[29].rotateAngleX = 1.57079633F;

		attachmentModel[30].addShapeBox(0F, -3F, 7F, 2, 1, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 0.5F, -0.5F, -1.5F, 0.5F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 119
		attachmentModel[30].setRotationPoint(-7.5F, -3.5F, -3.5F);
		attachmentModel[30].rotateAngleX = 1.57079633F;

		attachmentModel[31].addShapeBox(0F, -3F, 5F, 2, 1, 1, 0F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -1.5F, 0.5F, 0F, -1.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 120
		attachmentModel[31].setRotationPoint(-7.5F, -3.5F, -3.5F);
		attachmentModel[31].rotateAngleX = 1.57079633F;

		attachmentModel[32].addShapeBox(0F, -5F, 5F, 4, 3, 1, 0F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, -0.5F, 2.5F, 0F, -0.5F, 2.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F); // Box 121
		attachmentModel[32].setRotationPoint(-7.5F, -3.5F, -3.5F);
		attachmentModel[32].rotateAngleX = 1.57079633F;

		attachmentModel[33].addShapeBox(0F, -5F, 8F, 9, 4, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 3F, 0F, -1F, 3F, 0F, 0F, -3F, 0.5F, 0F, -3F); // Box 122
		attachmentModel[33].setRotationPoint(-16.5F, -3.5F, -3.5F);
		attachmentModel[33].rotateAngleX = 1.57079633F;

		attachmentModel[34].addShapeBox(0F, -2F, 1F, 16, 1, 5, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0.5F, 0F, 0F); // Box 123
		attachmentModel[34].setRotationPoint(-16.5F, -3.5F, -3.5F);
		attachmentModel[34].rotateAngleX = 1.57079633F;

		attachmentModel[35].addShapeBox(0F, -9F, -2F, 11, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 124
		attachmentModel[35].setRotationPoint(-17F, -3.5F, -3.5F);
		attachmentModel[35].rotateAngleX = 1.57079633F;

		attachmentModel[36].addShapeBox(0F, -8F, -2F, 11, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F); // Box 125
		attachmentModel[36].setRotationPoint(-17F, -3.5F, -3.5F);
		attachmentModel[36].rotateAngleX = 1.57079633F;

		attachmentModel[37].addShapeBox(0F, -5F, -2F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F); // Box 126
		attachmentModel[37].setRotationPoint(-17F, -3.5F, -3.5F);
		attachmentModel[37].rotateAngleX = 1.57079633F;

		attachmentModel[38].addShapeBox(0F, -8F, -4.5F, 1, 1, 1, 0F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.25F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, 0F, 0.5F, 0.25F, 0F); // Box 127
		attachmentModel[38].setRotationPoint(-5.5F, -3.5F, -3.5F);
		attachmentModel[38].rotateAngleX = 1.57079633F;

		attachmentModel[39].addShapeBox(0F, -8.5F, -3.5F, 2, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 128
		attachmentModel[39].setRotationPoint(-5F, -3.5F, -3.5F);
		attachmentModel[39].rotateAngleX = 1.57079633F;

		attachmentModel[40].addShapeBox(0F, -8F, -4.5F, 1, 1, 1, 0F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0.25F, 0.5F, 0F, 0.25F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, 0.5F, 0.25F, 0F, 0.25F, 0.25F, 0F); // Box 129
		attachmentModel[40].setRotationPoint(-3.5F, -3.5F, -3.5F);
		attachmentModel[40].rotateAngleX = 1.57079633F;

		attachmentModel[41].addShapeBox(0F, -7.5F, -3.5F, 1, 2, 3, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 130
		attachmentModel[41].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[41].rotateAngleX = 1.57079633F;

		attachmentModel[42].addShapeBox(0F, -6F, -4.5F, 1, 1, 1, 0F, 0.25F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, 0.5F, 0.25F, 0F, 0.25F, 0.25F, 0F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0.25F, 0.5F, 0F); // Box 131
		attachmentModel[42].setRotationPoint(-3.5F, -3.5F, -3.5F);
		attachmentModel[42].rotateAngleX = 1.57079633F;

		attachmentModel[43].addShapeBox(0F, -6F, -4.5F, 1, 1, 1, 0F, -0.5F, 0.25F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, 0F, 0.5F, 0.25F, 0F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0.5F, 0F, 0F, 0F, 0F); // Box 132
		attachmentModel[43].setRotationPoint(-5.5F, -3.5F, -3.5F);
		attachmentModel[43].rotateAngleX = 1.57079633F;

		attachmentModel[44].addShapeBox(0F, -7.5F, -3.5F, 1, 2, 3, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F); // Box 133
		attachmentModel[44].setRotationPoint(-6F, -3.5F, -3.5F);
		attachmentModel[44].rotateAngleX = 1.57079633F;

		attachmentModel[45].addShapeBox(0F, -9F, -2F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F); // Box 134
		attachmentModel[45].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[45].rotateAngleX = 1.57079633F;

		attachmentModel[46].addShapeBox(0F, -5F, -2F, 1, 1, 1, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 135
		attachmentModel[46].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[46].rotateAngleX = 1.57079633F;

		attachmentModel[47].addShapeBox(0F, -5F, -2F, 15, 4, 1, 0F, 0.5F, -1F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -3F, 0F, 0F, -3F, 1F, -1F, 3F, 0.5F, -1F, 3F); // Box 136
		attachmentModel[47].setRotationPoint(-16.5F, -3.5F, -3.5F);
		attachmentModel[47].rotateAngleX = 1.57079633F;

		attachmentModel[48].addShapeBox(0F, -3F, 2F, 19, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 137
		attachmentModel[48].setRotationPoint(-19.5F, -3.5F, -3.5F);
		attachmentModel[48].rotateAngleX = 1.57079633F;

		attachmentModel[49].addShapeBox(0F, -12F, 5F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -1F); // Box 138
		attachmentModel[49].setRotationPoint(-17F, -3.5F, -3.5F);
		attachmentModel[49].rotateAngleX = 1.57079633F;

		attachmentModel[50].addShapeBox(0F, -12F, 2F, 11, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 139
		attachmentModel[50].setRotationPoint(-17F, -3.5F, -3.5F);
		attachmentModel[50].rotateAngleX = 1.57079633F;

		attachmentModel[51].addShapeBox(0F, -12F, 1F, 11, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 140
		attachmentModel[51].setRotationPoint(-17F, -3.5F, -3.5F);
		attachmentModel[51].rotateAngleX = 1.57079633F;

		attachmentModel[52].addShapeBox(0F, -12F, 5F, 1, 1, 1, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 141
		attachmentModel[52].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[52].rotateAngleX = 1.57079633F;

		attachmentModel[53].addShapeBox(0F, -12F, 1F, 1, 1, 5, 0F, 0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0.5F, 0F, -1F); // Box 142
		attachmentModel[53].setRotationPoint(-1.5F, -3.5F, -3.5F);
		attachmentModel[53].rotateAngleX = 1.57079633F;

		attachmentModel[54].addShapeBox(0F, -12F, 1F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 143
		attachmentModel[54].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[54].rotateAngleX = 1.57079633F;

		attachmentModel[55].addShapeBox(0F, -14.5F, 2F, 1, 1, 1, 0F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0.25F, 0.25F, 0F, 0.25F); // Box 144
		attachmentModel[55].setRotationPoint(-3.5F, -3.5F, -3.5F);
		attachmentModel[55].rotateAngleX = 1.57079633F;

		attachmentModel[56].addShapeBox(0F, -13.5F, 1.5F, 2, 3, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 145
		attachmentModel[56].setRotationPoint(-5F, -3.5F, -3.5F);
		attachmentModel[56].rotateAngleX = 1.57079633F;

		attachmentModel[57].addShapeBox(0F, -14.5F, 4F, 1, 1, 1, 0F, 0.25F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0F, 0.25F, 0.5F, 0F, 0.25F, 0F, 0F, 0F, 0.25F, 0F, 0.5F); // Box 146
		attachmentModel[57].setRotationPoint(-3.5F, -3.5F, -3.5F);
		attachmentModel[57].rotateAngleX = 1.57079633F;

		attachmentModel[58].addShapeBox(0F, -13.5F, 2.5F, 1, 3, 2, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 147
		attachmentModel[58].setRotationPoint(-3F, -3.5F, -3.5F);
		attachmentModel[58].rotateAngleX = 1.57079633F;

		attachmentModel[59].addShapeBox(0F, -13.5F, 2.5F, 1, 3, 2, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 148
		attachmentModel[59].setRotationPoint(-6F, -3.5F, -3.5F);
		attachmentModel[59].rotateAngleX = 1.57079633F;

		attachmentModel[60].addShapeBox(0F, -14.5F, 4F, 1, 1, 1, 0F, -0.5F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0.5F, 0F, 0.25F, 0.25F, 0F, 0.25F, 0.25F, 0F, 0.5F, 0F, 0F, 0F); // Box 149
		attachmentModel[60].setRotationPoint(-5.5F, -3.5F, -3.5F);
		attachmentModel[60].rotateAngleX = 1.57079633F;

		attachmentModel[61].addShapeBox(0F, -14.5F, 2F, 1, 1, 1, 0F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0F, 0F, 0F, 0.25F, 0F, 0.5F, 0.25F, 0F, 0.25F, 0.5F, 0F, 0.25F); // Box 150
		attachmentModel[61].setRotationPoint(-5.5F, -3.5F, -3.5F);
		attachmentModel[61].rotateAngleX = 1.57079633F;

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 151
		attachmentModel[62].setRotationPoint(-3.5F, -5F, -4F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 152
		attachmentModel[63].setRotationPoint(-3.5F, -3F, -4F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 153
		attachmentModel[64].setRotationPoint(-3.5F, -4F, -5F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 154
		attachmentModel[65].setRotationPoint(-3.5F, -2F, -4F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 155
		attachmentModel[66].setRotationPoint(-3.5F, -1F, -4F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F); // Box 156
		attachmentModel[67].setRotationPoint(0F, 0F, -4.5F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 157
		attachmentModel[68].setRotationPoint(-0.5F, -1F, -4F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 158
		attachmentModel[69].setRotationPoint(2.5F, -1F, -4F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 159
		attachmentModel[70].setRotationPoint(2.5F, -2F, -4F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 160
		attachmentModel[71].setRotationPoint(2.5F, -3F, -4F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 161
		attachmentModel[72].setRotationPoint(2.5F, -4F, -5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 162
		attachmentModel[73].setRotationPoint(2.5F, -5F, -4F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 9, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 163
		attachmentModel[74].setRotationPoint(-3.5F, -1F, -3F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0.5F); // Box 164
		attachmentModel[75].setRotationPoint(2.5F, -1F, 4F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 165
		attachmentModel[76].setRotationPoint(2.5F, -1F, 3F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 166
		attachmentModel[77].setRotationPoint(2.5F, -0.5F, 5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F); // Box 167
		attachmentModel[78].setRotationPoint(2.5F, 1F, 4F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 168
		attachmentModel[79].setRotationPoint(1.5F, -1F, 5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 169
		attachmentModel[80].setRotationPoint(0.5F, -1F, 5F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 170
		attachmentModel[81].setRotationPoint(0.5F, 0F, 5F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 171
		attachmentModel[82].setRotationPoint(1.5F, 1F, 5F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 172
		attachmentModel[83].setRotationPoint(0.5F, 1F, 5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 173
		attachmentModel[84].setRotationPoint(0.5F, -1F, 3F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 6, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 174
		attachmentModel[85].setRotationPoint(-3.5F, -1F, 4F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 175
		attachmentModel[86].setRotationPoint(-6.5F, -1F, 4F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 176
		attachmentModel[87].setRotationPoint(-7.5F, -1F, 5F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 177
		attachmentModel[88].setRotationPoint(-3.5F, -1F, 3F);

		attachmentModel[89].addShapeBox(0F, -10F, 1F, 1, 1, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 178
		attachmentModel[89].setRotationPoint(-18.5F, -3.5F, -3.5F);
		attachmentModel[89].rotateAngleX = 1.57079633F;

		attachmentModel[90].addShapeBox(0F, -4F, 1F, 1, 1, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 179
		attachmentModel[90].setRotationPoint(-18.5F, -3.5F, -3.5F);
		attachmentModel[90].rotateAngleX = 1.57079633F;

		attachmentModel[91].addShapeBox(0F, -9F, 0F, 1, 5, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 180
		attachmentModel[91].setRotationPoint(-18.5F, -3.5F, -3.5F);
		attachmentModel[91].rotateAngleX = 1.57079633F;

		flipAll();
	}
}