//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: PSO-1 4x24 Scope
// Model Creator: 
// Created on: 22.12.2019 - 13:22:03
// Last changed on: 22.12.2019 - 13:22:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelPSO14x24ScopeEPR extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelPSO14x24ScopeEPR() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[26];
		attachmentModel[0] = new ModelRendererTurbo(this, 209, 81, textureX, textureY); // Box 115
		attachmentModel[1] = new ModelRendererTurbo(this, 225, 81, textureX, textureY); // Box 116
		attachmentModel[2] = new ModelRendererTurbo(this, 233, 81, textureX, textureY); // Box 117
		attachmentModel[3] = new ModelRendererTurbo(this, 241, 81, textureX, textureY); // Box 118
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 119
		attachmentModel[5] = new ModelRendererTurbo(this, 17, 89, textureX, textureY); // Box 120
		attachmentModel[6] = new ModelRendererTurbo(this, 25, 89, textureX, textureY); // Box 121
		attachmentModel[7] = new ModelRendererTurbo(this, 41, 89, textureX, textureY); // Box 122
		attachmentModel[8] = new ModelRendererTurbo(this, 49, 89, textureX, textureY); // Box 127
		attachmentModel[9] = new ModelRendererTurbo(this, 65, 89, textureX, textureY); // Box 128
		attachmentModel[10] = new ModelRendererTurbo(this, 81, 89, textureX, textureY); // Box 129
		attachmentModel[11] = new ModelRendererTurbo(this, 97, 89, textureX, textureY); // Box 130
		attachmentModel[12] = new ModelRendererTurbo(this, 185, 89, textureX, textureY); // Box 131
		attachmentModel[13] = new ModelRendererTurbo(this, 209, 89, textureX, textureY); // Box 132
		attachmentModel[14] = new ModelRendererTurbo(this, 105, 89, textureX, textureY); // Box 133
		attachmentModel[15] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 134
		attachmentModel[16] = new ModelRendererTurbo(this, 25, 97, textureX, textureY); // Box 135
		attachmentModel[17] = new ModelRendererTurbo(this, 49, 97, textureX, textureY); // Box 136
		attachmentModel[18] = new ModelRendererTurbo(this, 201, 81, textureX, textureY); // Box 137
		attachmentModel[19] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 138
		attachmentModel[20] = new ModelRendererTurbo(this, 121, 89, textureX, textureY); // Box 139
		attachmentModel[21] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 140
		attachmentModel[22] = new ModelRendererTurbo(this, 129, 89, textureX, textureY); // Box 141
		attachmentModel[23] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 142
		attachmentModel[24] = new ModelRendererTurbo(this, 121, 57, textureX, textureY); // Box 143
		attachmentModel[25] = new ModelRendererTurbo(this, 249, 89, textureX, textureY); // Box 144

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 115
		attachmentModel[0].setRotationPoint(0F, -3F, -2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 116
		attachmentModel[1].setRotationPoint(0F, -2F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 117
		attachmentModel[2].setRotationPoint(0F, -2F, 2F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 118
		attachmentModel[3].setRotationPoint(0F, 2F, -2F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 119
		attachmentModel[4].setRotationPoint(6F, 2F, -2F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 120
		attachmentModel[5].setRotationPoint(6F, -2F, 2F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 121
		attachmentModel[6].setRotationPoint(6F, -3F, -2F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 122
		attachmentModel[7].setRotationPoint(6F, -2F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 127
		attachmentModel[8].setRotationPoint(6F, -4F, -2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 128
		attachmentModel[9].setRotationPoint(0F, -4F, -2F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 129
		attachmentModel[10].setRotationPoint(0F, -5F, -2F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 130
		attachmentModel[11].setRotationPoint(6F, -5F, -2F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 10, 1, 4, 0F, 2F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 2F, 0F, 1F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F); // Box 131
		attachmentModel[12].setRotationPoint(-2F, -6F, -2F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 14, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 132
		attachmentModel[13].setRotationPoint(-6F, -7F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 133
		attachmentModel[14].setRotationPoint(-6F, -8F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 134
		attachmentModel[15].setRotationPoint(-2F, -8F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 135
		attachmentModel[16].setRotationPoint(6F, -8F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 136
		attachmentModel[17].setRotationPoint(2F, -8F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F); // Box 137
		attachmentModel[18].setRotationPoint(0F, -1F, 3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F); // Box 138
		attachmentModel[19].setRotationPoint(0.5F, -1F, 3.25F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F); // Box 139
		attachmentModel[20].setRotationPoint(6F, -1F, 3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F); // Box 140
		attachmentModel[21].setRotationPoint(6.5F, -1F, 3.25F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 141
		attachmentModel[22].setRotationPoint(6F, -1F, -4F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F); // Box 142
		attachmentModel[23].setRotationPoint(6.5F, -1F, -4.25F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F); // Box 143
		attachmentModel[24].setRotationPoint(0.5F, -1F, -4.25F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 144
		attachmentModel[25].setRotationPoint(0F, -1F, -4F);

		flipAll();
	}
}