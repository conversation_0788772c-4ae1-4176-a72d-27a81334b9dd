//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: SilencerMod0
// Model Creator: 
// Created on: 03.07.2019 - 20:04:57
// Last changed on: 03.07.2019 - 20:04:57

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelSilencerMod0 extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelSilencerMod0() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[26];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 444
		attachmentModel[1] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 445
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 446
		attachmentModel[3] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 447
		attachmentModel[4] = new ModelRendererTurbo(this, 105, 73, textureX, textureY); // Box 448
		attachmentModel[5] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 449
		attachmentModel[6] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 450
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 129, textureX, textureY); // Box 451
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 137, textureX, textureY); // Box 452
		attachmentModel[9] = new ModelRendererTurbo(this, 1, 145, textureX, textureY); // Box 453
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 454
		attachmentModel[11] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 455
		attachmentModel[12] = new ModelRendererTurbo(this, 1, 177, textureX, textureY); // Box 456
		attachmentModel[13] = new ModelRendererTurbo(this, 113, 89, textureX, textureY); // Box 457
		attachmentModel[14] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 458
		attachmentModel[15] = new ModelRendererTurbo(this, 217, 1, textureX, textureY); // Box 459
		attachmentModel[16] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 460
		attachmentModel[17] = new ModelRendererTurbo(this, 233, 17, textureX, textureY); // Box 461
		attachmentModel[18] = new ModelRendererTurbo(this, 209, 129, textureX, textureY); // Box 18
		attachmentModel[19] = new ModelRendererTurbo(this, 209, 153, textureX, textureY); // Box 19
		attachmentModel[20] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 20
		attachmentModel[21] = new ModelRendererTurbo(this, 217, 33, textureX, textureY); // Box 21
		attachmentModel[22] = new ModelRendererTurbo(this, 225, 41, textureX, textureY); // Box 22
		attachmentModel[23] = new ModelRendererTurbo(this, 217, 49, textureX, textureY); // Box 23
		attachmentModel[24] = new ModelRendererTurbo(this, 217, 57, textureX, textureY); // Box 24
		attachmentModel[25] = new ModelRendererTurbo(this, 233, 65, textureX, textureY); // Box 25

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 97, 5, 16, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 444
		attachmentModel[0].setRotationPoint(5F, -8F, -8F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 97, 1, 16, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 445
		attachmentModel[1].setRotationPoint(5F, -3F, -8F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 97, 5, 16, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F); // Box 446
		attachmentModel[2].setRotationPoint(5F, 3F, -8F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 46, 7, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 447
		attachmentModel[3].setRotationPoint(5F, -3.5F, -9F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 46, 5, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 5.5F, 0F, 0.5F, 5.5F, 0F, 0.5F, 5.5F, 0F, 0.5F, 5.5F); // Box 448
		attachmentModel[4].setRotationPoint(5F, -9F, -3.5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 46, 5, 7, 0F, 0F, 0.5F, 5.5F, 0F, 0.5F, 5.5F, 0F, 0.5F, 5.5F, 0F, 0.5F, 5.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 449
		attachmentModel[5].setRotationPoint(5F, 4F, -3.5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 97, 1, 16, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 450
		attachmentModel[6].setRotationPoint(5F, 2F, -8F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 97, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 451
		attachmentModel[7].setRotationPoint(5F, 1F, -8F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 97, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 452
		attachmentModel[8].setRotationPoint(5F, 1F, 2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 97, 1, 6, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 453
		attachmentModel[9].setRotationPoint(5F, -2F, 2F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 97, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 454
		attachmentModel[10].setRotationPoint(5F, -2F, -8F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 97, 2, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 455
		attachmentModel[11].setRotationPoint(5F, -1F, 2F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 97, 2, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[12].setRotationPoint(5F, -1F, -8F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 46, 7, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 457
		attachmentModel[13].setRotationPoint(5F, -3.5F, 7F);

		attachmentModel[14].addShapeBox(0F, -2F, -7.5F, 2, 4, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 458
		attachmentModel[14].setRotationPoint(102F, 0F, 0F);
		attachmentModel[14].rotateAngleX = 0.78539816F;

		attachmentModel[15].addShapeBox(0F, -2F, -7.5F, 2, 4, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 459
		attachmentModel[15].setRotationPoint(102F, 0F, 0F);
		attachmentModel[15].rotateAngleX = 2.35619449F;

		attachmentModel[16].addShapeBox(0F, -2F, -7.5F, 2, 4, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 460
		attachmentModel[16].setRotationPoint(102F, 0F, 0F);
		attachmentModel[16].rotateAngleX = 3.92699082F;

		attachmentModel[17].addShapeBox(0F, -2F, -7.5F, 2, 4, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[17].setRotationPoint(102F, 0F, 0F);
		attachmentModel[17].rotateAngleX = 5.49778714F;

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 5, 5, 16, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 18
		attachmentModel[18].setRotationPoint(0F, -8F, -8F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 5, 5, 16, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, -5F); // Box 19
		attachmentModel[19].setRotationPoint(0F, 3F, -8F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 5, 2, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 20
		attachmentModel[20].setRotationPoint(0F, -1F, -8F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 5, 2, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 21
		attachmentModel[21].setRotationPoint(0F, -1F, 3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 5, 2, 5, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 22
		attachmentModel[22].setRotationPoint(0F, -3F, 3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 5, 2, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 23
		attachmentModel[23].setRotationPoint(0F, 1F, 3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 5, 2, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 24
		attachmentModel[24].setRotationPoint(0F, 1F, -8F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 5, 2, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 25
		attachmentModel[25].setRotationPoint(0F, -3F, -8F);



		flipAll();
	}
}