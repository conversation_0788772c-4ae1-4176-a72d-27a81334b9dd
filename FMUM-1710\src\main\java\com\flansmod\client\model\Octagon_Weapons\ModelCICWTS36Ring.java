//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC WTS-36 Ring
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36Ring extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelCICWTS36Ring() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[28];
		attachmentModel[0] = new ModelRendererTurbo(this, 481, 137, textureX, textureY); // Box 442
		attachmentModel[1] = new ModelRendererTurbo(this, 377, 145, textureX, textureY); // Box 446
		attachmentModel[2] = new ModelRendererTurbo(this, 409, 145, textureX, textureY); // Box 448
		attachmentModel[3] = new ModelRendererTurbo(this, 9, 25, textureX, textureY); // Box 451
		attachmentModel[4] = new ModelRendererTurbo(this, 385, 57, textureX, textureY); // Box 452
		attachmentModel[5] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 453
		attachmentModel[6] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 460
		attachmentModel[7] = new ModelRendererTurbo(this, 81, 97, textureX, textureY); // Box 461
		attachmentModel[8] = new ModelRendererTurbo(this, 401, 97, textureX, textureY); // Box 465
		attachmentModel[9] = new ModelRendererTurbo(this, 457, 129, textureX, textureY); // Box 467
		attachmentModel[10] = new ModelRendererTurbo(this, 473, 145, textureX, textureY); // Box 469
		attachmentModel[11] = new ModelRendererTurbo(this, 497, 145, textureX, textureY); // Box 471
		attachmentModel[12] = new ModelRendererTurbo(this, 377, 169, textureX, textureY); // Box 376
		attachmentModel[13] = new ModelRendererTurbo(this, 497, 153, textureX, textureY); // Box 377
		attachmentModel[14] = new ModelRendererTurbo(this, 25, 121, textureX, textureY); // Box 378
		attachmentModel[15] = new ModelRendererTurbo(this, 57, 121, textureX, textureY); // Box 379
		attachmentModel[16] = new ModelRendererTurbo(this, 129, 121, textureX, textureY); // Box 384
		attachmentModel[17] = new ModelRendererTurbo(this, 137, 161, textureX, textureY); // Box 385
		attachmentModel[18] = new ModelRendererTurbo(this, 153, 121, textureX, textureY); // Box 386
		attachmentModel[19] = new ModelRendererTurbo(this, 401, 169, textureX, textureY); // Box 387
		attachmentModel[20] = new ModelRendererTurbo(this, 425, 169, textureX, textureY); // Box 389
		attachmentModel[21] = new ModelRendererTurbo(this, 313, 225, textureX, textureY); // Box 471
		attachmentModel[22] = new ModelRendererTurbo(this, 329, 225, textureX, textureY); // Box 472
		attachmentModel[23] = new ModelRendererTurbo(this, 345, 225, textureX, textureY); // Box 473
		attachmentModel[24] = new ModelRendererTurbo(this, 505, 217, textureX, textureY); // Box 474
		attachmentModel[25] = new ModelRendererTurbo(this, 369, 225, textureX, textureY); // Box 475
		attachmentModel[26] = new ModelRendererTurbo(this, 377, 225, textureX, textureY); // Box 476
		attachmentModel[27] = new ModelRendererTurbo(this, 385, 225, textureX, textureY); // Box 477

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 442
		attachmentModel[0].setRotationPoint(0F, -2F, -6F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 446
		attachmentModel[1].setRotationPoint(0F, 5F, -2F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		attachmentModel[2].setRotationPoint(0F, -2F, 5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 451
		attachmentModel[3].setRotationPoint(8F, 5F, -2F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 452
		attachmentModel[4].setRotationPoint(8F, -2F, -6F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 453
		attachmentModel[5].setRotationPoint(8F, -2F, 5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F); // Box 460
		attachmentModel[6].setRotationPoint(9F, 5F, -2F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 461
		attachmentModel[7].setRotationPoint(9F, -2F, -6F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 465
		attachmentModel[8].setRotationPoint(9F, -2F, 5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 467
		attachmentModel[9].setRotationPoint(10F, -2F, 5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[10].setRotationPoint(10F, 5F, -2F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[11].setRotationPoint(10F, -2F, -6F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 8, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 376
		attachmentModel[12].setRotationPoint(0F, -5F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 377
		attachmentModel[13].setRotationPoint(10F, -5F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -3F); // Box 378
		attachmentModel[14].setRotationPoint(8F, -5F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -2F); // Box 379
		attachmentModel[15].setRotationPoint(9F, -5F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 3F); // Box 384
		attachmentModel[16].setRotationPoint(8F, -5F, 2F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 385
		attachmentModel[17].setRotationPoint(10F, -5F, 2F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 2F); // Box 386
		attachmentModel[18].setRotationPoint(9F, -5F, 2F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 8, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 387
		attachmentModel[19].setRotationPoint(0F, -5F, 2F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 389
		attachmentModel[20].setRotationPoint(0F, 2F, 2F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 471
		attachmentModel[21].setRotationPoint(10F, 2F, 2F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 472
		attachmentModel[22].setRotationPoint(10F, 2F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 473
		attachmentModel[23].setRotationPoint(0F, 2F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, -3F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -1F, -1F, 0F, 0F, -1F); // Box 474
		attachmentModel[24].setRotationPoint(8F, 2F, 2F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, -2F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, -1F); // Box 475
		attachmentModel[25].setRotationPoint(9F, 2F, 2F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -2F, 0F, -1F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -2F, 0F); // Box 476
		attachmentModel[26].setRotationPoint(9F, 2F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, -1F, -1F, 0F, -2F, 0F, 0F, -1F, 0F); // Box 477
		attachmentModel[27].setRotationPoint(8F, 2F, -3F);

		flipAll();
	}
}