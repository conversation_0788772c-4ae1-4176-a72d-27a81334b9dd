//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: X Products X-15 5.56x45mm 50 Round Drum
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelGun;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.Vector3f;

public class ModelXProductsX15556x45mm50RoundDrum extends ModelGun //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelXProductsX15556x45mm50RoundDrum() //Same as Filename
	{
		gunModel = new ModelRendererTurbo[59];
		gunModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 106
		gunModel[1] = new ModelRendererTurbo(this, 17, 1, textureX, textureY); // Box 110
		gunModel[2] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 68
		gunModel[3] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 147
		gunModel[4] = new ModelRendererTurbo(this, 73, 1, textureX, textureY); // Box 148
		gunModel[5] = new ModelRendererTurbo(this, 97, 1, textureX, textureY); // Box 149
		gunModel[6] = new ModelRendererTurbo(this, 121, 1, textureX, textureY); // Box 157
		gunModel[7] = new ModelRendererTurbo(this, 129, 1, textureX, textureY); // Box 84
		gunModel[8] = new ModelRendererTurbo(this, 169, 1, textureX, textureY); // Box 85
		gunModel[9] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 86
		gunModel[10] = new ModelRendererTurbo(this, 193, 1, textureX, textureY); // Box 90
		gunModel[11] = new ModelRendererTurbo(this, 193, 1, textureX, textureY); // Box 82
		gunModel[12] = new ModelRendererTurbo(this, 217, 1, textureX, textureY); // Box 83
		gunModel[13] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 84
		gunModel[14] = new ModelRendererTurbo(this, 73, 9, textureX, textureY); // Box 95
		gunModel[15] = new ModelRendererTurbo(this, 161, 9, textureX, textureY); // Box 96
		gunModel[16] = new ModelRendererTurbo(this, 129, 17, textureX, textureY); // Box 97
		gunModel[17] = new ModelRendererTurbo(this, 177, 17, textureX, textureY); // Box 98
		gunModel[18] = new ModelRendererTurbo(this, 81, 41, textureX, textureY); // Box 99
		gunModel[19] = new ModelRendererTurbo(this, 209, 17, textureX, textureY); // Box 100
		gunModel[20] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 101
		gunModel[21] = new ModelRendererTurbo(this, 145, 49, textureX, textureY); // Box 102
		gunModel[22] = new ModelRendererTurbo(this, 1, 80, textureX, textureY); // Box 103
		gunModel[23] = new ModelRendererTurbo(this, 73, 9, textureX, textureY); // Box 104
		gunModel[24] = new ModelRendererTurbo(this, 105, 17, textureX, textureY); // Box 105
		gunModel[25] = new ModelRendererTurbo(this, 105, 9, textureX, textureY); // Box 106
		gunModel[26] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 107
		gunModel[27] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 108
		gunModel[28] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 109
		gunModel[29] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 110
		gunModel[30] = new ModelRendererTurbo(this, 217, 41, textureX, textureY); // Box 111
		gunModel[31] = new ModelRendererTurbo(this, 201, 65, textureX, textureY); // Box 112
		gunModel[32] = new ModelRendererTurbo(this, 41, 79, textureX, textureY); // Box 113
		gunModel[33] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 114
		gunModel[34] = new ModelRendererTurbo(this, 97, 81, textureX, textureY); // Box 115
		gunModel[35] = new ModelRendererTurbo(this, 65, 1, textureX, textureY); // Box 116
		gunModel[36] = new ModelRendererTurbo(this, 241, 9, textureX, textureY); // Box 117
		gunModel[37] = new ModelRendererTurbo(this, 129, 17, textureX, textureY); // Box 118
		gunModel[38] = new ModelRendererTurbo(this, 137, 41, textureX, textureY); // Box 119
		gunModel[39] = new ModelRendererTurbo(this, 161, 81, textureX, textureY); // Box 120
		gunModel[40] = new ModelRendererTurbo(this, 185, 94, textureX, textureY); // Box 121
		gunModel[41] = new ModelRendererTurbo(this, 1, 101, textureX, textureY); // Box 122
		gunModel[42] = new ModelRendererTurbo(this, 137, 81, textureX, textureY); // Box 123
		gunModel[43] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 124
		gunModel[44] = new ModelRendererTurbo(this, 169, 33, textureX, textureY); // Box 125
		gunModel[45] = new ModelRendererTurbo(this, 153, 17, textureX, textureY); // Box 126
		gunModel[46] = new ModelRendererTurbo(this, 185, 17, textureX, textureY); // Box 127
		gunModel[47] = new ModelRendererTurbo(this, 73, 9, textureX, textureY); // Box 128
		gunModel[48] = new ModelRendererTurbo(this, 89, 9, textureX, textureY); // Box 129
		gunModel[49] = new ModelRendererTurbo(this, 57, 49, textureX, textureY); // Box 130
		gunModel[50] = new ModelRendererTurbo(this, 48, 107, textureX, textureY); // Box 131
		gunModel[51] = new ModelRendererTurbo(this, 209, 17, textureX, textureY); // Box 132
		gunModel[52] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 133
		gunModel[53] = new ModelRendererTurbo(this, 217, 92, textureX, textureY); // Box 134
		gunModel[54] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 135
		gunModel[55] = new ModelRendererTurbo(this, 73, 25, textureX, textureY); // Box 136
		gunModel[56] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 137
		gunModel[57] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 139
		gunModel[58] = new ModelRendererTurbo(this, 153, 113, textureX, textureY); // Box 140

		gunModel[0].addShapeBox(0F, 0F, 0F, 6, 21, 1, 0F, -0.25F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.75F, -0.5F, 0F, -0.75F, -0.25F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.75F, -0.5F, 0F, -0.75F); // Box 106
		gunModel[0].setRotationPoint(11.5F, 0F, -2F);

		gunModel[1].addShapeBox(0F, 0F, 0F, 1, 17, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 110
		gunModel[1].setRotationPoint(0F, 1.5F, -1.5F);

		gunModel[2].addShapeBox(0F, 0F, 0F, 1, 21, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 68
		gunModel[2].setRotationPoint(17.5F, 0F, -2F);

		gunModel[3].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 147
		gunModel[3].setRotationPoint(2F, -0.5F, -3F);

		gunModel[4].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -1.25F, 0F, 0F, -1.25F, 0.125F, -0.25F, 0.5F, 0F, -0.25F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 148
		gunModel[4].setRotationPoint(2F, -1.5F, -3F);

		gunModel[5].addShapeBox(0F, 0F, 0F, 9, 1, 2, 0F, 0F, 0F, 0.5F, -1.5F, 0F, 0.5F, -1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 149
		gunModel[5].setRotationPoint(1F, -1F, 1F);

		gunModel[6].addShapeBox(0F, 0F, 0F, 1, 18, 2, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 157
		gunModel[6].setRotationPoint(1F, 2F, -2F);

		gunModel[7].addShapeBox(0F, 0F, 0F, 17, 6, 2, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 84
		gunModel[7].setRotationPoint(1.5F, 0F, 1F);

		gunModel[8].addShapeBox(0F, 0F, 0F, 10, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 85
		gunModel[8].setRotationPoint(8.5F, 6.5F, 2F);

		gunModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 86
		gunModel[9].setRotationPoint(1.5F, 6.5F, 2F);

		gunModel[10].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, -0.5F, 1F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F); // Box 90
		gunModel[10].setRotationPoint(4.75F, 8F, 3F);

		gunModel[11].addShapeBox(0F, 0F, 0F, 3, 4, 8, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 82
		gunModel[11].setRotationPoint(-2F, 31F, -4F);

		gunModel[12].addShapeBox(0F, 0F, 0F, 7, 2, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 83
		gunModel[12].setRotationPoint(18F, 32F, -3F);

		gunModel[13].addShapeBox(0F, 0F, 0F, 12, 10, 28, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 84
		gunModel[13].setRotationPoint(1F, 28F, -14F);

		gunModel[14].addShapeBox(0F, 0F, 0F, 4, 4, 22, 0F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, 1F, 0F, 0F, 1F); // Box 95
		gunModel[14].setRotationPoint(14F, 25F, -11F);

		gunModel[15].addShapeBox(0F, 0F, 0F, 4, 4, 14, 0F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0.5F, 0F, 3F, 0.5F, 0F, 3F, 0F, 0F, 3F); // Box 96
		gunModel[15].setRotationPoint(14F, 21F, -7F);

		gunModel[16].addShapeBox(0F, 0F, 0F, 4, 4, 14, 0F, 0F, 0F, 3F, 0.5F, 0F, 3F, 0.5F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, 0F, -3F, 0F, 0F, -3F); // Box 97
		gunModel[16].setRotationPoint(14F, 41F, -7F);

		gunModel[17].addShapeBox(0F, 0F, 0F, 4, 4, 22, 0F, 0F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0F, 0F, -1F); // Box 98
		gunModel[17].setRotationPoint(14F, 37F, -11F);

		gunModel[18].addShapeBox(0F, 0F, 0F, 4, 8, 24, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 99
		gunModel[18].setRotationPoint(14F, 29F, -12F);

		gunModel[19].addShapeBox(0F, 0F, 0F, 6, 4, 16, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 100
		gunModel[19].setRotationPoint(7F, 19F, -8F);

		gunModel[20].addShapeBox(0F, 0F, 0F, 12, 5, 24, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 101
		gunModel[20].setRotationPoint(1F, 23F, -12F);

		gunModel[21].addShapeBox(0F, 0F, 0F, 12, 5, 24, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 102
		gunModel[21].setRotationPoint(1F, 38F, -12F);

		gunModel[22].addShapeBox(0F, 0F, 0F, 12, 4, 16, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 103
		gunModel[22].setRotationPoint(1F, 43F, -8F);

		gunModel[23].addShapeBox(0F, 0F, 0F, 3, 2, 6, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 104
		gunModel[23].setRotationPoint(-2F, 29F, -3F);

		gunModel[24].addShapeBox(0F, 0F, 0F, 3, 2, 6, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 105
		gunModel[24].setRotationPoint(-2F, 35F, -3F);

		gunModel[25].addShapeBox(0F, 0F, 0F, 2, 2, 4, 0F, 0F, 0F, 0F, -1.5F, 1F, 2F, -1.5F, 1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 1F, 2F, -1.5F, 1F, 2F, 0F, 0F, 0F); // Box 106
		gunModel[25].setRotationPoint(-2.5F, 32F, -2F);

		gunModel[26].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, -1F, -1.5F, 2F, 0F, -1.5F, 2F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, -1.5F, -1F, 2F, -1.5F, -1F, 2F, 0F, 0F, 0F); // Box 107
		gunModel[26].setRotationPoint(-2.5F, 31F, -2F);

		gunModel[27].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, -1.5F, -1F, 2F, -1.5F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, -1F, -1.5F, 2F, 0F, -1.5F, 2F, 0F, 0F, 0F, -1F); // Box 108
		gunModel[27].setRotationPoint(-2.5F, 34F, -2F);

		gunModel[28].addShapeBox(0F, 0F, 0F, 7, 2, 4, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 109
		gunModel[28].setRotationPoint(18F, 30F, -2F);

		gunModel[29].addShapeBox(0F, 0F, 0F, 7, 2, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 110
		gunModel[29].setRotationPoint(18F, 34F, -2F);

		gunModel[30].addShapeBox(0F, 0F, 0F, 4, 4, 14, 0F, 0F, 0F, 3F, 0.5F, 0F, 3F, 0.5F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, 0F, -3F, 0F, 0F, -3F); // Box 111
		gunModel[30].setRotationPoint(19F, 41F, -7F);

		gunModel[31].addShapeBox(0F, 0F, 0F, 4, 4, 22, 0F, 0F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0F, 0F, -1F); // Box 112
		gunModel[31].setRotationPoint(19F, 37F, -11F);

		gunModel[32].addShapeBox(0F, 0F, 0F, 4, 4, 22, 0F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, 1F, 0F, 0F, 1F); // Box 113
		gunModel[32].setRotationPoint(19F, 25F, -11F);

		gunModel[33].addShapeBox(0F, 0F, 0F, 4, 4, 14, 0F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0.5F, 0F, 3F, 0.5F, 0F, 3F, 0F, 0F, 3F); // Box 114
		gunModel[33].setRotationPoint(19F, 21F, -7F);

		gunModel[34].addShapeBox(0F, 0F, 0F, 4, 8, 24, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 115
		gunModel[34].setRotationPoint(19F, 29F, -12F);

		gunModel[35].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 116
		gunModel[35].setRotationPoint(25F, 32F, -2F);

		gunModel[36].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 1F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 1F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 117
		gunModel[36].setRotationPoint(25F, 31F, -2F);

		gunModel[37].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 1F, -1F); // Box 118
		gunModel[37].setRotationPoint(25F, 34F, -2F);

		gunModel[38].addShapeBox(0F, 0F, 0F, 1, 4, 14, 0F, 0F, 1F, -2F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, -0.5F, 4F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -0.5F, 4F); // Box 119
		gunModel[38].setRotationPoint(13F, 21F, -7F);

		gunModel[39].addShapeBox(0F, 0F, 0F, 1, 4, 22, 0F, 0F, 0.5F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0.5F, 0F, 0F, -1F, 2F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, -1F, 2F); // Box 120
		gunModel[39].setRotationPoint(13F, 25F, -11F);

		gunModel[40].addShapeBox(0F, 0F, 0F, 1, 8, 24, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F); // Box 121
		gunModel[40].setRotationPoint(13F, 29F, -12F);

		gunModel[41].addShapeBox(0F, 0F, 0F, 1, 4, 22, 0F, 0F, -1F, 2F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, -1F, 2F, 0F, 1F, -0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 1F, -0.5F); // Box 122
		gunModel[41].setRotationPoint(13F, 37F, -11F);

		gunModel[42].addShapeBox(0F, 0F, 0F, 1, 4, 14, 0F, 0F, -1F, 3.5F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -1F, 3.5F, 0F, 1F, -2F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F); // Box 123
		gunModel[42].setRotationPoint(13F, 41F, -7F);

		gunModel[43].addShapeBox(0F, 0F, 0F, 10, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 124
		gunModel[43].setRotationPoint(8.5F, 6F, 2F);

		gunModel[44].addShapeBox(0F, 0F, 0F, 10, 1, 1, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 125
		gunModel[44].setRotationPoint(8.5F, 7F, 2F);

		gunModel[45].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F); // Box 126
		gunModel[45].setRotationPoint(0.5F, 7F, 2F);

		gunModel[46].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 127
		gunModel[46].setRotationPoint(0.5F, 6F, 2F);

		gunModel[47].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 128
		gunModel[47].setRotationPoint(5.25F, 8F, 3F);

		gunModel[48].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F); // Box 129
		gunModel[48].setRotationPoint(5.75F, 8F, 3F);

		gunModel[49].addShapeBox(0F, 0F, 0F, 17, 13, 2, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 130
		gunModel[49].setRotationPoint(1.5F, 8F, 1F);

		gunModel[50].addShapeBox(0F, 0F, 0F, 6, 4, 16, 0F, 0F, -1F, -1.5F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, -1.5F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 131
		gunModel[50].setRotationPoint(1F, 19F, -8F);

		gunModel[51].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -1F, -0.5F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 1F, -0.5F); // Box 132
		gunModel[51].setRotationPoint(7F, 18F, 3F);

		gunModel[52].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 133
		gunModel[52].setRotationPoint(13.5F, 18F, 3F);

		gunModel[53].addShapeBox(0F, 0F, 0F, 17, 21, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 134
		gunModel[53].setRotationPoint(1.5F, 0F, -3F);

		gunModel[54].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 135
		gunModel[54].setRotationPoint(13.5F, 18F, -4F);

		gunModel[55].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, -1F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 136
		gunModel[55].setRotationPoint(7F, 18F, -4F);

		gunModel[56].addShapeBox(0F, 0F, 0F, 1, 3, 6, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F); // Box 137
		gunModel[56].setRotationPoint(18.5F, 18F, -3F);

		gunModel[57].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F); // Box 139
		gunModel[57].setRotationPoint(1F, 0F, -2F);

		gunModel[58].addShapeBox(0F, 0F, 0F, 17, 2, 1, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 140
		gunModel[58].setRotationPoint(1.5F, 6F, 1F);

		takeOutTranslate = new Vector3f(-11F / 16F, -9F / 16F, 2F / 16F);
		takeOutRotate = new Vector3f(0F, 0F, -80F);
		holdingTranslate = new Vector3f(0F, 3F / 16F, 2.5F / 16F);
		holdingRotate = new Vector3f(-10F, 0F, 15F);
		thirdPersonOffset = new Vector3f(0F, 5F /16F, 0F);
		gunModifyTranslate = new Vector3f(0.5F, 10F /16F, 0F);

		bulletAttachPoint1 = new Vector3f[3];
		bulletAttachPoint1[0] = new Vector3f(1.5F / 160F, 0.25F / 160F, 0.75F / 160F);
		bulletAttachPoint1[1] = new Vector3f(1.5F / 160F, -1.75F / 160F, 0.75F / 160F);
		bulletAttachPoint1[2] = new Vector3f(1.5F / 160F, -3.75F / 160F, 0.75F / 160F);

		bulletAttachPoint2 = new Vector3f[] { bulletAttachPoint1[0], bulletAttachPoint1[1], bulletAttachPoint1[2] };

		magFollowerAttachPos = new Vector3f[4];
		magFollowerAttachPos[0] = new Vector3f(1.5F / 160F, -1.75F / 160F, 0F);
		magFollowerAttachPos[1] = new Vector3f(1.5F / 160F, -2.75F / 160F, 0F);
		magFollowerAttachPos[2] = new Vector3f(1.5F / 160F, -4.75F / 160F, 0F);
		magFollowerAttachPos[3] = new Vector3f(1.5F / 160F, -6.75F / 160F, 0F);

		flipAll();
	}
}