//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: Osprey Silencer@5.56mm Mod 0 - Short
// Model Creator: 
// Created on: 24.08.2019 - 09:46:57
// Last changed on: 24.08.2019 - 09:46:57

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelOspreySilencer556mmMod0Short extends ModelAttachment //Same as Filename
{
	int textureX = 128;
	int textureY = 64;

	public ModelOspreySilencer556mmMod0Short() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[27];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 211
		attachmentModel[1] = new ModelRendererTurbo(this, 9, 1, textureX, textureY); // Box 212
		attachmentModel[2] = new ModelRendererTurbo(this, 17, 1, textureX, textureY); // Box 213
		attachmentModel[3] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 214
		attachmentModel[4] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 218
		attachmentModel[5] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 219
		attachmentModel[6] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 220
		attachmentModel[7] = new ModelRendererTurbo(this, 57, 9, textureX, textureY); // Box 221
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 222
		attachmentModel[9] = new ModelRendererTurbo(this, 57, 25, textureX, textureY); // Box 223
		attachmentModel[10] = new ModelRendererTurbo(this, 97, 1, textureX, textureY); // Box 224
		attachmentModel[11] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 225
		attachmentModel[12] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 228
		attachmentModel[13] = new ModelRendererTurbo(this, 105, 17, textureX, textureY); // Box 229
		attachmentModel[14] = new ModelRendererTurbo(this, 121, 17, textureX, textureY); // Box 231
		attachmentModel[15] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 232
		attachmentModel[16] = new ModelRendererTurbo(this, 89, 1, textureX, textureY); // Box 234
		attachmentModel[17] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 235
		attachmentModel[18] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 28
		attachmentModel[19] = new ModelRendererTurbo(this, 57, 25, textureX, textureY); // Box 29
		attachmentModel[20] = new ModelRendererTurbo(this, 9, 33, textureX, textureY); // Box 30
		attachmentModel[21] = new ModelRendererTurbo(this, 17, 33, textureX, textureY); // Box 31
		attachmentModel[22] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 32
		attachmentModel[23] = new ModelRendererTurbo(this, 41, 33, textureX, textureY); // Box 33
		attachmentModel[24] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 34
		attachmentModel[25] = new ModelRendererTurbo(this, 65, 33, textureX, textureY); // Box 35
		attachmentModel[26] = new ModelRendererTurbo(this, 81, 33, textureX, textureY); // Box 36

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 211
		attachmentModel[0].setRotationPoint(0F, -1F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 212
		attachmentModel[1].setRotationPoint(0F, -1F, -2.5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 213
		attachmentModel[2].setRotationPoint(0F, -2.5F, -1F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 214
		attachmentModel[3].setRotationPoint(0F, 2F, -1F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -1F, 0.5F, 0F, -1F, 0.5F); // Box 218
		attachmentModel[4].setRotationPoint(0F, 1F, -2.5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 22, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 2.5F); // Box 219
		attachmentModel[5].setRotationPoint(3F, -4F, -1.5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 22, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 220
		attachmentModel[6].setRotationPoint(3F, -1F, 1F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 22, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 221
		attachmentModel[7].setRotationPoint(3F, -1F, -4F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 22, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 222
		attachmentModel[8].setRotationPoint(3F, 1F, -1F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 22, 2, 5, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 223
		attachmentModel[9].setRotationPoint(3F, 5F, -2.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 224
		attachmentModel[10].setRotationPoint(1F, -1F, 2F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 225
		attachmentModel[11].setRotationPoint(1F, -1F, -4F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 2, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 228
		attachmentModel[12].setRotationPoint(1F, -4F, -1.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 229
		attachmentModel[13].setRotationPoint(1F, 6F, -2F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 0F, 0F, -1.5F, 0F, 0F, 1.5F, 0F, 0F, 1.5F); // Box 231
		attachmentModel[14].setRotationPoint(1F, 5F, -4F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1.5F, -0.5F, 0F, -1.5F, -0.5F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 232
		attachmentModel[15].setRotationPoint(1F, 3F, -4F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 234
		attachmentModel[16].setRotationPoint(1F, 4F, -4F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 4, 3, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.75F, 0F, 0F, 0.75F, 0F, 0F, 0.75F, 0F, 0F, 0.75F); // Box 235
		attachmentModel[17].setRotationPoint(1F, 2F, -1.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, -1.5F, 0F, 0F, -1.5F); // Box 28
		attachmentModel[18].setRotationPoint(1F, 5F, 3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F); // Box 29
		attachmentModel[19].setRotationPoint(0F, 1F, 1.5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 30
		attachmentModel[20].setRotationPoint(0F, -3F, 1.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 31
		attachmentModel[21].setRotationPoint(0F, -3F, -2.5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, -1.5F, -1F, 0F, -1.5F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -2.5F, 0F, 1F, -2.5F, 0F, -1F, 1F, 0F, -1F, 1F); // Box 32
		attachmentModel[22].setRotationPoint(1F, 1F, -4F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -0.5F, 0F, -2F, -0.5F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0.5F, -1F, 0F, 0.5F, -1F); // Box 33
		attachmentModel[23].setRotationPoint(1F, 1F, 2F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 1F, 0F, -1.5F, 1F, 0F, 0F, -1.25F, 0F, 0F, -1.25F, 0F, -1F, 1F, 0F, -1F, 1F); // Box 34
		attachmentModel[24].setRotationPoint(1F, 2F, 1F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, 0F, -2F, 1F, 0F, -2F, 1F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[25].setRotationPoint(1F, -4F, 2F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, -2F, 1F, 0F, -2F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 36
		attachmentModel[26].setRotationPoint(1F, -4F, -4F);

		flipAll();
	}
}