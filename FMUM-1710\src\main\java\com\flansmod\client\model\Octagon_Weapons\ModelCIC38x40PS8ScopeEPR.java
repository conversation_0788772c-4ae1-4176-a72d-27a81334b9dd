//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC 3-8x40 PS-8 Scope
// Model Creator: 
// Created on: 27.10.2019 - 16:45:48
// Last changed on: 27.10.2019 - 16:45:48

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCIC38x40PS8ScopeEPR extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelCIC38x40PS8ScopeEPR() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[20];
		attachmentModel[0] = new ModelRendererTurbo(this, 57, 145, textureX, textureY); // Box 131
		attachmentModel[1] = new ModelRendererTurbo(this, 161, 145, textureX, textureY); // Box 132
		attachmentModel[2] = new ModelRendererTurbo(this, 177, 145, textureX, textureY); // Box 133
		attachmentModel[3] = new ModelRendererTurbo(this, 193, 145, textureX, textureY); // Box 134
		attachmentModel[4] = new ModelRendererTurbo(this, 65, 153, textureX, textureY); // Box 135
		attachmentModel[5] = new ModelRendererTurbo(this, 81, 153, textureX, textureY); // Box 136
		attachmentModel[6] = new ModelRendererTurbo(this, 97, 153, textureX, textureY); // Box 137
		attachmentModel[7] = new ModelRendererTurbo(this, 113, 153, textureX, textureY); // Box 138
		attachmentModel[8] = new ModelRendererTurbo(this, 249, 33, textureX, textureY); // Box 139
		attachmentModel[9] = new ModelRendererTurbo(this, 249, 49, textureX, textureY); // Box 140
		attachmentModel[10] = new ModelRendererTurbo(this, 161, 153, textureX, textureY); // Box 141
		attachmentModel[11] = new ModelRendererTurbo(this, 145, 65, textureX, textureY); // Box 142
		attachmentModel[12] = new ModelRendererTurbo(this, 201, 65, textureX, textureY); // Box 143
		attachmentModel[13] = new ModelRendererTurbo(this, 177, 153, textureX, textureY); // Box 144
		attachmentModel[14] = new ModelRendererTurbo(this, 209, 153, textureX, textureY); // Box 145
		attachmentModel[15] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 146
		attachmentModel[16] = new ModelRendererTurbo(this, 209, 145, textureX, textureY); // Box 147
		attachmentModel[17] = new ModelRendererTurbo(this, 49, 161, textureX, textureY); // Box 148
		attachmentModel[18] = new ModelRendererTurbo(this, 73, 161, textureX, textureY); // Box 149
		attachmentModel[19] = new ModelRendererTurbo(this, 97, 161, textureX, textureY); // Box 150

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 131
		attachmentModel[0].setRotationPoint(0F, -4F, 4F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 6, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 132
		attachmentModel[1].setRotationPoint(0F, 2F, 4F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 6, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F); // Box 133
		attachmentModel[2].setRotationPoint(0F, 2F, -5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 6, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 134
		attachmentModel[3].setRotationPoint(0F, 4F, -2F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 6, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 135
		attachmentModel[4].setRotationPoint(0F, -2F, -5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 6, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 136
		attachmentModel[5].setRotationPoint(0F, -2F, 4F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 137
		attachmentModel[6].setRotationPoint(0F, -4F, -5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 6, 2, 2, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 138
		attachmentModel[7].setRotationPoint(0F, -1F, 5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 139
		attachmentModel[8].setRotationPoint(4F, -2F, 5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 140
		attachmentModel[9].setRotationPoint(1F, -2F, 5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 6, 2, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 141
		attachmentModel[10].setRotationPoint(0F, -1F, -7F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 142
		attachmentModel[11].setRotationPoint(4F, -2F, -6F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 143
		attachmentModel[12].setRotationPoint(1F, -2F, -6F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 6, 3, 6, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 144
		attachmentModel[13].setRotationPoint(0F, -7F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 8, 2, 6, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, -1F, 0F, 0F); // Box 145
		attachmentModel[14].setRotationPoint(-1F, -9F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 14, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 146
		attachmentModel[15].setRotationPoint(-7F, -10F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 147
		attachmentModel[16].setRotationPoint(-7F, -11F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 148
		attachmentModel[17].setRotationPoint(-3F, -11F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 149
		attachmentModel[18].setRotationPoint(5F, -11F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 150
		attachmentModel[19].setRotationPoint(1F, -11F, -3F);

		flipAll();
	}
}