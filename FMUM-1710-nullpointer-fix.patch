Flans Mod FMUM-1710 NullPointerException Fix
==========================================

This patch fixes a NullPointerException that occurs when other mods (like SpiceOfLife) 
try to check if gun items are edible. The crash happens because the getStates() method 
in GunType.java doesn't check if the ItemStack has a valid NBT compound before accessing it.

Error Details:
- Exception: java.lang.NullPointerException: Cannot invoke "net.minecraft.nbt.NBTTagCompound.func_74781_a(String)" because "gunStack.field_77990_d" is null
- Location: GunType.getStates(GunType.java:945)
- Triggered by: ItemGun.getItemUseAction() called by SpiceOfLife mod

Changes Made:
=============

1. Modified GunType.getStates() method to include null checks:
   - Check if stackTagCompound is null
   - Check if the STATES tag exists and is the correct type
   - Return null if either check fails

2. Updated all callers of getStates() to handle null returns:
   - ItemGun.getItemUseAction()
   - ItemGun.addInformation() (tooltip display)
   - ItemGun.onUpdate() (client-side update)
   - GunType.setGunStates() - added checkForTags() call
   - GunType.setGunOperatable() - added checkForTags() call
   - GunType.getMoveSpeed()
   - GunType.setSightsAtForGun()
   - GunType.loadNewMag()
   - GunType.doCharge()
   - GunType.releaseSlide()
   - GunType.tryShoot()
   - GunType.getMagState()

3. Added import for NBTBase class

Key Safety Measures:
===================
- Methods that initialize gun states (setGunStates, setGunOperatable) now call checkForTags() first
- Methods that read gun states return sensible defaults when states are null
- All null checks are defensive and won't break existing functionality

This fix ensures compatibility with other mods that inspect item properties without 
breaking the core functionality of Flans Mod.
