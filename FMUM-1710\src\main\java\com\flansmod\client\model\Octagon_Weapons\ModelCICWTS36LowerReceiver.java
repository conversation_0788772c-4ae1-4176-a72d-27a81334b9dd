//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICWTS36LowerReceiver
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelGun;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.TwoVector3f;

public class ModelCICWTS36LowerReceiver extends ModelGun //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelCICWTS36LowerReceiver() //Same as Filename
	{
		gunModel = new ModelRendererTurbo[92];
		gunModel[0] = new ModelRendererTurbo(this, 265, 49, textureX, textureY); // Box 143
		gunModel[1] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 157
		gunModel[2] = new ModelRendererTurbo(this, 145, 65, textureX, textureY); // Box 159
		gunModel[3] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 160
		gunModel[4] = new ModelRendererTurbo(this, 489, 41, textureX, textureY); // Box 161
		gunModel[5] = new ModelRendererTurbo(this, 409, 49, textureX, textureY); // Box 162
		gunModel[6] = new ModelRendererTurbo(this, 385, 57, textureX, textureY); // Box 163
		gunModel[7] = new ModelRendererTurbo(this, 137, 57, textureX, textureY); // Box 166
		gunModel[8] = new ModelRendererTurbo(this, 89, 25, textureX, textureY); // Box 167
		gunModel[9] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 189
		gunModel[10] = new ModelRendererTurbo(this, 257, 57, textureX, textureY); // Box 190
		gunModel[11] = new ModelRendererTurbo(this, 465, 73, textureX, textureY); // Box 191
		gunModel[12] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 196
		gunModel[13] = new ModelRendererTurbo(this, 481, 73, textureX, textureY); // Box 197
		gunModel[14] = new ModelRendererTurbo(this, 41, 81, textureX, textureY); // Box 199
		gunModel[15] = new ModelRendererTurbo(this, 321, 25, textureX, textureY); // Box 200
		gunModel[16] = new ModelRendererTurbo(this, 345, 25, textureX, textureY); // Box 201
		gunModel[17] = new ModelRendererTurbo(this, 465, 25, textureX, textureY); // Box 202
		gunModel[18] = new ModelRendererTurbo(this, 481, 25, textureX, textureY); // Box 203
		gunModel[19] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 237
		gunModel[20] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 238
		gunModel[21] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 239
		gunModel[22] = new ModelRendererTurbo(this, 113, 33, textureX, textureY); // Box 240
		gunModel[23] = new ModelRendererTurbo(this, 441, 33, textureX, textureY); // Box 241
		gunModel[24] = new ModelRendererTurbo(this, 449, 33, textureX, textureY); // Box 242
		gunModel[25] = new ModelRendererTurbo(this, 465, 33, textureX, textureY); // Box 243
		gunModel[26] = new ModelRendererTurbo(this, 177, 89, textureX, textureY); // Box 244
		gunModel[27] = new ModelRendererTurbo(this, 377, 81, textureX, textureY); // Box 247
		gunModel[28] = new ModelRendererTurbo(this, 449, 81, textureX, textureY); // Box 255
		gunModel[29] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 257
		gunModel[30] = new ModelRendererTurbo(this, 305, 89, textureX, textureY); // Box 258
		gunModel[31] = new ModelRendererTurbo(this, 361, 89, textureX, textureY); // Box 259
		gunModel[32] = new ModelRendererTurbo(this, 81, 97, textureX, textureY); // Box 261
		gunModel[33] = new ModelRendererTurbo(this, 417, 41, textureX, textureY); // Box 285
		gunModel[34] = new ModelRendererTurbo(this, 481, 89, textureX, textureY); // Box 286
		gunModel[35] = new ModelRendererTurbo(this, 361, 105, textureX, textureY); // Box 289
		gunModel[36] = new ModelRendererTurbo(this, 441, 41, textureX, textureY); // Box 290
		gunModel[37] = new ModelRendererTurbo(this, 465, 41, textureX, textureY); // Box 291
		gunModel[38] = new ModelRendererTurbo(this, 497, 89, textureX, textureY); // Box 292
		gunModel[39] = new ModelRendererTurbo(this, 17, 49, textureX, textureY); // Box 293
		gunModel[40] = new ModelRendererTurbo(this, 161, 97, textureX, textureY); // Box 294
		gunModel[41] = new ModelRendererTurbo(this, 41, 49, textureX, textureY); // Box 295
		gunModel[42] = new ModelRendererTurbo(this, 489, 41, textureX, textureY); // Box 296
		gunModel[43] = new ModelRendererTurbo(this, 65, 49, textureX, textureY); // Box 297
		gunModel[44] = new ModelRendererTurbo(this, 89, 49, textureX, textureY); // Box 298
		gunModel[45] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 299
		gunModel[46] = new ModelRendererTurbo(this, 233, 49, textureX, textureY); // Box 300
		gunModel[47] = new ModelRendererTurbo(this, 281, 57, textureX, textureY); // Box 301
		gunModel[48] = new ModelRendererTurbo(this, 433, 105, textureX, textureY); // Box 302
		gunModel[49] = new ModelRendererTurbo(this, 401, 97, textureX, textureY); // Box 303
		gunModel[50] = new ModelRendererTurbo(this, 185, 105, textureX, textureY); // Box 304
		gunModel[51] = new ModelRendererTurbo(this, 89, 113, textureX, textureY); // Box 306
		gunModel[52] = new ModelRendererTurbo(this, 297, 105, textureX, textureY); // Box 308
		gunModel[53] = new ModelRendererTurbo(this, 161, 57, textureX, textureY); // Box 363
		gunModel[54] = new ModelRendererTurbo(this, 1, 121, textureX, textureY); // Box 371
		gunModel[55] = new ModelRendererTurbo(this, 1, 129, textureX, textureY); // Box 393
		gunModel[56] = new ModelRendererTurbo(this, 145, 131, textureX, textureY); // Box 395
		gunModel[57] = new ModelRendererTurbo(this, 361, 129, textureX, textureY); // Box 396
		gunModel[58] = new ModelRendererTurbo(this, 1, 137, textureX, textureY); // Box 397
		gunModel[59] = new ModelRendererTurbo(this, 145, 137, textureX, textureY); // Box 399
		gunModel[60] = new ModelRendererTurbo(this, 145, 121, textureX, textureY); // Box 400
		gunModel[61] = new ModelRendererTurbo(this, 505, 121, textureX, textureY); // Box 407
		gunModel[62] = new ModelRendererTurbo(this, 417, 113, textureX, textureY); // Box 408
		gunModel[63] = new ModelRendererTurbo(this, 441, 113, textureX, textureY); // Box 409
		gunModel[64] = new ModelRendererTurbo(this, 249, 137, textureX, textureY); // Box 425
		gunModel[65] = new ModelRendererTurbo(this, 497, 137, textureX, textureY); // Box 479
		gunModel[66] = new ModelRendererTurbo(this, 435, 145, textureX, textureY); // Box 480
		gunModel[67] = new ModelRendererTurbo(this, 401, 177, textureX, textureY); // Box 406
		gunModel[68] = new ModelRendererTurbo(this, 465, 177, textureX, textureY); // Box 408
		gunModel[69] = new ModelRendererTurbo(this, 1, 185, textureX, textureY); // Box 409
		gunModel[70] = new ModelRendererTurbo(this, 33, 185, textureX, textureY); // Box 410
		gunModel[71] = new ModelRendererTurbo(this, 65, 185, textureX, textureY); // Box 411
		gunModel[72] = new ModelRendererTurbo(this, 193, 185, textureX, textureY); // Box 396
		gunModel[73] = new ModelRendererTurbo(this, 209, 185, textureX, textureY); // Box 397
		gunModel[74] = new ModelRendererTurbo(this, 225, 185, textureX, textureY); // Box 398
		gunModel[75] = new ModelRendererTurbo(this, 249, 185, textureX, textureY); // Box 399
		gunModel[76] = new ModelRendererTurbo(this, 265, 185, textureX, textureY); // Box 400
		gunModel[77] = new ModelRendererTurbo(this, 281, 185, textureX, textureY); // Box 401
		gunModel[78] = new ModelRendererTurbo(this, 297, 185, textureX, textureY); // Box 402
		gunModel[79] = new ModelRendererTurbo(this, 393, 185, textureX, textureY); // Box 408
		gunModel[80] = new ModelRendererTurbo(this, 409, 185, textureX, textureY); // Box 409
		gunModel[81] = new ModelRendererTurbo(this, 1, 193, textureX, textureY); // Box 453
		gunModel[82] = new ModelRendererTurbo(this, 153, 217, textureX, textureY); // Box 496
		gunModel[83] = new ModelRendererTurbo(this, 241, 217, textureX, textureY); // Box 497
		gunModel[84] = new ModelRendererTurbo(this, 265, 217, textureX, textureY); // Box 498
		gunModel[85] = new ModelRendererTurbo(this, 305, 217, textureX, textureY); // Box 501
		gunModel[86] = new ModelRendererTurbo(this, 73, 225, textureX, textureY); // Box 431
		gunModel[87] = new ModelRendererTurbo(this, 89, 225, textureX, textureY); // Box 432
		gunModel[88] = new ModelRendererTurbo(this, 433, 176, textureX, textureY); // Box 449
		gunModel[89] = new ModelRendererTurbo(this, 433, 180, textureX, textureY); // Box 450
		gunModel[90] = new ModelRendererTurbo(this, 55, 236, textureX, textureY); // Box 126
		gunModel[91] = new ModelRendererTurbo(this, 465, 50, textureX, textureY); // Box 127

		gunModel[0].addShapeBox(0F, 0F, 0F, 1, 2, 7, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 143
		gunModel[0].setRotationPoint(8F, -39F, -2F);

		gunModel[1].addShapeBox(0F, 0F, 0F, 61, 1, 10, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 157
		gunModel[1].setRotationPoint(-52F, -37F, -5F);

		gunModel[2].addShapeBox(0F, 0F, 0F, 116, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 159
		gunModel[2].setRotationPoint(-107F, -41F, 6F);

		gunModel[3].addShapeBox(0F, 0F, 0F, 116, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 160
		gunModel[3].setRotationPoint(-107F, -38F, 6F);

		gunModel[4].addShapeBox(0F, 0F, 0F, 1, 4, 8, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 161
		gunModel[4].setRotationPoint(-18F, -36F, -4F);

		gunModel[5].addShapeBox(0F, 0F, 0F, 20, 6, 6, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 162
		gunModel[5].setRotationPoint(-12F, -36F, -3F);

		gunModel[6].addShapeBox(0F, 0F, 0F, 1, 5, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 163
		gunModel[6].setRotationPoint(8F, -36F, -3F);

		gunModel[7].addShapeBox(0F, 0F, 0F, 3, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F); // Box 166
		gunModel[7].setRotationPoint(-25.5F, -36F, -1.5F);

		gunModel[8].addShapeBox(0F, 0F, 0F, 2, 6, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F); // Box 167
		gunModel[8].setRotationPoint(-22.5F, -36F, -1.5F);

		gunModel[9].addShapeBox(0F, 0F, 0F, 2, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 189
		gunModel[9].setRotationPoint(-6.5F, -30F, -1F);

		gunModel[10].addShapeBox(0F, 0F, 0F, 2, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -2F, 0F, 0F); // Box 190
		gunModel[10].setRotationPoint(-6.5F, -26F, -1F);

		gunModel[11].addShapeBox(0F, 0F, 0F, 1, 8, 4, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 191
		gunModel[11].setRotationPoint(-11.5F, -28F, -2F);

		gunModel[12].addShapeBox(0F, 0F, 0F, 7, 4, 10, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 196
		gunModel[12].setRotationPoint(-17F, -36F, -5F);

		gunModel[13].addShapeBox(0F, 0F, 0F, 7, 1, 8, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 197
		gunModel[13].setRotationPoint(-17F, -32F, -4F);

		gunModel[14].addShapeBox(0F, 0F, 0F, 1, 4, 8, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 199
		gunModel[14].setRotationPoint(-10F, -36F, -4F);

		gunModel[15].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F); // Box 200
		gunModel[15].setRotationPoint(-10F, -32F, 3F);

		gunModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F); // Box 201
		gunModel[16].setRotationPoint(-18F, -32F, -4F);

		gunModel[17].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -1F, 0F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, -1F, 0F, -0.5F, -0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, -0.5F, 0F); // Box 202
		gunModel[17].setRotationPoint(-10F, -32F, -4F);

		gunModel[18].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 1F, 0F, -1F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -1F, 0F); // Box 203
		gunModel[18].setRotationPoint(-18F, -32F, 3F);

		gunModel[19].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 237
		gunModel[19].setRotationPoint(4F, -34.5F, -4F);

		gunModel[20].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 238
		gunModel[20].setRotationPoint(5F, -33.5F, -4F);

		gunModel[21].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F); // Box 239
		gunModel[21].setRotationPoint(3F, -33.5F, -4F);

		gunModel[22].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 240
		gunModel[22].setRotationPoint(7F, -34F, -4F);

		gunModel[23].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 241
		gunModel[23].setRotationPoint(8F, -35F, -4F);

		gunModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 242
		gunModel[24].setRotationPoint(7F, -35F, -4F);

		gunModel[25].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 243
		gunModel[25].setRotationPoint(8F, -39F, 5F);

		gunModel[26].addShapeBox(0F, 0F, 0F, 22, 11, 2, 0F, 0.5F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, 0.5F, 0F, 0F); // Box 244
		gunModel[26].setRotationPoint(-71F, -36F, 3F);

		gunModel[27].addShapeBox(0F, 0F, 0F, 3, 11, 6, 0F, -1F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, -1F, -1F, 0F, 2F, 5F, 0F, 2F, -6F, 0F, -1F, -6F, 0F, -1F, 5F, 0F, 2F); // Box 247
		gunModel[27].setRotationPoint(-48F, -36F, -3F);

		gunModel[28].addShapeBox(0F, 0F, 0F, 2, 7, 10, 0F, 1F, 1F, 0F, 0.5F, -2F, 0F, 0.5F, -2F, 0F, 1F, 1F, 0F, -1F, 0F, -1F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, -1F, 0F, -1F); // Box 255
		gunModel[28].setRotationPoint(-74F, -27F, -5F);

		gunModel[29].addShapeBox(0F, 0F, 0F, 32, 9, 10, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 257
		gunModel[29].setRotationPoint(-107F, -37F, -5F);

		gunModel[30].addShapeBox(0F, 0F, 0F, 22, 11, 2, 0F, 0.5F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, 0.5F, 0F, 0F); // Box 258
		gunModel[30].setRotationPoint(-71F, -36F, -5F);

		gunModel[31].addShapeBox(0F, 0F, 0F, 1, 15, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0F, 0F, 0.1653F, 0F, 0F, 0.1653F, 0.5F, 0F, 1F, 0.5F); // Box 259
		gunModel[31].setRotationPoint(-71.5F, -36F, -3F);

		gunModel[32].addShapeBox(0F, 0F, 0F, 32, 3, 4, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 261
		gunModel[32].setRotationPoint(-107F, -28F, -2F);

		gunModel[33].addShapeBox(-2F, 8F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 285
		gunModel[33].setRotationPoint(-43F, -36F, -1.5F);
		gunModel[33].rotateAngleZ = -0.50614548F;

		gunModel[34].addShapeBox(-2F, 0F, 0F, 2, 8, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 286
		gunModel[34].setRotationPoint(-43F, -36F, -1.5F);
		gunModel[34].rotateAngleZ = -0.50614548F;

		gunModel[35].addShapeBox(0F, 0F, 0F, 31, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 289
		gunModel[35].setRotationPoint(-104F, -21F, -3F);

		gunModel[36].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 290
		gunModel[36].setRotationPoint(-85F, -22.5F, -1F);

		gunModel[37].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 291
		gunModel[37].setRotationPoint(-83F, -23F, -1F);

		gunModel[38].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 292
		gunModel[38].setRotationPoint(-90F, -22.5F, -3F);

		gunModel[39].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 293
		gunModel[39].setRotationPoint(-89F, -22.5F, -4F);

		gunModel[40].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 294
		gunModel[40].setRotationPoint(-86F, -22.5F, -3F);

		gunModel[41].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 295
		gunModel[41].setRotationPoint(-89F, -22.5F, 3F);

		gunModel[42].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 296
		gunModel[42].setRotationPoint(-81F, -23F, -1F);

		gunModel[43].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 297
		gunModel[43].setRotationPoint(-83F, -23F, -2F);

		gunModel[44].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 298
		gunModel[44].setRotationPoint(-83F, -23F, 1F);

		gunModel[45].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 299
		gunModel[45].setRotationPoint(-82.5F, -20.5F, -1.5F);

		gunModel[46].addShapeBox(0F, 0F, 0F, 1, 3, 2, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 300
		gunModel[46].setRotationPoint(-83.5F, -20F, -0.5F);

		gunModel[47].addShapeBox(0F, 0F, 0F, 1, 3, 2, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 301
		gunModel[47].setRotationPoint(-81.5F, -20F, -0.5F);

		gunModel[48].addShapeBox(0F, 0F, 0F, 31, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 302
		gunModel[48].setRotationPoint(-104F, -25F, -0.5F);

		gunModel[49].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 303
		gunModel[49].setRotationPoint(-104F, -22F, -0.5F);

		gunModel[50].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 304
		gunModel[50].setRotationPoint(-104F, -24F, -0.5F);

		gunModel[51].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 306
		gunModel[51].setRotationPoint(-52F, -40F, -7F);

		gunModel[52].addShapeBox(0F, 0F, 0F, 8, 1, 3, 0F, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 308
		gunModel[52].setRotationPoint(1F, -39F, -5F);

		gunModel[53].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 363
		gunModel[53].setRotationPoint(6F, -36F, -4F);

		gunModel[54].addShapeBox(0F, 0F, 0F, 61, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 371
		gunModel[54].setRotationPoint(-52F, -41F, -7F);

		gunModel[55].addShapeBox(0F, 0F, 0F, 61, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 393
		gunModel[55].setRotationPoint(-52F, -40F, -4F);

		gunModel[56].addShapeBox(0F, 0F, 0F, 61, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 395
		gunModel[56].setRotationPoint(-52F, -39F, -3F);

		gunModel[57].addShapeBox(0F, 0F, 0F, 61, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 396
		gunModel[57].setRotationPoint(-52F, -38F, -3F);

		gunModel[58].addShapeBox(0F, 0F, 0F, 55, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 397
		gunModel[58].setRotationPoint(-107F, -38F, -7F);

		gunModel[59].addShapeBox(0F, 0F, 0F, 55, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 399
		gunModel[59].setRotationPoint(-107F, -41F, -7F);

		gunModel[60].addShapeBox(0F, 0F, 0F, 3, 3, 4, 0F, -2F, 0F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, -2F, 0F, 0F, 0F, -1F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -1F, 0F); // Box 400
		gunModel[60].setRotationPoint(-12.5F, -32F, -2F);

		gunModel[61].addShapeBox(0F, 0F, 0F, 1, 15, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0.5F, 0F, 0.1653F, 0.5F, 0F, 0.1653F, 0F, 0F, 1F, 0F); // Box 407
		gunModel[61].setRotationPoint(-71.5F, -36F, 2F);

		gunModel[62].addShapeBox(0F, 0F, 0F, 6, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, -2.5F, 0F, -3F, -2.5F, -0.25F, 0F, 0F, -0.5F); // Box 408
		gunModel[62].setRotationPoint(-71.5F, -25F, 4F);

		gunModel[63].addShapeBox(0F, 0F, 0F, 6, 5, 1, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F); // Box 409
		gunModel[63].setRotationPoint(-71.5F, -25F, -5F);

		gunModel[64].addShapeBox(0F, 0F, 0F, 3, 10, 10, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0.5F, 1F, 0F, 0.5F, 1F, 0F, 0F, -2F, 0F); // Box 425
		gunModel[64].setRotationPoint(-75F, -36F, -5F);

		gunModel[65].addShapeBox(0F, 0F, 0F, 2, 7, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 479
		gunModel[65].setRotationPoint(-13.5F, -30F, -1.5F);

		gunModel[66].addShapeBox(0F, 0F, 0F, 7, 7, 3, 0F, 0.5F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0F, 5F, 0F, 0F, 5F, 0F, 0.5F, 0.5F, 0F); // Box 480
		gunModel[66].setRotationPoint(-20.5F, -35F, -1.5F);

		gunModel[67].addShapeBox(0F, 0F, 0F, 9, 2, 5, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 406
		gunModel[67].setRotationPoint(-52F, -39F, -2F);

		gunModel[68].addShapeBox(0F, 0F, 0F, 9, 5, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 408
		gunModel[68].setRotationPoint(-52F, -45F, 4F);

		gunModel[69].addShapeBox(0F, 0F, 0F, 9, 2, 2, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 409
		gunModel[69].setRotationPoint(-52F, -47F, 4F);

		gunModel[70].addShapeBox(0F, 0F, 0F, 9, 2, 2, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 410
		gunModel[70].setRotationPoint(-52F, -47F, -6F);

		gunModel[71].addShapeBox(0F, 0F, 0F, 9, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 411
		gunModel[71].setRotationPoint(-52F, -45F, -6F);

		gunModel[72].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 396
		gunModel[72].setRotationPoint(-107F, -43F, 6F);

		gunModel[73].addShapeBox(0F, 0F, 0F, 3, 5, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 397
		gunModel[73].setRotationPoint(-107F, -50F, 6F);

		gunModel[74].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 398
		gunModel[74].setRotationPoint(-107F, -51F, -3F);

		gunModel[75].addShapeBox(0F, 0F, 0F, 3, 5, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 399
		gunModel[75].setRotationPoint(-107F, -50F, -7F);

		gunModel[76].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 400
		gunModel[76].setRotationPoint(-107F, -46F, -7F);

		gunModel[77].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 401
		gunModel[77].setRotationPoint(-107F, -46F, 6F);

		gunModel[78].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 402
		gunModel[78].setRotationPoint(-107F, -43F, -7F);

		gunModel[79].addShapeBox(0F, 0F, 0F, 3, 8, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 408
		gunModel[79].setRotationPoint(-107F, -45F, -6F);

		gunModel[80].addShapeBox(0F, 0F, 0F, 3, 8, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 409
		gunModel[80].setRotationPoint(-107F, -45F, 5F);

		gunModel[81].addShapeBox(0F, 0F, 0F, 1, 11, 6, 0F, 0F, 0F, 0F, 5F, 0F, 0F, 5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 453
		gunModel[81].setRotationPoint(-53F, -36F, -3F);

		gunModel[82].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 496
		gunModel[82].setRotationPoint(-52F, -39F, -7F);

		gunModel[83].addShapeBox(0F, 0F, 0F, 8, 1, 2, 0F, -2F, 0F, -0.25F, 0F, 0F, 2F, 0F, 0F, -2F, -2F, 0F, -1.5F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 497
		gunModel[83].setRotationPoint(1F, -40F, -4F);

		gunModel[84].addShapeBox(0F, 0F, 0F, 8, 1, 2, 0F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -1F); // Box 498
		gunModel[84].setRotationPoint(1F, -38F, -4F);

		gunModel[85].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F); // Box 501
		gunModel[85].setRotationPoint(-52F, -38F, -7F);

		gunModel[86].addShapeBox(0F, 0F, 0F, 6, 5, 1, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F); // Box 431
		gunModel[86].setRotationPoint(-71.5F, -25F, 3F);

		gunModel[87].addShapeBox(0F, 0F, 0F, 6, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, -2.5F, 0F, -3F, -2.5F, 0F, 0F, 0F, 0F); // Box 432
		gunModel[87].setRotationPoint(-71.5F, -25F, -4F);

		gunModel[88].addShapeBox(0F, 0F, 0F, 9, 2, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 449
		gunModel[88].setRotationPoint(-52F, -41F, -5F);

		gunModel[89].addShapeBox(0F, 0F, 0F, 9, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 450
		gunModel[89].setRotationPoint(-52F, -41F, 4F);

		gunModel[90].addShapeBox(0F, 0F, 0F, 7, 6, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 126
		gunModel[90].setRotationPoint(-20.5F, -36F, -3.5F);

		gunModel[91].addShapeBox(0F, 0F, 0F, 1, 6, 5, 0F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 1F); // Box 127
		gunModel[91].setRotationPoint(-13.5F, -36F, -2.5F);


		switchModel = new ModelRendererTurbo[36];
		switchModel[0] = new ModelRendererTurbo(this, 489, 89, textureX, textureY); // Box 362
		switchModel[1] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 363
		switchModel[2] = new ModelRendererTurbo(this, 353, 105, textureX, textureY); // Box 364
		switchModel[3] = new ModelRendererTurbo(this, 433, 185, textureX, textureY); // Box 419
		switchModel[4] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 420
		switchModel[5] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 421
		switchModel[6] = new ModelRendererTurbo(this, 433, 185, textureX, textureY); // Box 430
		switchModel[7] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 431
		switchModel[8] = new ModelRendererTurbo(this, 353, 105, textureX, textureY); // Box 432
		switchModel[9] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 433
		switchModel[10] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 434
		switchModel[11] = new ModelRendererTurbo(this, 489, 89, textureX, textureY); // Box 435
		switchModel[12] = new ModelRendererTurbo(this, 433, 185, textureX, textureY); // Box 436
		switchModel[13] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 437
		switchModel[14] = new ModelRendererTurbo(this, 353, 105, textureX, textureY); // Box 438
		switchModel[15] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 439
		switchModel[16] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 440
		switchModel[17] = new ModelRendererTurbo(this, 489, 89, textureX, textureY); // Box 441
		switchModel[18] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 442
		switchModel[19] = new ModelRendererTurbo(this, 433, 185, textureX, textureY); // Box 443
		switchModel[20] = new ModelRendererTurbo(this, 353, 105, textureX, textureY); // Box 444
		switchModel[21] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 445
		switchModel[22] = new ModelRendererTurbo(this, 489, 89, textureX, textureY); // Box 446
		switchModel[23] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 447
		switchModel[24] = new ModelRendererTurbo(this, 433, 185, textureX, textureY); // Box 448
		switchModel[25] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 449
		switchModel[26] = new ModelRendererTurbo(this, 353, 105, textureX, textureY); // Box 450
		switchModel[27] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 451
		switchModel[28] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 452
		switchModel[29] = new ModelRendererTurbo(this, 489, 89, textureX, textureY); // Box 453
		switchModel[30] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 454
		switchModel[31] = new ModelRendererTurbo(this, 433, 185, textureX, textureY); // Box 455
		switchModel[32] = new ModelRendererTurbo(this, 353, 105, textureX, textureY); // Box 456
		switchModel[33] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 457
		switchModel[34] = new ModelRendererTurbo(this, 489, 89, textureX, textureY); // Box 458
		switchModel[35] = new ModelRendererTurbo(this, 425, 41, textureX, textureY); // Box 459

		switchModel[0].addShapeBox(-5F, -0.75F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 362
		switchModel[0].setRotationPoint(-13.5F, -34F, 5F);

		switchModel[1].addShapeBox(-1F, -2F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 363
		switchModel[1].setRotationPoint(-13.5F, -34F, 5F);

		switchModel[2].addShapeBox(-1F, 1F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 364
		switchModel[2].setRotationPoint(-13.5F, -34F, 5F);

		switchModel[3].addShapeBox(-2F, -1F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 419
		switchModel[3].setRotationPoint(-13.5F, -34F, 5F);

		switchModel[4].addShapeBox(-6F, -0.75F, 0F, 1, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F); // Box 420
		switchModel[4].setRotationPoint(-13.5F, -34F, 5F);

		switchModel[5].addShapeBox(-1F, -0.75F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F); // Box 421
		switchModel[5].setRotationPoint(-13.5F, -34F, 5F);

		switchModel[6].addShapeBox(-2F, -1F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 430
		switchModel[6].setRotationPoint(-13.5F, -34F, -6F);

		switchModel[7].addShapeBox(-1F, -2F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 431
		switchModel[7].setRotationPoint(-13.5F, -34F, -6F);

		switchModel[8].addShapeBox(-1F, 1F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 432
		switchModel[8].setRotationPoint(-13.5F, -34F, -6F);

		switchModel[9].addShapeBox(-1F, -0.75F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F); // Box 433
		switchModel[9].setRotationPoint(-13.5F, -34F, -6F);

		switchModel[10].addShapeBox(-6F, -0.75F, 0F, 1, 1, 1, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F); // Box 434
		switchModel[10].setRotationPoint(-13.5F, -34F, -6F);

		switchModel[11].addShapeBox(-5F, -0.75F, 0F, 4, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 435
		switchModel[11].setRotationPoint(-13.5F, -34F, -6F);

		switchModel[12].addShapeBox(-2F, -1F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 436
		switchModel[12].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[12].rotateAngleZ = 1.57079633F;

		switchModel[13].addShapeBox(-1F, -2F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 437
		switchModel[13].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[13].rotateAngleZ = 1.57079633F;

		switchModel[14].addShapeBox(-1F, 1F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 438
		switchModel[14].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[14].rotateAngleZ = 1.57079633F;

		switchModel[15].addShapeBox(-1F, -0.75F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F); // Box 439
		switchModel[15].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[15].rotateAngleZ = 1.57079633F;

		switchModel[16].addShapeBox(-6F, -0.75F, 0F, 1, 1, 1, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F); // Box 440
		switchModel[16].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[16].rotateAngleZ = 1.57079633F;

		switchModel[17].addShapeBox(-5F, -0.75F, 0F, 4, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 441
		switchModel[17].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[17].rotateAngleZ = 1.57079633F;

		switchModel[18].addShapeBox(-1F, -2F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 442
		switchModel[18].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[18].rotateAngleZ = 1.57079633F;

		switchModel[19].addShapeBox(-2F, -1F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 443
		switchModel[19].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[19].rotateAngleZ = 1.57079633F;

		switchModel[20].addShapeBox(-1F, 1F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 444
		switchModel[20].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[20].rotateAngleZ = 1.57079633F;

		switchModel[21].addShapeBox(-1F, -0.75F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F); // Box 445
		switchModel[21].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[21].rotateAngleZ = 1.57079633F;

		switchModel[22].addShapeBox(-5F, -0.75F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 446
		switchModel[22].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[22].rotateAngleZ = 1.57079633F;

		switchModel[23].addShapeBox(-6F, -0.75F, 0F, 1, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F); // Box 447
		switchModel[23].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[23].rotateAngleZ = 1.57079633F;

		switchModel[24].addShapeBox(-2F, -1F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		switchModel[24].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[24].rotateAngleZ = 3.14159265F;

		switchModel[25].addShapeBox(-1F, -2F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 449
		switchModel[25].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[25].rotateAngleZ = 3.14159265F;

		switchModel[26].addShapeBox(-1F, 1F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 450
		switchModel[26].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[26].rotateAngleZ = 3.14159265F;

		switchModel[27].addShapeBox(-1F, -0.75F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F); // Box 451
		switchModel[27].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[27].rotateAngleZ = 3.14159265F;

		switchModel[28].addShapeBox(-6F, -0.75F, 0F, 1, 1, 1, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F); // Box 452
		switchModel[28].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[28].rotateAngleZ = 3.14159265F;

		switchModel[29].addShapeBox(-5F, -0.75F, 0F, 4, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 453
		switchModel[29].setRotationPoint(-13.5F, -34F, -6F);
		switchModel[29].rotateAngleZ = 3.14159265F;

		switchModel[30].addShapeBox(-1F, -2F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 454
		switchModel[30].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[30].rotateAngleZ = 3.14159265F;

		switchModel[31].addShapeBox(-2F, -1F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 455
		switchModel[31].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[31].rotateAngleZ = 3.14159265F;

		switchModel[32].addShapeBox(-1F, 1F, 0F, 3, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 456
		switchModel[32].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[32].rotateAngleZ = 3.14159265F;

		switchModel[33].addShapeBox(-1F, -0.75F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F); // Box 457
		switchModel[33].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[33].rotateAngleZ = 3.14159265F;

		switchModel[34].addShapeBox(-5F, -0.75F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 458
		switchModel[34].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[34].rotateAngleZ = 3.14159265F;

		switchModel[35].addShapeBox(-6F, -0.75F, 0F, 1, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F); // Box 459
		switchModel[35].setRotationPoint(-13.5F, -34F, 5F);
		switchModel[35].rotateAngleZ = 3.14159265F;

		numSwitchModel = switchModel.length / 3;

		barrelOrigin.set(-52F / 160F, 43F / 160F, 0F);
		chamberSmokeOri.set(-1F / 160F, 39F / 160F, 27F / 160F);
		caseOrigin.set(-12F / 160F, 38.5F / 160F, -4.5F / 160F);
		caseEjectVelocity.set(28F / 160F, -3F / 160F, 28F / 160F);
		caseEjectRandV.set(5F / 160F, 6F / 160F, 5F / 160F);
		caseEjectVelocityWC.set(10F / 160F, -1F / 160F, 10F / 160F);
		caseEjectRandVWC.set(3F / 160F, 2F / 160F, 3F / 160F);

		magAttachPoint.set(-71.5F / 160F, 40F / 160F, 0F);

		holdingTranslate.set(10F / 160F, -2F / 160F, 20F / 160F);
		holdingRotate.set(-5F, 0F, 0F);
		thirdPersonOffset.set(22F / 160F, -10F / 160F, 0F);

		takeOutTranslate.set(-220F / 160F, -180F / 160F, 40F / 160F);
		takeOutRotate.set(0F, 0F, -160F);
		sprintTranslate.set(-10F / 160F, -40F / 160F, -40F / 160F);
		sprintRotate.set(-8F, 40F, -15F);
		restTranslate = sprintTranslate;
		restRotate = sprintRotate;

		reloadTranslate.set(50F / 160F, 0F, 0F);
		reloadRotate.set(15F, 0, 0);
		magTranslate.set(0, -10F / 16F, 0F);

		checkAmmoTranslate = reloadTranslate;
		checkAmmoRotate.set(15F, 0F, 0F);
		checkAmmoMagTranslate.set(0F, -25F / 160F, 0F);
		checkAmmoMagTranslate2.set(40F / 160F, 10F / 160F, -60F / 160F);
		checkAmmoMagRotate2.set(-30F, 0F, 0F);

		gunSlideDistance = 20F / 16F;
		chargeHandleDistance =  28F / 16F;

		leftHandAmmo = true;
		leftHandCharge = true;
		rightHandRelease = true;
		leftHandReloadingRelease = true;
		leftArmPos.set(48F / 160F, 32F / 160F, -11F / 160F);
		leftArmRot.set(-18F, -30F, 14F);
		rightArmPos.set(-14F / 160F, 10F / 160F, 5F / 160F);
		rightArmRot.set(0F, 23F, 10F);
		leftArmAmmoPos.set(-52F / 160F, -3F / 160F, 1F / 160F);
		leftArmAmmoRot.set(-5F, -40F, 21F);
		rightArmReleasePos.set(-37F / 160F, 14F / 160F, 10F / 160F);
		rightArmReleaseRot.set(3F, 51F, 11F);
		leftArmReloadingReleasePos.set(-36F / 160F, 14F / 160F, -12F / 160F);
		leftArmReloadingReleaseRot.set(-5F, -39F, 21F);
		leftArmChargePos.set(19F / 160F, 34F / 160F, -21F / 160F);
		leftArmChargeRot.set(-8F, -35F, 27F);

		leftArmPose.put("vgrip".hashCode(), new TwoVector3f(0F, 0F, 0F, -5F, 30F, -9F));
		leftArmPose.put("hgrip".hashCode(), new TwoVector3f(0F, 0F, 0F, -9F, 24F, -11F));
		leftArmPose.put("handguard".hashCode(), new TwoVector3f(0F, 0F, 0F, -18F, -24F, 11F));

		flipAll();
	}
}