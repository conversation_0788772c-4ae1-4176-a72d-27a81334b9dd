//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: Trijicon ACOG 4x32 TA31F
// Model Creator: 
// Created on: 09.11.2019 - 16:36:12
// Last changed on: 09.11.2019 - 16:36:12

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAtSight;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.Vector3f;

public class ModelTrijiconACOG4x32TA31F extends ModelAtSight //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelTrijiconACOG4x32TA31F() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[119];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 0
		attachmentModel[1] = new ModelRendererTurbo(this, 65, 1, textureX, textureY); // Box 1
		attachmentModel[2] = new ModelRendererTurbo(this, 121, 1, textureX, textureY); // Box 2
		attachmentModel[3] = new ModelRendererTurbo(this, 177, 1, textureX, textureY); // Box 4
		attachmentModel[4] = new ModelRendererTurbo(this, 225, 1, textureX, textureY); // Box 5
		attachmentModel[5] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 6
		attachmentModel[6] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 7
		attachmentModel[7] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 8
		attachmentModel[8] = new ModelRendererTurbo(this, 33, 9, textureX, textureY); // Box 9
		attachmentModel[9] = new ModelRendererTurbo(this, 57, 9, textureX, textureY); // Box 12
		attachmentModel[10] = new ModelRendererTurbo(this, 81, 9, textureX, textureY); // Box 14
		attachmentModel[11] = new ModelRendererTurbo(this, 105, 9, textureX, textureY); // Box 16
		attachmentModel[12] = new ModelRendererTurbo(this, 137, 9, textureX, textureY); // Box 17
		attachmentModel[13] = new ModelRendererTurbo(this, 193, 9, textureX, textureY); // Box 18
		attachmentModel[14] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 19
		attachmentModel[15] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 20
		attachmentModel[16] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 22
		attachmentModel[17] = new ModelRendererTurbo(this, 9, 9, textureX, textureY); // Box 25
		attachmentModel[18] = new ModelRendererTurbo(this, 145, 17, textureX, textureY); // Box 26
		attachmentModel[19] = new ModelRendererTurbo(this, 161, 17, textureX, textureY); // Box 27
		attachmentModel[20] = new ModelRendererTurbo(this, 177, 17, textureX, textureY); // Box 28
		attachmentModel[21] = new ModelRendererTurbo(this, 193, 17, textureX, textureY); // Box 29
		attachmentModel[22] = new ModelRendererTurbo(this, 209, 17, textureX, textureY); // Box 30
		attachmentModel[23] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 31
		attachmentModel[24] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 32
		attachmentModel[25] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 33
		attachmentModel[26] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 34
		attachmentModel[27] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 35
		attachmentModel[28] = new ModelRendererTurbo(this, 33, 25, textureX, textureY); // Box 36
		attachmentModel[29] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 37
		attachmentModel[30] = new ModelRendererTurbo(this, 65, 25, textureX, textureY); // Box 38
		attachmentModel[31] = new ModelRendererTurbo(this, 81, 25, textureX, textureY); // Box 39
		attachmentModel[32] = new ModelRendererTurbo(this, 97, 25, textureX, textureY); // Box 40
		attachmentModel[33] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 41
		attachmentModel[34] = new ModelRendererTurbo(this, 105, 25, textureX, textureY); // Box 51
		attachmentModel[35] = new ModelRendererTurbo(this, 137, 25, textureX, textureY); // Box 56
		attachmentModel[36] = new ModelRendererTurbo(this, 153, 25, textureX, textureY); // Box 57
		attachmentModel[37] = new ModelRendererTurbo(this, 249, 9, textureX, textureY); // Box 58
		attachmentModel[38] = new ModelRendererTurbo(this, 169, 25, textureX, textureY); // Box 59
		attachmentModel[39] = new ModelRendererTurbo(this, 177, 25, textureX, textureY); // Box 61
		attachmentModel[40] = new ModelRendererTurbo(this, 185, 25, textureX, textureY); // Box 65
		attachmentModel[41] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 80
		attachmentModel[42] = new ModelRendererTurbo(this, 209, 25, textureX, textureY); // Box 81
		attachmentModel[43] = new ModelRendererTurbo(this, 225, 25, textureX, textureY); // Box 82
		attachmentModel[44] = new ModelRendererTurbo(this, 241, 25, textureX, textureY); // Box 84
		attachmentModel[45] = new ModelRendererTurbo(this, 241, 9, textureX, textureY); // Box 86
		attachmentModel[46] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 88
		attachmentModel[47] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 90
		attachmentModel[48] = new ModelRendererTurbo(this, 41, 33, textureX, textureY); // Box 93
		attachmentModel[49] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 94
		attachmentModel[50] = new ModelRendererTurbo(this, 65, 33, textureX, textureY); // Box 95
		attachmentModel[51] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 96
		attachmentModel[52] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 98
		attachmentModel[53] = new ModelRendererTurbo(this, 145, 33, textureX, textureY); // Box 99
		attachmentModel[54] = new ModelRendererTurbo(this, 161, 33, textureX, textureY); // Box 100
		attachmentModel[55] = new ModelRendererTurbo(this, 89, 33, textureX, textureY); // Box 101
		attachmentModel[56] = new ModelRendererTurbo(this, 177, 33, textureX, textureY); // Box 102
		attachmentModel[57] = new ModelRendererTurbo(this, 193, 33, textureX, textureY); // Box 103
		attachmentModel[58] = new ModelRendererTurbo(this, 217, 33, textureX, textureY); // Box 104
		attachmentModel[59] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 105
		attachmentModel[60] = new ModelRendererTurbo(this, 41, 41, textureX, textureY); // Box 106
		attachmentModel[61] = new ModelRendererTurbo(this, 233, 33, textureX, textureY); // Box 107
		attachmentModel[62] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 108
		attachmentModel[63] = new ModelRendererTurbo(this, 57, 41, textureX, textureY); // Box 109
		attachmentModel[64] = new ModelRendererTurbo(this, 217, 33, textureX, textureY); // Box 110
		attachmentModel[65] = new ModelRendererTurbo(this, 97, 41, textureX, textureY); // Box 111
		attachmentModel[66] = new ModelRendererTurbo(this, 129, 41, textureX, textureY); // Box 112
		attachmentModel[67] = new ModelRendererTurbo(this, 65, 49, textureX, textureY); // Box 113
		attachmentModel[68] = new ModelRendererTurbo(this, 97, 49, textureX, textureY); // Box 115
		attachmentModel[69] = new ModelRendererTurbo(this, 193, 33, textureX, textureY); // Box 117
		attachmentModel[70] = new ModelRendererTurbo(this, 177, 41, textureX, textureY); // Box 118
		attachmentModel[71] = new ModelRendererTurbo(this, 241, 41, textureX, textureY); // Box 119
		attachmentModel[72] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 122
		attachmentModel[73] = new ModelRendererTurbo(this, 249, 33, textureX, textureY); // Box 143
		attachmentModel[74] = new ModelRendererTurbo(this, 129, 49, textureX, textureY); // Box 144
		attachmentModel[75] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 163
		attachmentModel[76] = new ModelRendererTurbo(this, 145, 49, textureX, textureY); // Box 164
		attachmentModel[77] = new ModelRendererTurbo(this, 153, 49, textureX, textureY); // Box 169
		attachmentModel[78] = new ModelRendererTurbo(this, 161, 49, textureX, textureY); // Box 170
		attachmentModel[79] = new ModelRendererTurbo(this, 169, 49, textureX, textureY); // Box 155
		attachmentModel[80] = new ModelRendererTurbo(this, 185, 49, textureX, textureY); // Box 158
		attachmentModel[81] = new ModelRendererTurbo(this, 209, 49, textureX, textureY); // Box 160
		attachmentModel[82] = new ModelRendererTurbo(this, 225, 49, textureX, textureY); // Box 161
		attachmentModel[83] = new ModelRendererTurbo(this, 241, 49, textureX, textureY); // Box 162
		attachmentModel[84] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 163
		attachmentModel[85] = new ModelRendererTurbo(this, 17, 57, textureX, textureY); // Box 164
		attachmentModel[86] = new ModelRendererTurbo(this, 25, 57, textureX, textureY); // Box 165
		attachmentModel[87] = new ModelRendererTurbo(this, 33, 57, textureX, textureY); // Box 169
		attachmentModel[88] = new ModelRendererTurbo(this, 49, 57, textureX, textureY); // Box 170
		attachmentModel[89] = new ModelRendererTurbo(this, 57, 57, textureX, textureY); // Box 171
		attachmentModel[90] = new ModelRendererTurbo(this, 73, 57, textureX, textureY); // Box 172
		attachmentModel[91] = new ModelRendererTurbo(this, 65, 57, textureX, textureY); // Box 173
		attachmentModel[92] = new ModelRendererTurbo(this, 81, 57, textureX, textureY); // Box 174
		attachmentModel[93] = new ModelRendererTurbo(this, 97, 57, textureX, textureY); // Box 155
		attachmentModel[94] = new ModelRendererTurbo(this, 121, 57, textureX, textureY); // Box 156
		attachmentModel[95] = new ModelRendererTurbo(this, 145, 57, textureX, textureY); // Box 157
		attachmentModel[96] = new ModelRendererTurbo(this, 161, 57, textureX, textureY); // Box 158
		attachmentModel[97] = new ModelRendererTurbo(this, 177, 49, textureX, textureY); // Box 159
		attachmentModel[98] = new ModelRendererTurbo(this, 177, 57, textureX, textureY); // Box 165
		attachmentModel[99] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 166
		attachmentModel[100] = new ModelRendererTurbo(this, 49, 65, textureX, textureY); // Box 167
		attachmentModel[101] = new ModelRendererTurbo(this, 89, 57, textureX, textureY); // Box 168
		attachmentModel[102] = new ModelRendererTurbo(this, 113, 57, textureX, textureY); // Box 169
		attachmentModel[103] = new ModelRendererTurbo(this, 225, 57, textureX, textureY); // Box 170
		attachmentModel[104] = new ModelRendererTurbo(this, 233, 57, textureX, textureY); // Box 174
		attachmentModel[105] = new ModelRendererTurbo(this, 241, 57, textureX, textureY); // Box 175
		attachmentModel[106] = new ModelRendererTurbo(this, 249, 57, textureX, textureY); // Box 176
		attachmentModel[107] = new ModelRendererTurbo(this, 97, 65, textureX, textureY); // Box 154
		attachmentModel[108] = new ModelRendererTurbo(this, 113, 65, textureX, textureY); // Box 155
		attachmentModel[109] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 156
		attachmentModel[110] = new ModelRendererTurbo(this, 49, 81, textureX, textureY); // Box 172
		attachmentModel[111] = new ModelRendererTurbo(this, 81, 81, textureX, textureY); // Box 173
		attachmentModel[112] = new ModelRendererTurbo(this, 177, 73, textureX, textureY); // Box 174
		attachmentModel[113] = new ModelRendererTurbo(this, 113, 81, textureX, textureY); // Box 175
		attachmentModel[114] = new ModelRendererTurbo(this, 129, 81, textureX, textureY); // Box 176
		attachmentModel[115] = new ModelRendererTurbo(this, 137, 81, textureX, textureY); // Box 178
		attachmentModel[116] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 179
		attachmentModel[117] = new ModelRendererTurbo(this, 161, 81, textureX, textureY); // Box 181
		attachmentModel[118] = new ModelRendererTurbo(this, 33, 89, textureX, textureY); // Box 187

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 25, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 0
		attachmentModel[0].setRotationPoint(-3.5F, -1F, -3F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 25, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 1
		attachmentModel[1].setRotationPoint(-3.5F, 0F, -4F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 25, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 2
		attachmentModel[2].setRotationPoint(-3.5F, 0F, 3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 19, 3, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F); // Box 4
		attachmentModel[3].setRotationPoint(-4.5F, -4F, -2F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 9, 2, 2, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F); // Box 5
		attachmentModel[4].setRotationPoint(-2.5F, -6F, -1F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 1, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 6
		attachmentModel[5].setRotationPoint(-5.5F, -5F, -2F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 2, 3, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 7
		attachmentModel[6].setRotationPoint(-6.5F, -5F, -1F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 8
		attachmentModel[7].setRotationPoint(-3.5F, -5F, -1F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 6, 1, 4, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 9
		attachmentModel[8].setRotationPoint(8.5F, -5F, -2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 12
		attachmentModel[9].setRotationPoint(8.5F, -12F, 5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 10, 4, 1, 0F, 0F, -1F, 3F, 1.5F, -1F, 3F, 2F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 14
		attachmentModel[10].setRotationPoint(8.5F, -16F, 5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 11, 1, 4, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 16
		attachmentModel[11].setRotationPoint(8.5F, -16F, -2F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 20, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 17
		attachmentModel[12].setRotationPoint(-22.5F, -15F, -2F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 20, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 18
		attachmentModel[13].setRotationPoint(-22.5F, -6F, -2F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 20, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 19
		attachmentModel[14].setRotationPoint(-22.5F, -12F, -5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 20, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 20
		attachmentModel[15].setRotationPoint(-22.5F, -12F, 4F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 20, 3, 1, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 22
		attachmentModel[16].setRotationPoint(-22.5F, -15F, 4F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0.5F, 0F); // Box 25
		attachmentModel[17].setRotationPoint(-13F, -16.5F, -0.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 3, 2, 4, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 26
		attachmentModel[18].setRotationPoint(-13F, -16.5F, 1F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 3, 2, 4, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 27
		attachmentModel[19].setRotationPoint(-13F, -16.5F, -5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 28
		attachmentModel[20].setRotationPoint(-13F, -15F, 5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 29
		attachmentModel[21].setRotationPoint(-13F, -15F, -7F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 30
		attachmentModel[22].setRotationPoint(-13F, -7F, -7F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 3, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 1F, -2F, 0F, 1F, -2F); // Box 31
		attachmentModel[23].setRotationPoint(-13F, -8F, 5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 1F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 32
		attachmentModel[24].setRotationPoint(-13F, -4.5F, -0.5F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 3, 2, 4, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 33
		attachmentModel[25].setRotationPoint(-13F, -5.5F, 1F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 3, 2, 4, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 34
		attachmentModel[26].setRotationPoint(-13F, -5.5F, -5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 2, 4, 2, 0F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0.5F, 0F, 1F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[27].setRotationPoint(-13F, -12F, -7F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 2, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 36
		attachmentModel[28].setRotationPoint(-13F, -12F, 5F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 37
		attachmentModel[29].setRotationPoint(-14F, -16F, 4F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 38
		attachmentModel[30].setRotationPoint(-14F, -16F, -6F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 39
		attachmentModel[31].setRotationPoint(-14F, -6F, -6F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 40
		attachmentModel[32].setRotationPoint(-14F, -6F, 4F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F); // Box 41
		attachmentModel[33].setRotationPoint(-20F, -10.75F, 6F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 9, 4, 8, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 51
		attachmentModel[34].setRotationPoint(-2.5F, -12F, -4F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F); // Box 56
		attachmentModel[35].setRotationPoint(6.5F, -6F, -2F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 57
		attachmentModel[36].setRotationPoint(6.5F, -15F, -2F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 58
		attachmentModel[37].setRotationPoint(6.5F, -12F, -5F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 59
		attachmentModel[38].setRotationPoint(6.5F, -12F, 4F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, -2F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, -1F, -3F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 61
		attachmentModel[39].setRotationPoint(6.5F, -16F, 4F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 65
		attachmentModel[40].setRotationPoint(8.5F, -12F, -6F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 1F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0.25F, 0F, 0F, 0F, 0F, 2F, 1F, 0F, 3.5F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 80
		attachmentModel[41].setRotationPoint(-14F, -6F, 4F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 1F, 0F, 2F, 0F, 0F, 2F, 0F, 0.5F, 1F, 0F, 0.5F, 1F, 1F, 1F, 0F, 0F, 1F, 0F); // Box 81
		attachmentModel[42].setRotationPoint(-14.5F, -14F, -6F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, 0F, 0F, -0.5F, 2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 1F); // Box 82
		attachmentModel[43].setRotationPoint(-18.5F, -8F, -5F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 2F, 0F, 0F, 3.5F, 0F, 0F, -1F, 0F, 0F, -1F, 1F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0.25F, 0F, 0F); // Box 84
		attachmentModel[44].setRotationPoint(-14F, -15F, 4F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 86
		attachmentModel[45].setRotationPoint(-10.5F, -15.5F, -5.5F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 6, 5, 4, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 1F, -1F, -2F, 4F, -1F, -2F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 1F, 0F, -2F, 4F, 0F, -2F); // Box 88
		attachmentModel[46].setRotationPoint(-14.5F, -13F, -7F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, -0.5F, 1F, 0F, -0.5F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 1F, 0F, 2F, 0F, 0F, 2F); // Box 90
		attachmentModel[47].setRotationPoint(-14.5F, -8F, -6F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, -0.5F, -0.5F, -0.12F, -0.5F, -0.5F, 0.12F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -1F, -0.12F, -0.5F, -1F, 0.12F, 0F, 0F, 0F); // Box 93
		attachmentModel[48].setRotationPoint(20.5F, -19F, -0.12F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0.5F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, 0F, 1F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 94
		attachmentModel[49].setRotationPoint(-14.5F, -15F, -4F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 95
		attachmentModel[50].setRotationPoint(-10.5F, -15.5F, 4.5F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 96
		attachmentModel[51].setRotationPoint(-10.5F, -5.5F, -5.5F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 2, 9, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4.5F, 0F, 0F, -4.5F, 0F, 0F, -4.5F, 0F, 0F, -4.5F, 0F); // Box 98
		attachmentModel[52].setRotationPoint(-5.5F, -18.5F, -3F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 2, 9, 4, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, -4.5F, 1F, 0F, -4.5F, -1F, 0F, -4.5F, -1F, 0F, -4.5F, 1F); // Box 99
		attachmentModel[53].setRotationPoint(-3.5F, -18.5F, -2F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 2, 9, 4, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, -4.5F, -1F, 0F, -4.5F, 1F, 0F, -4.5F, 1F, 0F, -4.5F, -1F); // Box 100
		attachmentModel[54].setRotationPoint(-7.5F, -18.5F, -2F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 2F, 2F, 0F, 2F, 2F, 0F, 0F, 0F, 0F, -0.5F, 2F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 101
		attachmentModel[55].setRotationPoint(-18.5F, -13F, -5F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 6, 3, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -2F, 0F, -2F, -2F, 0.5F, -2.5F, 1.5F, 0F, -2.5F, 1.5F); // Box 102
		attachmentModel[56].setRotationPoint(-14.5F, -6F, -4F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 8, 3, 5, 0F, 0F, -1F, -0.2F, 0.5F, 1F, -1.7F, 0.5F, 1F, 1F, 0F, -1F, 0F, 0F, 0F, -0.2F, 0.5F, 0F, -1.7F, 0.5F, 0F, 1F, 0F, 0F, 0F); // Box 103
		attachmentModel[57].setRotationPoint(-11F, -15F, -7F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 2, 6, 9, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4.5F, 0F, 0F, -4.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4.5F, 0F, 0F, -4.5F); // Box 104
		attachmentModel[58].setRotationPoint(-5.5F, -13F, -8.5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 2, 4, 9, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, -4.5F, 0F, -1F, -4.5F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, -4.5F, 0F, -1F, -4.5F); // Box 105
		attachmentModel[59].setRotationPoint(-7.5F, -12F, -8.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 4, 9, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, -4.5F, 0F, 1F, -4.5F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, -4.5F, 0F, 1F, -4.5F); // Box 106
		attachmentModel[60].setRotationPoint(-3.5F, -12F, -8.5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 107
		attachmentModel[61].setRotationPoint(-6.5F, -11F, 5F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 108
		attachmentModel[62].setRotationPoint(-5.5F, -12F, 5F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 109
		attachmentModel[63].setRotationPoint(-5.5F, -9F, 5F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, -1F, 0.5F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, -1F, 0.1111F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 110
		attachmentModel[64].setRotationPoint(-14F, -16F, 1F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 12, 2, 2, 0F, 0F, 0F, 2F, 0.5F, 0.5F, -1F, 0.5F, -0.5F, 0F, 0F, -1F, -3F, 0F, -1F, 3F, 0.5F, -1.5F, 0F, 0.5F, -0.5F, -1F, 0F, 0F, -4F); // Box 111
		attachmentModel[65].setRotationPoint(8.5F, -18F, 1F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 4, 2, 3, 0F, 0F, 0F, 0.3F, 0.5F, 0F, -0.44F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.3F, 0.5F, 0F, -0.44F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 112
		attachmentModel[66].setRotationPoint(-2.5F, -14F, -5F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 12, 2, 2, 0F, 0.5F, -2.5F, 2F, 0F, 0F, -1F, 0F, -1F, 0F, 0.5F, -3.5F, -3F, 0.5F, 1.5F, 3F, 0F, -1F, 0F, 0F, 0F, -1F, 0.5F, 2.5F, -4F); // Box 113
		attachmentModel[67].setRotationPoint(-3.5F, -18F, -2F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 12, 2, 1, 0F, 0F, -1.4F, 0F, 0F, 1F, -2.88F, 0F, 1F, 2.88F, 0F, -1.4F, 0F, 0F, 1F, 0F, 0F, 0F, -2.88F, 0F, 0F, 2.88F, 0F, 1F, 0F); // Box 115
		attachmentModel[68].setRotationPoint(8.5F, -18F, -3F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 117
		attachmentModel[69].setRotationPoint(-10.5F, -5.5F, 4.5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 118
		attachmentModel[70].setRotationPoint(-0.5F, -1F, 4F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 119
		attachmentModel[71].setRotationPoint(-0.5F, 0F, 3.5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 122
		attachmentModel[72].setRotationPoint(15.5F, 0F, 3.5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 143
		attachmentModel[73].setRotationPoint(-14F, -10.75F, 5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 3, 3, 2, 0F, 1F, 1F, -1F, 0F, 2F, 0F, 0F, 0.5F, 0F, -1F, 0F, -0.25F, 1F, 0F, -1F, 0F, 1F, 0F, 0F, -0.5F, 0F, -1F, -1F, -0.25F); // Box 144
		attachmentModel[74].setRotationPoint(-16F, -11F, 4F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 163
		attachmentModel[75].setRotationPoint(-19.5F, -10.75F, -6.5F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 164
		attachmentModel[76].setRotationPoint(-21F, -10.75F, 5.5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 169
		attachmentModel[77].setRotationPoint(-19.5F, -10.75F, 5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 170
		attachmentModel[78].setRotationPoint(-21F, -10.75F, -6.5F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 155
		attachmentModel[79].setRotationPoint(-9F, -10.5F, 4.5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, -4.4F, 0F, 0.864F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.92F, -4.4F, 0F, 0.864F, -0.45F, -0.1F, 0F, -0.95F, 0.9F, 0F, 0F, 0F, -1.92F); // Box 158
		attachmentModel[80].setRotationPoint(12.5F, -16F, 2.88F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, 1F, 1.5F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 2F, 0F, -1F, 0F, -0.5F, 1F, 0F, -0.5F, 1F); // Box 160
		attachmentModel[81].setRotationPoint(-11F, -14F, 4F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, -0.5F, 0.5F, 0.75F, -0.5F, 0.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, -1F, 1.5F, -1F, -1F, 0F, -1F, 1F, 0F, -1F, 1F); // Box 161
		attachmentModel[82].setRotationPoint(-11F, -15F, 2F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 2, 4, 2, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 162
		attachmentModel[83].setRotationPoint(-11F, -12F, 5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 2F, 1.5F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 163
		attachmentModel[84].setRotationPoint(-11F, -8F, 5F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, -0.5F, 1.5F, 0.75F, -0.5F, 1.5F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 164
		attachmentModel[85].setRotationPoint(-11F, -6F, 3F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, -0.5F, -0.5F, -0.12F, -0.5F, -0.5F, 0.12F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -1F, -0.12F, -0.5F, -1F, 0.12F, 0F, 0F, 0F); // Box 165
		attachmentModel[86].setRotationPoint(20.5F, -19F, 2.88F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0.5F, 0F, -0.44F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 0.44F, 0.5F, 0F, -1.44F, 0F, 0F, -3F, 0F, 0F, 3F, 0.5F, 0F, 1.44F); // Box 169
		attachmentModel[87].setRotationPoint(2.5F, -12F, -5F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, -1F, 0.1111F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, -1F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 170
		attachmentModel[88].setRotationPoint(-14F, -16F, -4F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 3.5F, 0F, 0F, 2F, 0.25F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 1F, 0F); // Box 171
		attachmentModel[89].setRotationPoint(-14F, -15F, -5F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.25F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 3.5F, 0F, 0F, 2F); // Box 172
		attachmentModel[90].setRotationPoint(-14F, -6F, -5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -1F, 0.1111F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, -1F, 0.5F, 0.5F); // Box 173
		attachmentModel[91].setRotationPoint(-14F, -5F, -4F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0.5F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, -1F, 0.1111F, 0F); // Box 174
		attachmentModel[92].setRotationPoint(-14F, -5F, 1F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0.2F, 0.4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.2F, 0.4F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 155
		attachmentModel[93].setRotationPoint(15.5F, -2F, -2F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 10, 4, 1, 0F, 0F, 0F, -4F, 2F, 0F, -4F, 1.5F, -1F, 3F, 0F, -1F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 156
		attachmentModel[94].setRotationPoint(8.5F, -16F, -6F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 6, 4, 1, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4F, 0F, 0F, -4F, 0.5F, -1F, 3F, 0F, -1F, 3F); // Box 157
		attachmentModel[95].setRotationPoint(8.5F, -8F, -6F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 6, 4, 1, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 3F, 0.5F, -1F, 3F, 0F, 0F, -4F, 0F, 0F, -4F); // Box 158
		attachmentModel[96].setRotationPoint(8.5F, -8F, 5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 159
		attachmentModel[97].setRotationPoint(-4.5F, -5F, -2F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 20, 3, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 165
		attachmentModel[98].setRotationPoint(-22.5F, -15F, -5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 20, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F); // Box 166
		attachmentModel[99].setRotationPoint(-22.5F, -8F, -5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 20, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 167
		attachmentModel[100].setRotationPoint(-22.5F, -8F, 4F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F); // Box 168
		attachmentModel[101].setRotationPoint(-20F, -10.75F, 5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 169
		attachmentModel[102].setRotationPoint(-20F, -10.75F, -7F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 170
		attachmentModel[103].setRotationPoint(-20F, -10.75F, -6F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, -1F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -2F, 2F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 174
		attachmentModel[104].setRotationPoint(6.5F, -16F, -5F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, -1F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -2F, 2F); // Box 175
		attachmentModel[105].setRotationPoint(6.5F, -8F, -5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -2F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, -1F, -3F); // Box 176
		attachmentModel[106].setRotationPoint(6.5F, -8F, 4F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 1, 3, 4, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 154
		attachmentModel[107].setRotationPoint(13.5F, -8F, -2F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 1, 3, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 155
		attachmentModel[108].setRotationPoint(13.5F, -15F, -2F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 1, 4, 10, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 156
		attachmentModel[109].setRotationPoint(13.5F, -12F, -5F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 9, 3, 4, 0F, 0F, -0.9F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.9F, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 2F); // Box 172
		attachmentModel[110].setRotationPoint(-2.5F, -15F, -2F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 9, 3, 4, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F); // Box 173
		attachmentModel[111].setRotationPoint(-2.5F, -8F, -2F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 174
		attachmentModel[112].setRotationPoint(-0.5F, 1F, 4F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 175
		attachmentModel[113].setRotationPoint(15.5F, 1F, 4F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 176
		attachmentModel[114].setRotationPoint(15.5F, -1F, 4F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0F, 0F, 0.8F, 0.5F, 0F, -0.7F, 0.5F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -0.4F, 0.5F, 0F, -1.9F, 0.5F, 0F, 1F, 0F, 0F, 0F); // Box 178
		attachmentModel[115].setRotationPoint(-11F, -12F, -6F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0.3F, 0.5F, 0F, -0.44F, 0.5F, 0F, 0.44F, 0F, 0F, 0F, 0F, 0F, -0.7F, 0.5F, 0F, -1.44F, 0.5F, 0F, 1.44F, 0F, 0F, 1F); // Box 179
		attachmentModel[116].setRotationPoint(-2.5F, -12F, -5F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 6, 4, 1, 0F, 0.5F, -0.7F, 1.56F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0.5F, -0.7F, -1.56F, 0.5F, 0F, 1.56F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -1.56F); // Box 181
		attachmentModel[117].setRotationPoint(2.5F, -16F, -3F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 18, 2, 1, 0F, 0.5F, -0.7F, 1.06F, 0F, 3F, -3.38F, 0F, 3F, 3.38F, 0.5F, -0.7F, -1.06F, 0.5F, 0F, 1.06F, 0F, -2F, -3.38F, 0F, -2F, 3.38F, 0.5F, 0F, -1.06F); // Box 187
		attachmentModel[118].setRotationPoint(2.5F, -16F, -0.5F);

		pupilRefCenter.set(-21.5F /16F, 10F / 16F, 0F);
		scopeGlassModelScale.set(1F, 0.02F, 0.02F);
		scopeObjectiveLensCenter.set(15.5F / 160F, 10F / 160F, 0F);
		scopeMaskMultRotY = scopeMaskMultRotZ = 0.375F;
		scopeMaskMultTransY = scopeMaskMultTransZ = 0.3F;
		scopeMaskScale = 0.29F / 1600F;
		minPupilDistance = 0.01068F;
		scopeMaskAlphaDivisor[0] = 0.000045F;
		reticleAt[0].set(4096F, 10F / 160F, 0F);
		reticleScale = 1.6F / 160F;

		reticleBorder = new Vector3f[6];
		reticleBorder[0] = new Vector3f(19.5F / 160F, 12F / 160F, -6F / 160F);
		reticleBorder[1] = new Vector3f(19.5F / 160F, 16F / 160F, -2F / 160F);
		reticleBorder[2] = new Vector3f(19.5F / 160F, 16F / 160F, 2F / 160F);
		reticleBorder[3] = new Vector3f(19.5F / 160F, 12F / 160F, 6F / 160F);
		reticleBorder[4] = new Vector3f(19.5F / 160F, 4F / 160F, 6F / 160F);
		reticleBorder[5] = new Vector3f(19.5F / 160F, 4F / 160F, -6F / 160F);

		flipAll();
	}
}