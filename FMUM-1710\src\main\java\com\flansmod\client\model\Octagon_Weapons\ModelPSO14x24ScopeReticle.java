//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: PSO-1 4x24 Scope - Division
// Model Creator: 
// Created on: 22.12.2019 - 13:22:03
// Last changed on: 22.12.2019 - 13:22:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelDefault;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelPSO14x24ScopeReticle extends ModelDefault //Same as Filename
{
	int textureX = 512;
	int textureY = 512;

	public ModelPSO14x24ScopeReticle() //Same as Filename
	{
		defaultModel = new ModelRendererTurbo[39];
		defaultModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 105
		defaultModel[1] = new ModelRendererTurbo(this, 6, 1, textureX, textureY); // Box 106
		defaultModel[2] = new ModelRendererTurbo(this, 11, 1, textureX, textureY); // Box 107
		defaultModel[3] = new ModelRendererTurbo(this, 16, 1, textureX, textureY); // Box 108
		defaultModel[4] = new ModelRendererTurbo(this, 21, 1, textureX, textureY); // Box 109
		defaultModel[5] = new ModelRendererTurbo(this, 26, 1, textureX, textureY); // Box 110
		defaultModel[6] = new ModelRendererTurbo(this, 31, 1, textureX, textureY); // Box 111
		defaultModel[7] = new ModelRendererTurbo(this, 36, 1, textureX, textureY); // Box 112
		defaultModel[8] = new ModelRendererTurbo(this, 1, 10, textureX, textureY); // Box 113
		defaultModel[9] = new ModelRendererTurbo(this, 1, 4, textureX, textureY); // Box 114
		defaultModel[10] = new ModelRendererTurbo(this, 1, 7, textureX, textureY); // Box 115
		defaultModel[11] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 116
		defaultModel[12] = new ModelRendererTurbo(this, 46, 1, textureX, textureY); // Box 117
		defaultModel[13] = new ModelRendererTurbo(this, 51, 1, textureX, textureY); // Box 118
		defaultModel[14] = new ModelRendererTurbo(this, 56, 1, textureX, textureY); // Box 119
		defaultModel[15] = new ModelRendererTurbo(this, 61, 1, textureX, textureY); // Box 120
		defaultModel[16] = new ModelRendererTurbo(this, 1, 16, textureX, textureY); // Box 121
		defaultModel[17] = new ModelRendererTurbo(this, 1, 13, textureX, textureY); // Box 122
		defaultModel[18] = new ModelRendererTurbo(this, 1, 19, textureX, textureY); // Box 123
		defaultModel[19] = new ModelRendererTurbo(this, 1, 55, textureX, textureY); // Box 124
		defaultModel[20] = new ModelRendererTurbo(this, 1, 58, textureX, textureY); // Box 125
		defaultModel[21] = new ModelRendererTurbo(this, 1, 61, textureX, textureY); // Box 126
		defaultModel[22] = new ModelRendererTurbo(this, 1, 64, textureX, textureY); // Box 127
		defaultModel[23] = new ModelRendererTurbo(this, 1, 79, textureX, textureY); // Box 128
		defaultModel[24] = new ModelRendererTurbo(this, 1, 22, textureX, textureY); // Box 129
		defaultModel[25] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 130
		defaultModel[26] = new ModelRendererTurbo(this, 1, 28, textureX, textureY); // Box 131
		defaultModel[27] = new ModelRendererTurbo(this, 1, 43, textureX, textureY); // Box 132
		defaultModel[28] = new ModelRendererTurbo(this, 1, 70, textureX, textureY); // Box 133
		defaultModel[29] = new ModelRendererTurbo(this, 1, 76, textureX, textureY); // Box 134
		defaultModel[30] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 135
		defaultModel[31] = new ModelRendererTurbo(this, 1, 67, textureX, textureY); // Box 136
		defaultModel[32] = new ModelRendererTurbo(this, 1, 31, textureX, textureY); // Box 137
		defaultModel[33] = new ModelRendererTurbo(this, 1, 34, textureX, textureY); // Box 138
		defaultModel[34] = new ModelRendererTurbo(this, 1, 37, textureX, textureY); // Box 139
		defaultModel[35] = new ModelRendererTurbo(this, 1, 40, textureX, textureY); // Box 140
		defaultModel[36] = new ModelRendererTurbo(this, 1, 46, textureX, textureY); // Box 141
		defaultModel[37] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 142
		defaultModel[38] = new ModelRendererTurbo(this, 1, 52, textureX, textureY); // Box 143

		defaultModel[0].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.4F, -0.7F, 0F, -0.4F, -0.7F, 0F, -0.9F, 0F, 0F, -0.9F, 0F, 0F, -0.5F, -0.7F, 0F, -0.5F, -0.7F); // Box 105
		defaultModel[0].setRotationPoint(0F, -8.88178419700125E-16F, 0F);

		defaultModel[1].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.4F, -0.7F, 0F, -0.4F, -0.7F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.7F, 0F, -0.5F, -0.7F, 0F, -0.9F, 0F, 0F, -0.9F, 0F); // Box 106
		defaultModel[1].setRotationPoint(0F, -8.88178419700125E-16F, -1F);

		defaultModel[2].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.3F, -0.8F, 0F, -0.3F, -0.8F, 0F, -0.9F, 0F, 0F, -0.9F, 0F, 0F, -0.6F, -0.8F, 0F, -0.6F, -0.8F); // Box 107
		defaultModel[2].setRotationPoint(0F, 0.499999999999999F, 0F);

		defaultModel[3].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.3F, -0.8F, 0F, -0.3F, -0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.6F, -0.8F, 0F, -0.6F, -0.8F, 0F, -0.9F, 0F, 0F, -0.9F, 0F); // Box 108
		defaultModel[3].setRotationPoint(0F, 0.499999999999999F, -1F);

		defaultModel[4].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.3F, -0.8F, 0F, -0.3F, -0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.6F, -0.8F, 0F, -0.6F, -0.8F, 0F, -0.9F, 0F, 0F, -0.9F, 0F); // Box 109
		defaultModel[4].setRotationPoint(0F, 0.999999999999999F, -1F);

		defaultModel[5].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.3F, -0.8F, 0F, -0.3F, -0.8F, 0F, -0.9F, 0F, 0F, -0.9F, 0F, 0F, -0.6F, -0.8F, 0F, -0.6F, -0.8F); // Box 110
		defaultModel[5].setRotationPoint(0F, 0.999999999999999F, 0F);

		defaultModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 111
		defaultModel[6].setRotationPoint(0F, -8.88178419700125E-16F, -1.2F);

		defaultModel[7].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 112
		defaultModel[7].setRotationPoint(0F, -8.88178419700125E-16F, -1.4F);

		defaultModel[8].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 113
		defaultModel[8].setRotationPoint(0F, -8.88178419700125E-16F, -1.6F);

		defaultModel[9].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 114
		defaultModel[9].setRotationPoint(0F, -8.88178419700125E-16F, -1.8F);

		defaultModel[10].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.6F, -0.94F, 0F, -0.6F, -0.94F, 0F, -0.6F, 0F, 0F, -0.6F, 0F); // Box 115
		defaultModel[10].setRotationPoint(0F, -8.88178419700125E-16F, -2F);

		defaultModel[11].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 116
		defaultModel[11].setRotationPoint(0F, -8.88178419700125E-16F, -2.2F);

		defaultModel[12].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 117
		defaultModel[12].setRotationPoint(0F, -8.88178419700125E-16F, -2.4F);

		defaultModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 118
		defaultModel[13].setRotationPoint(0F, -8.88178419700125E-16F, -2.6F);

		defaultModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F); // Box 119
		defaultModel[14].setRotationPoint(0F, -8.88178419700125E-16F, -2.8F);

		defaultModel[15].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.6F, -0.92F, 0F, -0.6F, -0.92F, 0F, -0.6F, 0F, 0F, -0.6F, 0F); // Box 120
		defaultModel[15].setRotationPoint(0F, -0.04F, -3F);

		defaultModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, -0.2F, 0F, -0.92F, -0.2F, 0F, -0.92F, 0F, 0F, -0.92F, 0F); // Box 121
		defaultModel[16].setRotationPoint(0F, -0.04F, -3F);

		defaultModel[17].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 122
		defaultModel[17].setRotationPoint(0F, -8.88178419700125E-16F, 0.2F);

		defaultModel[18].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 123
		defaultModel[18].setRotationPoint(0F, -8.88178419700125E-16F, 0.4F);

		defaultModel[19].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 124
		defaultModel[19].setRotationPoint(0F, -8.88178419700125E-16F, 0.6F);

		defaultModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 125
		defaultModel[20].setRotationPoint(0F, -8.88178419700125E-16F, 0.8F);

		defaultModel[21].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 126
		defaultModel[21].setRotationPoint(0F, -8.88178419700125E-16F, 1.8F);

		defaultModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 127
		defaultModel[22].setRotationPoint(0F, -8.88178419700125E-16F, 1.6F);

		defaultModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 128
		defaultModel[23].setRotationPoint(0F, -8.88178419700125E-16F, 1.4F);

		defaultModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.8F, 0F, 0F, -0.8F, 0F, 0F, -0.8F, -0.94F, 0F, -0.8F, -0.94F); // Box 129
		defaultModel[24].setRotationPoint(0F, -8.88178419700125E-16F, 1.2F);

		defaultModel[25].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.94F, 0F, 0F, -0.94F, 0F, -0.6F, 0F, 0F, -0.6F, 0F, 0F, -0.6F, -0.94F, 0F, -0.6F, -0.94F); // Box 130
		defaultModel[25].setRotationPoint(0F, -8.88178419700125E-16F, 1F);

		defaultModel[26].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.6F, 0F, 0F, -0.6F, 0F, 0F, -0.6F, -0.92F, 0F, -0.6F, -0.92F); // Box 131
		defaultModel[26].setRotationPoint(0F, -0.04F, 2F);

		defaultModel[27].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, -0.2F, 0F, -0.92F, -0.2F); // Box 132
		defaultModel[27].setRotationPoint(0F, -0.04F, 2F);

		defaultModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.7F, 0F, 0F, -0.7F, 0F, 0F, -0.7F, -0.92F, 0F, -0.7F, -0.92F); // Box 133
		defaultModel[28].setRotationPoint(0F, 0.399999999999999F, 2.2F);

		defaultModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.7F, 0F, 0F, -0.7F, 0F, 0F, -0.7F, -0.92F, 0F, -0.7F, -0.92F); // Box 134
		defaultModel[29].setRotationPoint(0F, 0.399999999999999F, 2F);

		defaultModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.7F, 0F, 0F, -0.7F, 0F, 0F, -0.7F, -0.92F, 0F, -0.7F, -0.92F); // Box 135
		defaultModel[30].setRotationPoint(0F, 0.399999999999999F, 1.84F);

		defaultModel[31].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, -0.92F, 0F, -0.92F, -0.92F); // Box 136
		defaultModel[31].setRotationPoint(0F, 0.399999999999999F, 1.92F);

		defaultModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, -0.92F, 0F, -0.92F, -0.92F); // Box 137
		defaultModel[32].setRotationPoint(0F, 0.399999999999999F, -2.16F);

		defaultModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, -0.92F, 0F, -0.92F, -0.92F); // Box 138
		defaultModel[33].setRotationPoint(0F, 0.619999999999998F, 1.92F);

		defaultModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.7F, 0F, 0F, -0.7F, 0F, 0F, -0.7F, -0.92F, 0F, -0.7F, -0.92F); // Box 139
		defaultModel[34].setRotationPoint(0F, 0.399999999999999F, -2.24F);

		defaultModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.7F, 0F, 0F, -0.7F, 0F, 0F, -0.7F, -0.92F, 0F, -0.7F, -0.92F); // Box 140
		defaultModel[35].setRotationPoint(0F, 0.399999999999999F, -2.08F);

		defaultModel[36].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, -0.92F, 0F, -0.92F, -0.92F); // Box 141
		defaultModel[36].setRotationPoint(0F, 0.619999999999998F, -2.16F);

		defaultModel[37].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, -0.7F, 0F, 0F, -0.7F, 0F, 0F, -0.7F, -0.92F, 0F, -0.7F, -0.92F); // Box 142
		defaultModel[37].setRotationPoint(0F, 0.399999999999999F, -1.88F);

		defaultModel[38].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.92F, 0F, 0F, -0.92F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.92F, 0F, 0.5F, -0.92F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 143
		defaultModel[38].setRotationPoint(0F, 1.5F, -0.96F);

		flipAll();
	}
}