//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC WTS-36 MK1 Handguard
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36MK1Handguard extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelCICWTS36MK1Handguard() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[232];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 220
		attachmentModel[1] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 459
		attachmentModel[2] = new ModelRendererTurbo(this, 145, 1, textureX, textureY); // Box 460
		attachmentModel[3] = new ModelRendererTurbo(this, 185, 1, textureX, textureY); // Box 461
		attachmentModel[4] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 462
		attachmentModel[5] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 463
		attachmentModel[6] = new ModelRendererTurbo(this, 25, 17, textureX, textureY); // Box 464
		attachmentModel[7] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 353
		attachmentModel[8] = new ModelRendererTurbo(this, 217, 1, textureX, textureY); // Box 419
		attachmentModel[9] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 420
		attachmentModel[10] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 421
		attachmentModel[11] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 422
		attachmentModel[12] = new ModelRendererTurbo(this, 217, 9, textureX, textureY); // Box 423
		attachmentModel[13] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 429
		attachmentModel[14] = new ModelRendererTurbo(this, 233, 9, textureX, textureY); // Box 450
		attachmentModel[15] = new ModelRendererTurbo(this, 201, 9, textureX, textureY); // Box 451
		attachmentModel[16] = new ModelRendererTurbo(this, 249, 9, textureX, textureY); // Box 452
		attachmentModel[17] = new ModelRendererTurbo(this, 137, 17, textureX, textureY); // Box 453
		attachmentModel[18] = new ModelRendererTurbo(this, 153, 17, textureX, textureY); // Box 454
		attachmentModel[19] = new ModelRendererTurbo(this, 161, 17, textureX, textureY); // Box 455
		attachmentModel[20] = new ModelRendererTurbo(this, 177, 17, textureX, textureY); // Box 456
		attachmentModel[21] = new ModelRendererTurbo(this, 185, 17, textureX, textureY); // Box 457
		attachmentModel[22] = new ModelRendererTurbo(this, 193, 17, textureX, textureY); // Box 458
		attachmentModel[23] = new ModelRendererTurbo(this, 217, 17, textureX, textureY); // Box 459
		attachmentModel[24] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 460
		attachmentModel[25] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 461
		attachmentModel[26] = new ModelRendererTurbo(this, 249, 17, textureX, textureY); // Box 462
		attachmentModel[27] = new ModelRendererTurbo(this, 121, 25, textureX, textureY); // Box 463
		attachmentModel[28] = new ModelRendererTurbo(this, 137, 25, textureX, textureY); // Box 464
		attachmentModel[29] = new ModelRendererTurbo(this, 145, 25, textureX, textureY); // Box 465
		attachmentModel[30] = new ModelRendererTurbo(this, 161, 25, textureX, textureY); // Box 466
		attachmentModel[31] = new ModelRendererTurbo(this, 169, 25, textureX, textureY); // Box 467
		attachmentModel[32] = new ModelRendererTurbo(this, 177, 25, textureX, textureY); // Box 468
		attachmentModel[33] = new ModelRendererTurbo(this, 193, 25, textureX, textureY); // Box 469
		attachmentModel[34] = new ModelRendererTurbo(this, 201, 25, textureX, textureY); // Box 470
		attachmentModel[35] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 471
		attachmentModel[36] = new ModelRendererTurbo(this, 225, 25, textureX, textureY); // Box 472
		attachmentModel[37] = new ModelRendererTurbo(this, 233, 25, textureX, textureY); // Box 473
		attachmentModel[38] = new ModelRendererTurbo(this, 249, 25, textureX, textureY); // Box 474
		attachmentModel[39] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 475
		attachmentModel[40] = new ModelRendererTurbo(this, 17, 33, textureX, textureY); // Box 476
		attachmentModel[41] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 477
		attachmentModel[42] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 478
		attachmentModel[43] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 479
		attachmentModel[44] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 480
		attachmentModel[45] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 481
		attachmentModel[46] = new ModelRendererTurbo(this, 81, 33, textureX, textureY); // Box 482
		attachmentModel[47] = new ModelRendererTurbo(this, 89, 33, textureX, textureY); // Box 483
		attachmentModel[48] = new ModelRendererTurbo(this, 105, 33, textureX, textureY); // Box 484
		attachmentModel[49] = new ModelRendererTurbo(this, 113, 33, textureX, textureY); // Box 490
		attachmentModel[50] = new ModelRendererTurbo(this, 137, 33, textureX, textureY); // Box 492
		attachmentModel[51] = new ModelRendererTurbo(this, 153, 33, textureX, textureY); // Box 495
		attachmentModel[52] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 498
		attachmentModel[53] = new ModelRendererTurbo(this, 177, 33, textureX, textureY); // Box 524
		attachmentModel[54] = new ModelRendererTurbo(this, 193, 33, textureX, textureY); // Box 525
		attachmentModel[55] = new ModelRendererTurbo(this, 201, 33, textureX, textureY); // Box 526
		attachmentModel[56] = new ModelRendererTurbo(this, 209, 33, textureX, textureY); // Box 527
		attachmentModel[57] = new ModelRendererTurbo(this, 217, 33, textureX, textureY); // Box 528
		attachmentModel[58] = new ModelRendererTurbo(this, 233, 33, textureX, textureY); // Box 529
		attachmentModel[59] = new ModelRendererTurbo(this, 249, 33, textureX, textureY); // Box 530
		attachmentModel[60] = new ModelRendererTurbo(this, 89, 41, textureX, textureY); // Box 531
		attachmentModel[61] = new ModelRendererTurbo(this, 97, 41, textureX, textureY); // Box 532
		attachmentModel[62] = new ModelRendererTurbo(this, 105, 41, textureX, textureY); // Box 533
		attachmentModel[63] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 534
		attachmentModel[64] = new ModelRendererTurbo(this, 137, 41, textureX, textureY); // Box 535
		attachmentModel[65] = new ModelRendererTurbo(this, 145, 41, textureX, textureY); // Box 536
		attachmentModel[66] = new ModelRendererTurbo(this, 177, 41, textureX, textureY); // Box 537
		attachmentModel[67] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 538
		attachmentModel[68] = new ModelRendererTurbo(this, 201, 41, textureX, textureY); // Box 539
		attachmentModel[69] = new ModelRendererTurbo(this, 217, 41, textureX, textureY); // Box 540
		attachmentModel[70] = new ModelRendererTurbo(this, 225, 41, textureX, textureY); // Box 541
		attachmentModel[71] = new ModelRendererTurbo(this, 233, 41, textureX, textureY); // Box 542
		attachmentModel[72] = new ModelRendererTurbo(this, 241, 41, textureX, textureY); // Box 543
		attachmentModel[73] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 544
		attachmentModel[74] = new ModelRendererTurbo(this, 17, 49, textureX, textureY); // Box 545
		attachmentModel[75] = new ModelRendererTurbo(this, 25, 49, textureX, textureY); // Box 546
		attachmentModel[76] = new ModelRendererTurbo(this, 33, 49, textureX, textureY); // Box 547
		attachmentModel[77] = new ModelRendererTurbo(this, 41, 49, textureX, textureY); // Box 548
		attachmentModel[78] = new ModelRendererTurbo(this, 57, 49, textureX, textureY); // Box 555
		attachmentModel[79] = new ModelRendererTurbo(this, 65, 49, textureX, textureY); // Box 556
		attachmentModel[80] = new ModelRendererTurbo(this, 73, 49, textureX, textureY); // Box 557
		attachmentModel[81] = new ModelRendererTurbo(this, 89, 49, textureX, textureY); // Box 559
		attachmentModel[82] = new ModelRendererTurbo(this, 105, 49, textureX, textureY); // Box 560
		attachmentModel[83] = new ModelRendererTurbo(this, 113, 49, textureX, textureY); // Box 561
		attachmentModel[84] = new ModelRendererTurbo(this, 129, 49, textureX, textureY); // Box 562
		attachmentModel[85] = new ModelRendererTurbo(this, 145, 49, textureX, textureY); // Box 563
		attachmentModel[86] = new ModelRendererTurbo(this, 161, 49, textureX, textureY); // Box 564
		attachmentModel[87] = new ModelRendererTurbo(this, 177, 49, textureX, textureY); // Box 565
		attachmentModel[88] = new ModelRendererTurbo(this, 185, 49, textureX, textureY); // Box 566
		attachmentModel[89] = new ModelRendererTurbo(this, 193, 49, textureX, textureY); // Box 567
		attachmentModel[90] = new ModelRendererTurbo(this, 201, 49, textureX, textureY); // Box 568
		attachmentModel[91] = new ModelRendererTurbo(this, 217, 49, textureX, textureY); // Box 569
		attachmentModel[92] = new ModelRendererTurbo(this, 233, 49, textureX, textureY); // Box 570
		attachmentModel[93] = new ModelRendererTurbo(this, 241, 49, textureX, textureY); // Box 571
		attachmentModel[94] = new ModelRendererTurbo(this, 249, 49, textureX, textureY); // Box 572
		attachmentModel[95] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 573
		attachmentModel[96] = new ModelRendererTurbo(this, 17, 57, textureX, textureY); // Box 574
		attachmentModel[97] = new ModelRendererTurbo(this, 33, 57, textureX, textureY); // Box 575
		attachmentModel[98] = new ModelRendererTurbo(this, 41, 57, textureX, textureY); // Box 576
		attachmentModel[99] = new ModelRendererTurbo(this, 49, 57, textureX, textureY); // Box 577
		attachmentModel[100] = new ModelRendererTurbo(this, 57, 57, textureX, textureY); // Box 578
		attachmentModel[101] = new ModelRendererTurbo(this, 73, 57, textureX, textureY); // Box 579
		attachmentModel[102] = new ModelRendererTurbo(this, 89, 57, textureX, textureY); // Box 580
		attachmentModel[103] = new ModelRendererTurbo(this, 97, 57, textureX, textureY); // Box 581
		attachmentModel[104] = new ModelRendererTurbo(this, 105, 57, textureX, textureY); // Box 582
		attachmentModel[105] = new ModelRendererTurbo(this, 113, 57, textureX, textureY); // Box 583
		attachmentModel[106] = new ModelRendererTurbo(this, 129, 57, textureX, textureY); // Box 584
		attachmentModel[107] = new ModelRendererTurbo(this, 145, 57, textureX, textureY); // Box 585
		attachmentModel[108] = new ModelRendererTurbo(this, 153, 57, textureX, textureY); // Box 586
		attachmentModel[109] = new ModelRendererTurbo(this, 161, 57, textureX, textureY); // Box 587
		attachmentModel[110] = new ModelRendererTurbo(this, 169, 57, textureX, textureY); // Box 588
		attachmentModel[111] = new ModelRendererTurbo(this, 185, 57, textureX, textureY); // Box 589
		attachmentModel[112] = new ModelRendererTurbo(this, 217, 57, textureX, textureY); // Box 590
		attachmentModel[113] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 591
		attachmentModel[114] = new ModelRendererTurbo(this, 33, 65, textureX, textureY); // Box 592
		attachmentModel[115] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 593
		attachmentModel[116] = new ModelRendererTurbo(this, 145, 65, textureX, textureY); // Box 594
		attachmentModel[117] = new ModelRendererTurbo(this, 177, 65, textureX, textureY); // Box 595
		attachmentModel[118] = new ModelRendererTurbo(this, 209, 65, textureX, textureY); // Box 596
		attachmentModel[119] = new ModelRendererTurbo(this, 225, 65, textureX, textureY); // Box 597
		attachmentModel[120] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 598
		attachmentModel[121] = new ModelRendererTurbo(this, 9, 73, textureX, textureY); // Box 599
		attachmentModel[122] = new ModelRendererTurbo(this, 249, 57, textureX, textureY); // Box 600
		attachmentModel[123] = new ModelRendererTurbo(this, 169, 65, textureX, textureY); // Box 601
		attachmentModel[124] = new ModelRendererTurbo(this, 49, 73, textureX, textureY); // Box 602
		attachmentModel[125] = new ModelRendererTurbo(this, 73, 73, textureX, textureY); // Box 603
		attachmentModel[126] = new ModelRendererTurbo(this, 113, 73, textureX, textureY); // Box 604
		attachmentModel[127] = new ModelRendererTurbo(this, 137, 73, textureX, textureY); // Box 605
		attachmentModel[128] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 606
		attachmentModel[129] = new ModelRendererTurbo(this, 249, 65, textureX, textureY); // Box 607
		attachmentModel[130] = new ModelRendererTurbo(this, 161, 81, textureX, textureY); // Box 608
		attachmentModel[131] = new ModelRendererTurbo(this, 73, 89, textureX, textureY); // Box 612
		attachmentModel[132] = new ModelRendererTurbo(this, 161, 89, textureX, textureY); // Box 648
		attachmentModel[133] = new ModelRendererTurbo(this, 33, 73, textureX, textureY); // Box 649
		attachmentModel[134] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 650
		attachmentModel[135] = new ModelRendererTurbo(this, 33, 81, textureX, textureY); // Box 651
		attachmentModel[136] = new ModelRendererTurbo(this, 201, 65, textureX, textureY); // Box 672
		attachmentModel[137] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 673
		attachmentModel[138] = new ModelRendererTurbo(this, 225, 65, textureX, textureY); // Box 674
		attachmentModel[139] = new ModelRendererTurbo(this, 17, 73, textureX, textureY); // Box 675
		attachmentModel[140] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 676
		attachmentModel[141] = new ModelRendererTurbo(this, 17, 97, textureX, textureY); // Box 677
		attachmentModel[142] = new ModelRendererTurbo(this, 17, 81, textureX, textureY); // Box 678
		attachmentModel[143] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 679
		attachmentModel[144] = new ModelRendererTurbo(this, 41, 89, textureX, textureY); // Box 680
		attachmentModel[145] = new ModelRendererTurbo(this, 33, 97, textureX, textureY); // Box 681
		attachmentModel[146] = new ModelRendererTurbo(this, 49, 97, textureX, textureY); // Box 682
		attachmentModel[147] = new ModelRendererTurbo(this, 249, 89, textureX, textureY); // Box 683
		attachmentModel[148] = new ModelRendererTurbo(this, 65, 97, textureX, textureY); // Box 684
		attachmentModel[149] = new ModelRendererTurbo(this, 73, 97, textureX, textureY); // Box 685
		attachmentModel[150] = new ModelRendererTurbo(this, 81, 97, textureX, textureY); // Box 686
		attachmentModel[151] = new ModelRendererTurbo(this, 97, 97, textureX, textureY); // Box 687
		attachmentModel[152] = new ModelRendererTurbo(this, 113, 97, textureX, textureY); // Box 688
		attachmentModel[153] = new ModelRendererTurbo(this, 121, 97, textureX, textureY); // Box 689
		attachmentModel[154] = new ModelRendererTurbo(this, 129, 97, textureX, textureY); // Box 690
		attachmentModel[155] = new ModelRendererTurbo(this, 137, 97, textureX, textureY); // Box 691
		attachmentModel[156] = new ModelRendererTurbo(this, 153, 97, textureX, textureY); // Box 692
		attachmentModel[157] = new ModelRendererTurbo(this, 169, 97, textureX, textureY); // Box 693
		attachmentModel[158] = new ModelRendererTurbo(this, 177, 97, textureX, textureY); // Box 694
		attachmentModel[159] = new ModelRendererTurbo(this, 185, 97, textureX, textureY); // Box 695
		attachmentModel[160] = new ModelRendererTurbo(this, 193, 97, textureX, textureY); // Box 696
		attachmentModel[161] = new ModelRendererTurbo(this, 201, 97, textureX, textureY); // Box 702
		attachmentModel[162] = new ModelRendererTurbo(this, 241, 97, textureX, textureY); // Box 391
		attachmentModel[163] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 392
		attachmentModel[164] = new ModelRendererTurbo(this, 17, 105, textureX, textureY); // Box 393
		attachmentModel[165] = new ModelRendererTurbo(this, 33, 105, textureX, textureY); // Box 394
		attachmentModel[166] = new ModelRendererTurbo(this, 49, 105, textureX, textureY); // Box 395
		attachmentModel[167] = new ModelRendererTurbo(this, 65, 105, textureX, textureY); // Box 396
		attachmentModel[168] = new ModelRendererTurbo(this, 81, 105, textureX, textureY); // Box 397
		attachmentModel[169] = new ModelRendererTurbo(this, 121, 105, textureX, textureY); // Box 398
		attachmentModel[170] = new ModelRendererTurbo(this, 137, 105, textureX, textureY); // Box 399
		attachmentModel[171] = new ModelRendererTurbo(this, 161, 105, textureX, textureY); // Box 400
		attachmentModel[172] = new ModelRendererTurbo(this, 201, 105, textureX, textureY); // Box 401
		attachmentModel[173] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 402
		attachmentModel[174] = new ModelRendererTurbo(this, 25, 113, textureX, textureY); // Box 403
		attachmentModel[175] = new ModelRendererTurbo(this, 49, 113, textureX, textureY); // Box 404
		attachmentModel[176] = new ModelRendererTurbo(this, 233, 105, textureX, textureY); // Box 405
		attachmentModel[177] = new ModelRendererTurbo(this, 73, 113, textureX, textureY); // Box 406
		attachmentModel[178] = new ModelRendererTurbo(this, 89, 121, textureX, textureY); // Box 407
		attachmentModel[179] = new ModelRendererTurbo(this, 153, 105, textureX, textureY); // Box 408
		attachmentModel[180] = new ModelRendererTurbo(this, 153, 121, textureX, textureY); // Box 409
		attachmentModel[181] = new ModelRendererTurbo(this, 193, 121, textureX, textureY); // Box 410
		attachmentModel[182] = new ModelRendererTurbo(this, 49, 129, textureX, textureY); // Box 411
		attachmentModel[183] = new ModelRendererTurbo(this, 81, 137, textureX, textureY); // Box 412
		attachmentModel[184] = new ModelRendererTurbo(this, 233, 129, textureX, textureY); // Box 413
		attachmentModel[185] = new ModelRendererTurbo(this, 113, 137, textureX, textureY); // Box 414
		attachmentModel[186] = new ModelRendererTurbo(this, 89, 113, textureX, textureY); // Box 415
		attachmentModel[187] = new ModelRendererTurbo(this, 193, 105, textureX, textureY); // Box 416
		attachmentModel[188] = new ModelRendererTurbo(this, 177, 1, textureX, textureY); // Box 417
		attachmentModel[189] = new ModelRendererTurbo(this, 241, 113, textureX, textureY); // Box 418
		attachmentModel[190] = new ModelRendererTurbo(this, 145, 137, textureX, textureY); // Box 419
		attachmentModel[191] = new ModelRendererTurbo(this, 161, 137, textureX, textureY); // Box 420
		attachmentModel[192] = new ModelRendererTurbo(this, 177, 137, textureX, textureY); // Box 421
		attachmentModel[193] = new ModelRendererTurbo(this, 241, 9, textureX, textureY); // Box 422
		attachmentModel[194] = new ModelRendererTurbo(this, 185, 145, textureX, textureY); // Box 423
		attachmentModel[195] = new ModelRendererTurbo(this, 17, 113, textureX, textureY); // Box 424
		attachmentModel[196] = new ModelRendererTurbo(this, 201, 145, textureX, textureY); // Box 426
		attachmentModel[197] = new ModelRendererTurbo(this, 185, 209, textureX, textureY); // Box 426
		attachmentModel[198] = new ModelRendererTurbo(this, 185, 121, textureX, textureY); // Box 420
		attachmentModel[199] = new ModelRendererTurbo(this, 225, 121, textureX, textureY); // Box 424
		attachmentModel[200] = new ModelRendererTurbo(this, 73, 129, textureX, textureY); // Box 425
		attachmentModel[201] = new ModelRendererTurbo(this, 49, 129, textureX, textureY); // Box 426
		attachmentModel[202] = new ModelRendererTurbo(this, 249, 129, textureX, textureY); // Box 427
		attachmentModel[203] = new ModelRendererTurbo(this, 105, 137, textureX, textureY); // Box 428
		attachmentModel[204] = new ModelRendererTurbo(this, 81, 137, textureX, textureY); // Box 429
		attachmentModel[205] = new ModelRendererTurbo(this, 137, 137, textureX, textureY); // Box 430
		attachmentModel[206] = new ModelRendererTurbo(this, 241, 153, textureX, textureY); // Box 431
		attachmentModel[207] = new ModelRendererTurbo(this, 185, 137, textureX, textureY); // Box 432
		attachmentModel[208] = new ModelRendererTurbo(this, 1, 169, textureX, textureY); // Box 433
		attachmentModel[209] = new ModelRendererTurbo(this, 1, 177, textureX, textureY); // Box 434
		attachmentModel[210] = new ModelRendererTurbo(this, 89, 113, textureX, textureY); // Box 435
		attachmentModel[211] = new ModelRendererTurbo(this, 225, 137, textureX, textureY); // Box 436
		attachmentModel[212] = new ModelRendererTurbo(this, 249, 145, textureX, textureY); // Box 437
		attachmentModel[213] = new ModelRendererTurbo(this, 65, 185, textureX, textureY); // Box 438
		attachmentModel[214] = new ModelRendererTurbo(this, 57, 193, textureX, textureY); // Box 439
		attachmentModel[215] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 440
		attachmentModel[216] = new ModelRendererTurbo(this, 249, 161, textureX, textureY); // Box 441
		attachmentModel[217] = new ModelRendererTurbo(this, 249, 177, textureX, textureY); // Box 442
		attachmentModel[218] = new ModelRendererTurbo(this, 241, 193, textureX, textureY); // Box 443
		attachmentModel[219] = new ModelRendererTurbo(this, 201, 209, textureX, textureY); // Box 444
		attachmentModel[220] = new ModelRendererTurbo(this, 249, 185, textureX, textureY); // Box 445
		attachmentModel[221] = new ModelRendererTurbo(this, 249, 201, textureX, textureY); // Box 446
		attachmentModel[222] = new ModelRendererTurbo(this, 185, 209, textureX, textureY); // Box 447
		attachmentModel[223] = new ModelRendererTurbo(this, 225, 209, textureX, textureY); // Box 448
		attachmentModel[224] = new ModelRendererTurbo(this, 249, 209, textureX, textureY); // Box 449
		attachmentModel[225] = new ModelRendererTurbo(this, 217, 217, textureX, textureY); // Box 450
		attachmentModel[226] = new ModelRendererTurbo(this, 225, 217, textureX, textureY); // Box 451
		attachmentModel[227] = new ModelRendererTurbo(this, 241, 217, textureX, textureY); // Box 452
		attachmentModel[228] = new ModelRendererTurbo(this, 97, 225, textureX, textureY); // Box 453
		attachmentModel[229] = new ModelRendererTurbo(this, 129, 241, textureX, textureY); // Box 454
		attachmentModel[230] = new ModelRendererTurbo(this, 161, 241, textureX, textureY); // Box 455
		attachmentModel[231] = new ModelRendererTurbo(this, 193, 241, textureX, textureY); // Box 456

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 9, 15, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 220
		attachmentModel[0].setRotationPoint(6F, -6F, 6F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 58, 1, 3, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 459
		attachmentModel[1].setRotationPoint(0F, 10F, -5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, 0F, 0F, -1F); // Box 460
		attachmentModel[2].setRotationPoint(0F, 9F, 6F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[3].setRotationPoint(6F, -7F, 6F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 15, 1, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, -1F, 0F, 0F, 0F); // Box 462
		attachmentModel[4].setRotationPoint(15F, -6F, 6F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 42, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[5].setRotationPoint(16F, -7F, 5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 52, 2, 1, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 464
		attachmentModel[6].setRotationPoint(6F, -9F, 5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 42, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 353
		attachmentModel[7].setRotationPoint(16F, -3F, 5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 419
		attachmentModel[8].setRotationPoint(53F, 10F, -1.5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 420
		attachmentModel[9].setRotationPoint(56F, 10F, -1.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 421
		attachmentModel[10].setRotationPoint(56F, 10F, -0.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 422
		attachmentModel[11].setRotationPoint(56F, 10F, 0.5F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 423
		attachmentModel[12].setRotationPoint(53F, 10F, 0.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 58, 1, 3, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 429
		attachmentModel[13].setRotationPoint(0F, 10F, 2F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 450
		attachmentModel[14].setRotationPoint(47F, 10F, -1.5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 451
		attachmentModel[15].setRotationPoint(50F, 10F, -1.5F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 452
		attachmentModel[16].setRotationPoint(49.5F, 10F, -0.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 453
		attachmentModel[17].setRotationPoint(47F, 10F, 0.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 454
		attachmentModel[18].setRotationPoint(50F, 10F, 0.5F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 455
		attachmentModel[19].setRotationPoint(41F, 10F, -1.5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[20].setRotationPoint(44F, 10F, -1.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 457
		attachmentModel[21].setRotationPoint(43.5F, 10F, -0.5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 458
		attachmentModel[22].setRotationPoint(41F, 10F, 0.5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 459
		attachmentModel[23].setRotationPoint(44F, 10F, 0.5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 460
		attachmentModel[24].setRotationPoint(35F, 10F, -1.5F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[25].setRotationPoint(38F, 10F, -1.5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 462
		attachmentModel[26].setRotationPoint(37.5F, 10F, -0.5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 463
		attachmentModel[27].setRotationPoint(35F, 10F, 0.5F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 464
		attachmentModel[28].setRotationPoint(38F, 10F, 0.5F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 465
		attachmentModel[29].setRotationPoint(29F, 10F, -1.5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 466
		attachmentModel[30].setRotationPoint(32F, 10F, -1.5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 467
		attachmentModel[31].setRotationPoint(31.5F, 10F, -0.5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 468
		attachmentModel[32].setRotationPoint(29F, 10F, 0.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 469
		attachmentModel[33].setRotationPoint(32F, 10F, 0.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 470
		attachmentModel[34].setRotationPoint(23F, 10F, -1.5F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[35].setRotationPoint(26F, 10F, -1.5F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 472
		attachmentModel[36].setRotationPoint(25.5F, 10F, -0.5F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 473
		attachmentModel[37].setRotationPoint(23F, 10F, 0.5F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 474
		attachmentModel[38].setRotationPoint(26F, 10F, 0.5F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 475
		attachmentModel[39].setRotationPoint(17F, 10F, -1.5F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 476
		attachmentModel[40].setRotationPoint(20F, 10F, -1.5F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 477
		attachmentModel[41].setRotationPoint(19.5F, 10F, -0.5F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 478
		attachmentModel[42].setRotationPoint(17F, 10F, 0.5F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 479
		attachmentModel[43].setRotationPoint(20F, 10F, 0.5F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 480
		attachmentModel[44].setRotationPoint(11F, 10F, -1.5F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 481
		attachmentModel[45].setRotationPoint(14F, 10F, -1.5F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 482
		attachmentModel[46].setRotationPoint(13.5F, 10F, -0.5F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 483
		attachmentModel[47].setRotationPoint(11F, 10F, 0.5F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 484
		attachmentModel[48].setRotationPoint(14F, 10F, 0.5F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 490
		attachmentModel[49].setRotationPoint(0F, 10F, -1.5F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 492
		attachmentModel[50].setRotationPoint(0.5F, 10F, -0.5F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 5, 1, 7, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 495
		attachmentModel[51].setRotationPoint(0F, 11F, -3.5F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 42, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 498
		attachmentModel[52].setRotationPoint(16F, 1.5F, 5F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 524
		attachmentModel[53].setRotationPoint(47.5F, 1F, 5F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 525
		attachmentModel[54].setRotationPoint(50.5F, 0F, 5F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 526
		attachmentModel[55].setRotationPoint(50.5F, -0.5F, 5F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 527
		attachmentModel[56].setRotationPoint(50.5F, -1F, 5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 528
		attachmentModel[57].setRotationPoint(47.5F, -2F, 5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 529
		attachmentModel[58].setRotationPoint(41.5F, 1F, 5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 530
		attachmentModel[59].setRotationPoint(44.5F, 0F, 5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 531
		attachmentModel[60].setRotationPoint(44.5F, -0.5F, 5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 532
		attachmentModel[61].setRotationPoint(44.5F, -1F, 5F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 533
		attachmentModel[62].setRotationPoint(41.5F, -2F, 5F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 534
		attachmentModel[63].setRotationPoint(35.5F, 1F, 5F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 535
		attachmentModel[64].setRotationPoint(38.5F, 0F, 5F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 536
		attachmentModel[65].setRotationPoint(38.5F, -0.5F, 5F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 537
		attachmentModel[66].setRotationPoint(38.5F, -1F, 5F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 538
		attachmentModel[67].setRotationPoint(35.5F, -2F, 5F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 539
		attachmentModel[68].setRotationPoint(29.5F, 1F, 5F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 540
		attachmentModel[69].setRotationPoint(32.5F, 0F, 5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 541
		attachmentModel[70].setRotationPoint(32.5F, -0.5F, 5F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 542
		attachmentModel[71].setRotationPoint(32.5F, -1F, 5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 543
		attachmentModel[72].setRotationPoint(29.5F, -2F, 5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 544
		attachmentModel[73].setRotationPoint(23.5F, 1F, 5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 545
		attachmentModel[74].setRotationPoint(26.5F, 0F, 5F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 546
		attachmentModel[75].setRotationPoint(26.5F, -0.5F, 5F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 547
		attachmentModel[76].setRotationPoint(26.5F, -1F, 5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 548
		attachmentModel[77].setRotationPoint(23.5F, -2F, 5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 555
		attachmentModel[78].setRotationPoint(56.5F, -0.5F, 5F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 556
		attachmentModel[79].setRotationPoint(56F, -1F, 5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 557
		attachmentModel[80].setRotationPoint(53.5F, -2F, 5F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 559
		attachmentModel[81].setRotationPoint(53.5F, 1F, 5F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 560
		attachmentModel[82].setRotationPoint(56F, 0F, 5F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 561
		attachmentModel[83].setRotationPoint(16F, -1.5F, 5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 562
		attachmentModel[84].setRotationPoint(16F, -0.5F, 5F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 563
		attachmentModel[85].setRotationPoint(16F, 0.5F, 5F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 564
		attachmentModel[86].setRotationPoint(53.5F, 8F, 5F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 565
		attachmentModel[87].setRotationPoint(56F, 7F, 5F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 566
		attachmentModel[88].setRotationPoint(56.5F, 6.5F, 5F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 567
		attachmentModel[89].setRotationPoint(56F, 6F, 5F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 568
		attachmentModel[90].setRotationPoint(53.5F, 5F, 5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 569
		attachmentModel[91].setRotationPoint(47.5F, 5F, 5F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 570
		attachmentModel[92].setRotationPoint(50.5F, 6F, 5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 571
		attachmentModel[93].setRotationPoint(50.5F, 6.5F, 5F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 572
		attachmentModel[94].setRotationPoint(50.5F, 7F, 5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 573
		attachmentModel[95].setRotationPoint(47.5F, 8F, 5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 574
		attachmentModel[96].setRotationPoint(41.5F, 5F, 5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 575
		attachmentModel[97].setRotationPoint(44.5F, 6F, 5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 576
		attachmentModel[98].setRotationPoint(44.5F, 6.5F, 5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 577
		attachmentModel[99].setRotationPoint(44.5F, 7F, 5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 578
		attachmentModel[100].setRotationPoint(41.5F, 8F, 5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 579
		attachmentModel[101].setRotationPoint(35.5F, 5F, 5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 580
		attachmentModel[102].setRotationPoint(38.5F, 6F, 5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 581
		attachmentModel[103].setRotationPoint(38.5F, 6.5F, 5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 582
		attachmentModel[104].setRotationPoint(38.5F, 7F, 5F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 583
		attachmentModel[105].setRotationPoint(35.5F, 8F, 5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 584
		attachmentModel[106].setRotationPoint(29.5F, 5F, 5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 585
		attachmentModel[107].setRotationPoint(32.5F, 6F, 5F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 586
		attachmentModel[108].setRotationPoint(32.5F, 6.5F, 5F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 587
		attachmentModel[109].setRotationPoint(32.5F, 7F, 5F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 588
		attachmentModel[110].setRotationPoint(29.5F, 8F, 5F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 589
		attachmentModel[111].setRotationPoint(16F, 5.5F, 5F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 590
		attachmentModel[112].setRotationPoint(16F, 6.5F, 5F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 591
		attachmentModel[113].setRotationPoint(16F, 7.5F, 5F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 42, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 592
		attachmentModel[114].setRotationPoint(16F, 9F, 5F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 8, 1, 6, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 593
		attachmentModel[115].setRotationPoint(0F, 6F, -3F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -3F, 0F, 0F, -3F, 0F); // Box 594
		attachmentModel[116].setRotationPoint(0F, 6F, 2F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 595
		attachmentModel[117].setRotationPoint(0F, 6F, -6F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 596
		attachmentModel[118].setRotationPoint(10F, 6F, -6F);

		attachmentModel[119].addShapeBox(0F, 0F, 0F, 5, 1, 6, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 597
		attachmentModel[119].setRotationPoint(10F, 6F, -3F);

		attachmentModel[120].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -3F, 0F, 0F, -3F, 0F); // Box 598
		attachmentModel[120].setRotationPoint(10F, 6F, 2F);

		attachmentModel[121].addShapeBox(0F, 0F, 0F, 2, 2, 14, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 599
		attachmentModel[121].setRotationPoint(8F, 5F, -7F);

		attachmentModel[122].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 600
		attachmentModel[122].setRotationPoint(9F, 6F, -8F);
		attachmentModel[122].rotateAngleZ = -0.78539816F;

		attachmentModel[123].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 601
		attachmentModel[123].setRotationPoint(9F, 6F, 7F);
		attachmentModel[123].rotateAngleZ = -0.78539816F;

		attachmentModel[124].addShapeBox(0F, 0F, 0F, 9, 15, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 602
		attachmentModel[124].setRotationPoint(6F, -6F, -7F);

		attachmentModel[125].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F); // Box 603
		attachmentModel[125].setRotationPoint(0F, 9F, -7F);

		attachmentModel[126].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 604
		attachmentModel[126].setRotationPoint(6F, -7F, -7F);

		attachmentModel[127].addShapeBox(0F, 0F, 0F, 52, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 605
		attachmentModel[127].setRotationPoint(6F, -9F, -6F);

		attachmentModel[128].addShapeBox(0F, 0F, 0F, 42, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 606
		attachmentModel[128].setRotationPoint(16F, -7F, -6F);

		attachmentModel[129].addShapeBox(0F, 0F, 0F, 1, 15, 1, 0F, 0F, 0F, 0F, 0F, 1F, -1F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -1F, 0F, 1F, 1F, 0F, 0F, 0F); // Box 607
		attachmentModel[129].setRotationPoint(15F, -6F, -7F);

		attachmentModel[130].addShapeBox(0F, 0F, 0F, 42, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 608
		attachmentModel[130].setRotationPoint(16F, 9F, -6F);

		attachmentModel[131].addShapeBox(0F, 0F, 0F, 42, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 612
		attachmentModel[131].setRotationPoint(16F, 1.5F, -6F);

		attachmentModel[132].addShapeBox(0F, 0F, 0F, 42, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 648
		attachmentModel[132].setRotationPoint(16F, -3F, -6F);

		attachmentModel[133].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 649
		attachmentModel[133].setRotationPoint(16F, -1.5F, -6F);

		attachmentModel[134].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 650
		attachmentModel[134].setRotationPoint(16F, -0.5F, -6F);

		attachmentModel[135].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 651
		attachmentModel[135].setRotationPoint(16F, 0.5F, -6F);

		attachmentModel[136].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 672
		attachmentModel[136].setRotationPoint(47.5F, 1F, -6F);

		attachmentModel[137].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 673
		attachmentModel[137].setRotationPoint(50.5F, 0F, -6F);

		attachmentModel[138].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 674
		attachmentModel[138].setRotationPoint(50.5F, -0.5F, -6F);

		attachmentModel[139].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 675
		attachmentModel[139].setRotationPoint(50.5F, -1F, -6F);

		attachmentModel[140].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 676
		attachmentModel[140].setRotationPoint(47.5F, -2F, -6F);

		attachmentModel[141].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 677
		attachmentModel[141].setRotationPoint(41.5F, 1F, -6F);

		attachmentModel[142].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 678
		attachmentModel[142].setRotationPoint(44.5F, 0F, -6F);

		attachmentModel[143].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 679
		attachmentModel[143].setRotationPoint(44.5F, -0.5F, -6F);

		attachmentModel[144].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 680
		attachmentModel[144].setRotationPoint(44.5F, -1F, -6F);

		attachmentModel[145].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 681
		attachmentModel[145].setRotationPoint(41.5F, -2F, -6F);

		attachmentModel[146].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 682
		attachmentModel[146].setRotationPoint(35.5F, 1F, -6F);

		attachmentModel[147].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 683
		attachmentModel[147].setRotationPoint(38.5F, 0F, -6F);

		attachmentModel[148].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 684
		attachmentModel[148].setRotationPoint(38.5F, -0.5F, -6F);

		attachmentModel[149].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 685
		attachmentModel[149].setRotationPoint(38.5F, -1F, -6F);

		attachmentModel[150].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 686
		attachmentModel[150].setRotationPoint(35.5F, -2F, -6F);

		attachmentModel[151].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 687
		attachmentModel[151].setRotationPoint(29.5F, 1F, -6F);

		attachmentModel[152].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 688
		attachmentModel[152].setRotationPoint(32.5F, 0F, -6F);

		attachmentModel[153].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 689
		attachmentModel[153].setRotationPoint(32.5F, -0.5F, -6F);

		attachmentModel[154].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 690
		attachmentModel[154].setRotationPoint(32.5F, -1F, -6F);

		attachmentModel[155].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 691
		attachmentModel[155].setRotationPoint(29.5F, -2F, -6F);

		attachmentModel[156].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 692
		attachmentModel[156].setRotationPoint(23.5F, 1F, -6F);

		attachmentModel[157].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 693
		attachmentModel[157].setRotationPoint(26.5F, 0F, -6F);

		attachmentModel[158].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 694
		attachmentModel[158].setRotationPoint(26.5F, -0.5F, -6F);

		attachmentModel[159].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 695
		attachmentModel[159].setRotationPoint(26.5F, -1F, -6F);

		attachmentModel[160].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 696
		attachmentModel[160].setRotationPoint(23.5F, -2F, -6F);

		attachmentModel[161].addShapeBox(0F, 0F, 0F, 15, 1, 3, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F); // Box 702
		attachmentModel[161].setRotationPoint(0F, -6F, 3F);

		attachmentModel[162].addShapeBox(0F, 0F, 0F, 6, 11, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 391
		attachmentModel[162].setRotationPoint(0F, -2F, 6F);

		attachmentModel[163].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 392
		attachmentModel[163].setRotationPoint(0F, -8F, 4F);

		attachmentModel[164].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 2F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, -2F, 0F, -2F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -2F); // Box 393
		attachmentModel[164].setRotationPoint(3F, -6F, 6F);

		attachmentModel[165].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, -1.5F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, -2F, 1F); // Box 394
		attachmentModel[165].setRotationPoint(3F, -9F, 3F);

		attachmentModel[166].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 2F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 2F, 0F, -2F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 2F); // Box 395
		attachmentModel[166].setRotationPoint(3F, -6F, -7F);

		attachmentModel[167].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 396
		attachmentModel[167].setRotationPoint(0F, -8F, -5F);

		attachmentModel[168].addShapeBox(0F, 0F, 0F, 15, 1, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 397
		attachmentModel[168].setRotationPoint(0F, -6F, -6F);

		attachmentModel[169].addShapeBox(0F, 0F, 0F, 6, 11, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 398
		attachmentModel[169].setRotationPoint(0F, -2F, -7F);

		attachmentModel[170].addShapeBox(0F, 0F, 0F, 4, 1, 7, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 399
		attachmentModel[170].setRotationPoint(0F, 12F, -3.5F);

		attachmentModel[171].addShapeBox(0F, 0F, 0F, 9, 1, 7, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.625F, 0F, 0F, 0.625F, 0F, 0F, 0.5F, 0F, 0F); // Box 400
		attachmentModel[171].setRotationPoint(-6F, 13F, -3.5F);

		attachmentModel[172].addShapeBox(0F, 0F, 0F, 6, 7, 7, 0F, 2.5F, 0F, 0F, 1.625F, 0F, 0F, 1.625F, 0F, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F); // Box 401
		attachmentModel[172].setRotationPoint(-4F, 14F, -3.5F);

		attachmentModel[173].addShapeBox(0F, 0F, 0F, 1, 28, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, 0F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, 0F, 0F); // Box 402
		attachmentModel[173].setRotationPoint(-2F, 21F, -3.5F);

		attachmentModel[174].addShapeBox(0F, 0F, 0F, 1, 28, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, 0F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, 0F, 0F); // Box 403
		attachmentModel[174].setRotationPoint(0F, 21F, -3.5F);

		attachmentModel[175].addShapeBox(0F, 0F, 0F, 1, 3, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, -1.75F, 0.5F, 0F, -1.75F, 0.5F, 0F, 1.5F, 0F, 0F); // Box 404
		attachmentModel[175].setRotationPoint(-1F, 21F, -3.5F);

		attachmentModel[176].addShapeBox(0F, 0F, 0F, 1, 28, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, -0.5F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, -0.5F, 0F); // Box 405
		attachmentModel[176].setRotationPoint(-1F, 21F, -0.5F);

		attachmentModel[177].addShapeBox(0F, 0F, 0F, 1, 4, 7, 0F, -2.25F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2.25F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 406
		attachmentModel[177].setRotationPoint(-15F, 45F, -3.5F);

		attachmentModel[178].addShapeBox(0F, 0F, 0F, 21, 2, 7, 0F, -7.5F, 0F, 0F, 1F, -3F, 0F, 1F, -3F, 0F, -7.5F, 0F, 0F, 3F, -2F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 3F, -2F, 0F); // Box 407
		attachmentModel[178].setRotationPoint(-37F, 44F, -3.5F);

		attachmentModel[179].addShapeBox(0F, 0F, 0F, 3, 3, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 2F); // Box 408
		attachmentModel[179].setRotationPoint(-29.5F, 41F, -1.5F);

		attachmentModel[180].addShapeBox(0F, 0F, 0F, 10, 3, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 409
		attachmentModel[180].setRotationPoint(-39.5F, 41F, -3.5F);

		attachmentModel[181].addShapeBox(0F, 0F, 0F, 9, 9, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 3F, 0F, 0F); // Box 410
		attachmentModel[181].setRotationPoint(-36.5F, 32F, -3.5F);

		attachmentModel[182].addShapeBox(0F, 0F, 0F, 8, 9, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 4F, 0F, 0F); // Box 411
		attachmentModel[182].setRotationPoint(-32.5F, 23F, -3.5F);

		attachmentModel[183].addShapeBox(0F, 0F, 0F, 7, 6, 7, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 412
		attachmentModel[183].setRotationPoint(-31.5F, 17F, -3.5F);

		attachmentModel[184].addShapeBox(0F, 0F, 0F, 4, 5, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 413
		attachmentModel[184].setRotationPoint(-24.5F, 17F, -3.5F);

		attachmentModel[185].addShapeBox(0F, 0F, 0F, 8, 4, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 414
		attachmentModel[185].setRotationPoint(-29.5F, 13F, -3.5F);

		attachmentModel[186].addShapeBox(0F, 0F, 0F, 4, 1, 5, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 415
		attachmentModel[186].setRotationPoint(-24.5F, 22F, -2.5F);

		attachmentModel[187].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F); // Box 416
		attachmentModel[187].setRotationPoint(-24.5F, 22F, -1.5F);

		attachmentModel[188].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 417
		attachmentModel[188].setRotationPoint(-23.5F, 23F, -1.5F);

		attachmentModel[189].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 2F, 0F, 2F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 2F); // Box 418
		attachmentModel[189].setRotationPoint(-27.5F, 32F, -1.5F);

		attachmentModel[190].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 3F, 0F, 2F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 2F); // Box 419
		attachmentModel[190].setRotationPoint(-24.5F, 23F, -1.5F);

		attachmentModel[191].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 4F, -2F, 0F, -4F, 0F, 2F, -4F, 0F, 2F, 4F, -2F, 0F); // Box 420
		attachmentModel[191].setRotationPoint(-35.5F, 23F, -1.5F);

		attachmentModel[192].addShapeBox(0F, 0F, 0F, 2, 6, 3, 0F, 0F, 0.5F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0.5F, 0F, 4F, 0F, 0F, -3F, 0F, 2F, -3F, 0F, 2F, 4F, 0F, 0F); // Box 421
		attachmentModel[192].setRotationPoint(-31.5F, 17F, -1.5F);

		attachmentModel[193].addShapeBox(0F, 0F, 0F, 2, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F); // Box 422
		attachmentModel[193].setRotationPoint(-31.5F, 13F, -1.5F);

		attachmentModel[194].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 2F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 2F, 0F, 4F, 0F, 0F, -3F, 0F, 2F, -3F, 0F, 2F, 4F, 0F, 0F); // Box 423
		attachmentModel[194].setRotationPoint(-39.5F, 32F, -1.5F);

		attachmentModel[195].addShapeBox(0F, 0F, 0F, 4, 2, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1F, 2F, -0.5F, 1F, 2F, 0F, 0F, 0F); // Box 424
		attachmentModel[195].setRotationPoint(-43.5F, 41F, -1.5F);

		attachmentModel[196].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[196].setRotationPoint(0F, 10F, 0.5F);

		attachmentModel[197].addShapeBox(0F, 0F, 0F, 1, 3, 7, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[197].setRotationPoint(-21.5F, 14F, -3.5F);

		attachmentModel[198].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, -2F, 1F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, -1.5F, -1.5F); // Box 420
		attachmentModel[198].setRotationPoint(3F, -9F, -4F);

		attachmentModel[199].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 424
		attachmentModel[199].setRotationPoint(56F, -1F, -6F);

		attachmentModel[200].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 425
		attachmentModel[200].setRotationPoint(53.5F, -2F, -6F);

		attachmentModel[201].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[201].setRotationPoint(56.5F, -0.5F, -6F);

		attachmentModel[202].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 427
		attachmentModel[202].setRotationPoint(56F, 0F, -6F);

		attachmentModel[203].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 428
		attachmentModel[203].setRotationPoint(53.5F, 1F, -6F);

		attachmentModel[204].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 429
		attachmentModel[204].setRotationPoint(56.5F, 6.5F, -6F);

		attachmentModel[205].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 430
		attachmentModel[205].setRotationPoint(56F, 6F, -6F);

		attachmentModel[206].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 431
		attachmentModel[206].setRotationPoint(53.5F, 5F, -6F);

		attachmentModel[207].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 432
		attachmentModel[207].setRotationPoint(56F, 7F, -6F);

		attachmentModel[208].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 433
		attachmentModel[208].setRotationPoint(53.5F, 8F, -6F);

		attachmentModel[209].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 434
		attachmentModel[209].setRotationPoint(47.5F, 5F, -6F);

		attachmentModel[210].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 435
		attachmentModel[210].setRotationPoint(50.5F, 6F, -6F);

		attachmentModel[211].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 436
		attachmentModel[211].setRotationPoint(50.5F, 6.5F, -6F);

		attachmentModel[212].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 437
		attachmentModel[212].setRotationPoint(50.5F, 7F, -6F);

		attachmentModel[213].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 438
		attachmentModel[213].setRotationPoint(47.5F, 8F, -6F);

		attachmentModel[214].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 439
		attachmentModel[214].setRotationPoint(41.5F, 5F, -6F);

		attachmentModel[215].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 440
		attachmentModel[215].setRotationPoint(44.5F, 6F, -6F);

		attachmentModel[216].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 441
		attachmentModel[216].setRotationPoint(44.5F, 6.5F, -6F);

		attachmentModel[217].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 442
		attachmentModel[217].setRotationPoint(44.5F, 7F, -6F);

		attachmentModel[218].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 443
		attachmentModel[218].setRotationPoint(41.5F, 8F, -6F);

		attachmentModel[219].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 444
		attachmentModel[219].setRotationPoint(35.5F, 5F, -6F);

		attachmentModel[220].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 445
		attachmentModel[220].setRotationPoint(38.5F, 6F, -6F);

		attachmentModel[221].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 446
		attachmentModel[221].setRotationPoint(38.5F, 6.5F, -6F);

		attachmentModel[222].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 447
		attachmentModel[222].setRotationPoint(38.5F, 7F, -6F);

		attachmentModel[223].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 448
		attachmentModel[223].setRotationPoint(35.5F, 8F, -6F);

		attachmentModel[224].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 449
		attachmentModel[224].setRotationPoint(32.5F, 6F, -6F);

		attachmentModel[225].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 450
		attachmentModel[225].setRotationPoint(32.5F, 6.5F, -6F);

		attachmentModel[226].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 451
		attachmentModel[226].setRotationPoint(32.5F, 7F, -6F);

		attachmentModel[227].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 452
		attachmentModel[227].setRotationPoint(29.5F, 8F, -6F);

		attachmentModel[228].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 453
		attachmentModel[228].setRotationPoint(29.5F, 5F, -6F);

		attachmentModel[229].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 454
		attachmentModel[229].setRotationPoint(16F, 5.5F, -6F);

		attachmentModel[230].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 455
		attachmentModel[230].setRotationPoint(16F, 6.5F, -6F);

		attachmentModel[231].addShapeBox(0F, 0F, 0F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[231].setRotationPoint(16F, 7.5F, -6F);

		flipAll();
	}
}