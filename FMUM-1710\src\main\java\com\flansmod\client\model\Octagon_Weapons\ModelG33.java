//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: G33 Enabled
// Model Creator: 
// Created on: 27.10.2019 - 13:16:05
// Last changed on: 27.10.2019 - 13:16:05

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAtSight;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelG33 extends ModelAtSight //Same as Filename
{
	int textureX = 128;
	int textureY = 128;

	public ModelG33() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[89];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 46
		attachmentModel[1] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 47
		attachmentModel[2] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 48
		attachmentModel[3] = new ModelRendererTurbo(this, 60, 1, textureX, textureY); // Box 49
		attachmentModel[4] = new ModelRendererTurbo(this, 71, 1, textureX, textureY); // Box 50
		attachmentModel[5] = new ModelRendererTurbo(this, 81, 1, textureX, textureY); // Box 51
		attachmentModel[6] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 52
		attachmentModel[7] = new ModelRendererTurbo(this, 97, 1, textureX, textureY); // Box 53
		attachmentModel[8] = new ModelRendererTurbo(this, 105, 1, textureX, textureY); // Box 54
		attachmentModel[9] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 55
		attachmentModel[10] = new ModelRendererTurbo(this, 121, 1, textureX, textureY); // Box 56
		attachmentModel[11] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 57
		attachmentModel[12] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 58
		attachmentModel[13] = new ModelRendererTurbo(this, 25, 9, textureX, textureY); // Box 59
		attachmentModel[14] = new ModelRendererTurbo(this, 41, 9, textureX, textureY); // Box 60
		attachmentModel[15] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 61
		attachmentModel[16] = new ModelRendererTurbo(this, 65, 9, textureX, textureY); // Box 62
		attachmentModel[17] = new ModelRendererTurbo(this, 81, 9, textureX, textureY); // Box 63
		attachmentModel[18] = new ModelRendererTurbo(this, 97, 9, textureX, textureY); // Box 64
		attachmentModel[19] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 65
		attachmentModel[20] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 66
		attachmentModel[21] = new ModelRendererTurbo(this, 17, 17, textureX, textureY); // Box 67
		attachmentModel[22] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 68
		attachmentModel[23] = new ModelRendererTurbo(this, 41, 17, textureX, textureY); // Box 69
		attachmentModel[24] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 70
		attachmentModel[25] = new ModelRendererTurbo(this, 70, 16, textureX, textureY); // Box 71
		attachmentModel[26] = new ModelRendererTurbo(this, 88, 16, textureX, textureY); // Box 72
		attachmentModel[27] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 73
		attachmentModel[28] = new ModelRendererTurbo(this, 113, 17, textureX, textureY); // Box 74
		attachmentModel[29] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 75
		attachmentModel[30] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 76
		attachmentModel[31] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 77
		attachmentModel[32] = new ModelRendererTurbo(this, 81, 25, textureX, textureY); // Box 78
		attachmentModel[33] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 79
		attachmentModel[34] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 80
		attachmentModel[35] = new ModelRendererTurbo(this, 65, 33, textureX, textureY); // Box 81
		attachmentModel[36] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 83
		attachmentModel[37] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 85
		attachmentModel[38] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 86
		attachmentModel[39] = new ModelRendererTurbo(this, 81, 41, textureX, textureY); // Box 87
		attachmentModel[40] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 88
		attachmentModel[41] = new ModelRendererTurbo(this, 49, 49, textureX, textureY); // Box 89
		attachmentModel[42] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 93
		attachmentModel[43] = new ModelRendererTurbo(this, 79, 17, textureX, textureY); // Box 94
		attachmentModel[44] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 95
		attachmentModel[45] = new ModelRendererTurbo(this, 121, 25, textureX, textureY); // Box 96
		attachmentModel[46] = new ModelRendererTurbo(this, 97, 49, textureX, textureY); // Box 97
		attachmentModel[47] = new ModelRendererTurbo(this, 105, 49, textureX, textureY); // Box 98
		attachmentModel[48] = new ModelRendererTurbo(this, 121, 49, textureX, textureY); // Box 99
		attachmentModel[49] = new ModelRendererTurbo(this, 49, 57, textureX, textureY); // Box 100
		attachmentModel[50] = new ModelRendererTurbo(this, 81, 57, textureX, textureY); // Box 101
		attachmentModel[51] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 102
		attachmentModel[52] = new ModelRendererTurbo(this, 33, 65, textureX, textureY); // Box 103
		attachmentModel[53] = new ModelRendererTurbo(this, 73, 65, textureX, textureY); // Box 106
		attachmentModel[54] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 107
		attachmentModel[55] = new ModelRendererTurbo(this, 113, 57, textureX, textureY); // Box 109
		attachmentModel[56] = new ModelRendererTurbo(this, 120, 57, textureX, textureY); // Box 110
		attachmentModel[57] = new ModelRendererTurbo(this, 113, 65, textureX, textureY); // Box 111
		attachmentModel[58] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 113
		attachmentModel[59] = new ModelRendererTurbo(this, 41, 73, textureX, textureY); // Box 114
		attachmentModel[60] = new ModelRendererTurbo(this, 49, 73, textureX, textureY); // Box 115
		attachmentModel[61] = new ModelRendererTurbo(this, 65, 73, textureX, textureY); // Box 116
		attachmentModel[62] = new ModelRendererTurbo(this, 97, 73, textureX, textureY); // Box 117
		attachmentModel[63] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 118
		attachmentModel[64] = new ModelRendererTurbo(this, 33, 81, textureX, textureY); // Box 119
		attachmentModel[65] = new ModelRendererTurbo(this, 41, 81, textureX, textureY); // Box 120
		attachmentModel[66] = new ModelRendererTurbo(this, 49, 81, textureX, textureY); // Box 121
		attachmentModel[67] = new ModelRendererTurbo(this, 73, 57, textureX, textureY); // Box 122
		attachmentModel[68] = new ModelRendererTurbo(this, 105, 57, textureX, textureY); // Box 123
		attachmentModel[69] = new ModelRendererTurbo(this, 57, 81, textureX, textureY); // Box 124
		attachmentModel[70] = new ModelRendererTurbo(this, 65, 81, textureX, textureY); // Box 125
		attachmentModel[71] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 126
		attachmentModel[72] = new ModelRendererTurbo(this, 81, 81, textureX, textureY); // Box 127
		attachmentModel[73] = new ModelRendererTurbo(this, 89, 81, textureX, textureY); // Box 128
		attachmentModel[74] = new ModelRendererTurbo(this, 97, 81, textureX, textureY); // Box 129
		attachmentModel[75] = new ModelRendererTurbo(this, 105, 81, textureX, textureY); // Box 130
		attachmentModel[76] = new ModelRendererTurbo(this, 113, 81, textureX, textureY); // Box 131
		attachmentModel[77] = new ModelRendererTurbo(this, 121, 81, textureX, textureY); // Box 132
		attachmentModel[78] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 161
		attachmentModel[79] = new ModelRendererTurbo(this, 33, 88, textureX, textureY); // Box 162
		attachmentModel[80] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 163
		attachmentModel[81] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 92
		attachmentModel[82] = new ModelRendererTurbo(this, 49, 97, textureX, textureY); // Box 93
		attachmentModel[83] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 94
		attachmentModel[84] = new ModelRendererTurbo(this, 49, 105, textureX, textureY); // Box 95
		attachmentModel[85] = new ModelRendererTurbo(this, 89, 105, textureX, textureY); // Box 96
		attachmentModel[86] = new ModelRendererTurbo(this, 113, 89, textureX, textureY); // Box 97
		attachmentModel[87] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 98
		attachmentModel[88] = new ModelRendererTurbo(this, 25, 113, textureX, textureY); // Box 99

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 9, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 46
		attachmentModel[0].setRotationPoint(-3.5F, -1F, -3F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 47
		attachmentModel[1].setRotationPoint(-3.5F, -1F, 3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 48
		attachmentModel[2].setRotationPoint(2.5F, -1F, 3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0.5F); // Box 49
		attachmentModel[3].setRotationPoint(2.5F, -1F, 4F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F); // Box 50
		attachmentModel[4].setRotationPoint(2.5F, 1F, 4F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 51
		attachmentModel[5].setRotationPoint(2.5F, -0.5F, 5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[6].setRotationPoint(1.5F, -1F, 5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 53
		attachmentModel[7].setRotationPoint(1.5F, 1F, 5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 54
		attachmentModel[8].setRotationPoint(0.5F, 0F, 5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 55
		attachmentModel[9].setRotationPoint(0.5F, -1F, 5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 56
		attachmentModel[10].setRotationPoint(0.5F, 1F, 5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 6, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 57
		attachmentModel[11].setRotationPoint(-3.5F, -1F, 4F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 58
		attachmentModel[12].setRotationPoint(0F, -1F, 3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 59
		attachmentModel[13].setRotationPoint(-6.5F, -1F, 4F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 60
		attachmentModel[14].setRotationPoint(-7.5F, -1F, 5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 61
		attachmentModel[15].setRotationPoint(-3.5F, -2F, -4F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 62
		attachmentModel[16].setRotationPoint(2.5F, -2F, -4F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 63
		attachmentModel[17].setRotationPoint(-3.5F, -3F, -4F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 64
		attachmentModel[18].setRotationPoint(-3.5F, -1F, -4F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 65
		attachmentModel[19].setRotationPoint(2.5F, -1F, -4F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 66
		attachmentModel[20].setRotationPoint(2.5F, -3F, -4F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 67
		attachmentModel[21].setRotationPoint(-0.5F, -1F, -4F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F); // Box 68
		attachmentModel[22].setRotationPoint(0F, 0F, -4.5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 69
		attachmentModel[23].setRotationPoint(-3.5F, -4F, -5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 70
		attachmentModel[24].setRotationPoint(2.5F, -4F, -5F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 71
		attachmentModel[25].setRotationPoint(2.5F, -5F, -4F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 72
		attachmentModel[26].setRotationPoint(-3.5F, -5F, -4F);

		attachmentModel[27].addShapeBox(0F, -0.5F, -1.5F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 73
		attachmentModel[27].setRotationPoint(-0.5F, -3.5F, -3.5F);

		attachmentModel[28].addShapeBox(0F, -1.5F, -0.5F, 3, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 74
		attachmentModel[28].setRotationPoint(-0.5F, -3.5F, -3.5F);

		attachmentModel[29].addShapeBox(0F, 0.5F, -0.5F, 3, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 75
		attachmentModel[29].setRotationPoint(-0.5F, -3.5F, -3.5F);

		attachmentModel[30].addShapeBox(0F, -4F, 1.5F, 11, 5, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 76
		attachmentModel[30].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[31].addShapeBox(0F, -4F, 4.5F, 11, 5, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 77
		attachmentModel[31].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[32].addShapeBox(0F, -4F, 2F, 11, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 78
		attachmentModel[32].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[33].addShapeBox(0F, -5F, 6F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 0.5F, 0F, -1.5F, 0.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F); // Box 79
		attachmentModel[33].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[34].addShapeBox(0F, -8F, 6F, 11, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 80
		attachmentModel[34].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[35].addShapeBox(0F, -8F, 0F, 11, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 81
		attachmentModel[35].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[36].addShapeBox(0F, -10F, 0F, 11, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 83
		attachmentModel[36].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[37].addShapeBox(0F, -10F, 2F, 11, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 85
		attachmentModel[37].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[38].addShapeBox(0F, -11F, 2F, 19, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 86
		attachmentModel[38].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[39].addShapeBox(0F, -3F, 2F, 19, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 87
		attachmentModel[39].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[40].addShapeBox(0F, -8F, -1F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 88
		attachmentModel[40].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[41].addShapeBox(0F, -8F, 7F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 89
		attachmentModel[41].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[42].addShapeBox(0F, -5F, -1F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F); // Box 93
		attachmentModel[42].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[43].addShapeBox(0F, -12F, 1F, 1, 1, 5, 0F, 0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0.5F, 0F, -1F); // Box 94
		attachmentModel[43].setRotationPoint(-1.5F, -3.5F, -3.5F);

		attachmentModel[44].addShapeBox(0F, -12F, 1F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 95
		attachmentModel[44].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[45].addShapeBox(0F, -12F, 5F, 1, 1, 1, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 96
		attachmentModel[45].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[46].addShapeBox(0F, -13.5F, 2.5F, 1, 3, 2, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 97
		attachmentModel[46].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[47].addShapeBox(0F, -13.5F, 1.5F, 2, 3, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 98
		attachmentModel[47].setRotationPoint(-5F, -3.5F, -3.5F);

		attachmentModel[48].addShapeBox(0F, -13.5F, 2.5F, 1, 3, 2, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 99
		attachmentModel[48].setRotationPoint(-6F, -3.5F, -3.5F);

		attachmentModel[49].addShapeBox(0F, -12F, 5F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -1F); // Box 100
		attachmentModel[49].setRotationPoint(-17F, -3.5F, -3.5F);

		attachmentModel[50].addShapeBox(0F, -12F, 1F, 11, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 101
		attachmentModel[50].setRotationPoint(-17F, -3.5F, -3.5F);

		attachmentModel[51].addShapeBox(0F, -12F, 2F, 11, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F); // Box 102
		attachmentModel[51].setRotationPoint(-17F, -3.5F, -3.5F);

		attachmentModel[52].addShapeBox(0F, -12F, 8F, 15, 4, 1, 0F, 0.5F, -1F, 3F, 1F, -1F, 3F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F); // Box 103
		attachmentModel[52].setRotationPoint(-16.5F, -3.5F, -3.5F);

		attachmentModel[53].addShapeBox(0F, -9F, 8F, 15, 5, 1, 0F, 0.5F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 106
		attachmentModel[53].setRotationPoint(-16.5F, -3.5F, -3.5F);

		attachmentModel[54].addShapeBox(0F, -2F, 1F, 16, 1, 5, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0.5F, 0F, 0F); // Box 107
		attachmentModel[54].setRotationPoint(-16.5F, -3.5F, -3.5F);

		attachmentModel[55].addShapeBox(0F, -3F, 7F, 2, 1, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 0.5F, -0.5F, -1.5F, 0.5F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 109
		attachmentModel[55].setRotationPoint(-7.5F, -3.5F, -3.5F);

		attachmentModel[56].addShapeBox(0F, -3F, 5F, 2, 1, 1, 0F, 0F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -1.5F, 0.5F, 0F, -1.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 110
		attachmentModel[56].setRotationPoint(-7.5F, -3.5F, -3.5F);

		attachmentModel[57].addShapeBox(0F, -3.5F, 7F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.7071F, -0.2929F, 0F, -0.7071F, -0.2929F, 0F, -0.5858F, 1.4142F, 0F, -0.5858F, 1.4142F, 0F, 0.1213F, -1.7071F, 0F, 0.1213F, -1.7071F); // Box 111
		attachmentModel[57].setRotationPoint(-5.5F, -3.5F, -3.5F);

		attachmentModel[58].addShapeBox(0F, -3F, 7F, 1, 1, 1, 0F, -0.5F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -1F, -1.5F, 0.5F, 0F, -1.75F, 0.25F, 0F, -0.75F, -0.25F, -1F, -0.5F, -0.5F); // Box 113
		attachmentModel[58].setRotationPoint(-4F, -3.5F, -3.5F);

		attachmentModel[59].addShapeBox(0F, -3F, 5F, 1, 1, 1, 0F, -1F, -0.5F, -0.5F, 0F, -1F, 0F, 0F, -2F, 0F, -1F, -1.5F, 0.5F, -0.5F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 1F, 0F, -0.5F, 1F, 0F); // Box 114
		attachmentModel[59].setRotationPoint(-4F, -3.5F, -3.5F);

		attachmentModel[60].addShapeBox(0F, -5F, 7F, 4, 2, 2, 0F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F, 0.5F, 0F, -1F, 0F, 0F, -1F); // Box 115
		attachmentModel[60].setRotationPoint(-7.5F, -3.5F, -3.5F);

		attachmentModel[61].addShapeBox(0F, -8F, -2F, 11, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F); // Box 116
		attachmentModel[61].setRotationPoint(-17F, -3.5F, -3.5F);

		attachmentModel[62].addShapeBox(0F, -9F, -2F, 11, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 117
		attachmentModel[62].setRotationPoint(-17F, -3.5F, -3.5F);

		attachmentModel[63].addShapeBox(0F, -5F, -2F, 11, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F); // Box 118
		attachmentModel[63].setRotationPoint(-17F, -3.5F, -3.5F);

		attachmentModel[64].addShapeBox(0F, -8F, -2F, 1, 3, 1, 0F, 0.5F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 119
		attachmentModel[64].setRotationPoint(-1.5F, -3.5F, -3.5F);

		attachmentModel[65].addShapeBox(0F, -9F, -2F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F); // Box 120
		attachmentModel[65].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[66].addShapeBox(0F, -5F, -2F, 1, 1, 1, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 121
		attachmentModel[66].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[67].addShapeBox(0F, -8.5F, -3.5F, 2, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 122
		attachmentModel[67].setRotationPoint(-5F, -3.5F, -3.5F);

		attachmentModel[68].addShapeBox(0F, -7.5F, -3.5F, 1, 2, 3, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 123
		attachmentModel[68].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[69].addShapeBox(0F, -7.5F, -3.5F, 1, 2, 3, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F); // Box 124
		attachmentModel[69].setRotationPoint(-6F, -3.5F, -3.5F);

		attachmentModel[70].addShapeBox(0F, -14.5F, 4F, 1, 1, 1, 0F, 0.25F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0F, 0.25F, 0.5F, 0F, 0.25F, 0F, 0F, 0F, 0.25F, 0F, 0.5F); // Box 125
		attachmentModel[70].setRotationPoint(-3.5F, -3.5F, -3.5F);

		attachmentModel[71].addShapeBox(0F, -14.5F, 4F, 1, 1, 1, 0F, -0.5F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0.5F, 0F, 0.25F, 0.25F, 0F, 0.25F, 0.25F, 0F, 0.5F, 0F, 0F, 0F); // Box 126
		attachmentModel[71].setRotationPoint(-5.5F, -3.5F, -3.5F);

		attachmentModel[72].addShapeBox(0F, -14.5F, 2F, 1, 1, 1, 0F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0F, 0F, 0F, 0.25F, 0F, 0.5F, 0.25F, 0F, 0.25F, 0.5F, 0F, 0.25F); // Box 127
		attachmentModel[72].setRotationPoint(-5.5F, -3.5F, -3.5F);

		attachmentModel[73].addShapeBox(0F, -14.5F, 2F, 1, 1, 1, 0F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0.25F, 0.25F, 0F, 0.25F); // Box 128
		attachmentModel[73].setRotationPoint(-3.5F, -3.5F, -3.5F);

		attachmentModel[74].addShapeBox(0F, -8F, -4.5F, 1, 1, 1, 0F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0.25F, 0.5F, 0F, 0.25F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, 0.5F, 0.25F, 0F, 0.25F, 0.25F, 0F); // Box 129
		attachmentModel[74].setRotationPoint(-3.5F, -3.5F, -3.5F);

		attachmentModel[75].addShapeBox(0F, -6F, -4.5F, 1, 1, 1, 0F, 0.25F, 0.25F, -0.5F, -0.5F, 0.25F, -0.5F, 0.5F, 0.25F, 0F, 0.25F, 0.25F, 0F, 0.25F, -0.5F, -0.5F, -0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0.25F, 0.5F, 0F); // Box 130
		attachmentModel[75].setRotationPoint(-3.5F, -3.5F, -3.5F);

		attachmentModel[76].addShapeBox(0F, -6F, -4.5F, 1, 1, 1, 0F, -0.5F, 0.25F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, 0F, 0.5F, 0.25F, 0F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0.5F, 0F, 0F, 0F, 0F); // Box 131
		attachmentModel[76].setRotationPoint(-5.5F, -3.5F, -3.5F);

		attachmentModel[77].addShapeBox(0F, -8F, -4.5F, 1, 1, 1, 0F, -0.5F, -0.5F, -0.5F, 0.25F, -0.5F, -0.5F, 0.25F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.25F, -0.5F, 0.25F, 0.25F, -0.5F, 0.25F, 0.25F, 0F, 0.5F, 0.25F, 0F); // Box 132
		attachmentModel[77].setRotationPoint(-5.5F, -3.5F, -3.5F);

		attachmentModel[78].addShapeBox(0F, -10F, 6F, 11, 2, 1, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 161
		attachmentModel[78].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[79].addShapeBox(0F, -5F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -1.5F, 0.5F, 0F, -1.5F, 0.5F); // Box 162
		attachmentModel[79].setRotationPoint(-4.5F, -3.5F, -3.5F);

		attachmentModel[80].addShapeBox(0F, -9F, 1F, 1, 5, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 163
		attachmentModel[80].setRotationPoint(5F, -3.5F, -3.5F);

		attachmentModel[81].addShapeBox(0F, -5F, 7F, 19, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 92
		attachmentModel[81].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[82].addShapeBox(0F, -11F, 7F, 19, 3, 1, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 93
		attachmentModel[82].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[83].addShapeBox(0F, -11F, -1F, 19, 3, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 94
		attachmentModel[83].setRotationPoint(-19.5F, -3.5F, -3.5F);

		attachmentModel[84].addShapeBox(0F, -12F, -2F, 15, 4, 1, 0F, 0.5F, 0F, -3F, 0F, 0F, -3F, 1F, -1F, 3F, 0.5F, -1F, 3F, 0.5F, -1F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F); // Box 95
		attachmentModel[84].setRotationPoint(-16.5F, -3.5F, -3.5F);

		attachmentModel[85].addShapeBox(0F, -5F, -2F, 15, 4, 1, 0F, 0.5F, -1F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -3F, 0F, 0F, -3F, 1F, -1F, 3F, 0.5F, -1F, 3F); // Box 96
		attachmentModel[85].setRotationPoint(-16.5F, -3.5F, -3.5F);

		attachmentModel[86].addShapeBox(0F, -5F, 8F, 2, 4, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 3F, 0.5F, -1F, 3F, -0.5F, 0F, -3F, 0F, 0F, -3F); // Box 97
		attachmentModel[86].setRotationPoint(-3F, -3.5F, -3.5F);

		attachmentModel[87].addShapeBox(0F, -5F, 8F, 9, 4, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 3F, 0F, -1F, 3F, 0F, 0F, -3F, 0.5F, 0F, -3F); // Box 98
		attachmentModel[87].setRotationPoint(-16.5F, -3.5F, -3.5F);

		attachmentModel[88].addShapeBox(0F, -5F, 5F, 4, 3, 1, 0F, 0F, 0F, -3F, 0.5F, 0F, -3F, 0.5F, -0.5F, 2.5F, 0F, -0.5F, 2.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F); // Box 99
		attachmentModel[88].setRotationPoint(-7.5F, -3.5F, -3.5F);

		pupilRefCenter.set(-18.5F / 16F, 10F / 16F, 0F);
		scopeGlassModelScale.set(0.02F, 0.02F, 0.02F);
		scopeObjectiveLensCenter.set(6.5F / 160F, 10F / 160F, 0F);
		scopeMaskMultRotY = scopeMaskMultRotZ = 0.3F;
		scopeMaskMultTransY = scopeMaskMultTransZ = 0.24F;
		scopeMaskScale = 0.32F / 1600F;
		minPupilDistance = 0.01828F;

		flipAll();
	}
}