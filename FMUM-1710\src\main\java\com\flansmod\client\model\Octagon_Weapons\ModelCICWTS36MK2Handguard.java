//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC WTS-36 MK2 Handguard
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36MK2Handguard extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 256;

	public ModelCICWTS36MK2Handguard() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[356];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 220
		attachmentModel[1] = new ModelRendererTurbo(this, 22, 1, textureX, textureY); // Box 459
		attachmentModel[2] = new ModelRendererTurbo(this, 204, 1, textureX, textureY); // Box 460
		attachmentModel[3] = new ModelRendererTurbo(this, 22, 6, textureX, textureY); // Box 461
		attachmentModel[4] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 462
		attachmentModel[5] = new ModelRendererTurbo(this, 31, 9, textureX, textureY); // Box 463
		attachmentModel[6] = new ModelRendererTurbo(this, 118, 9, textureX, textureY); // Box 464
		attachmentModel[7] = new ModelRendererTurbo(this, 22, 9, textureX, textureY); // Box 351
		attachmentModel[8] = new ModelRendererTurbo(this, 22, 12, textureX, textureY); // Box 352
		attachmentModel[9] = new ModelRendererTurbo(this, 43, 6, textureX, textureY); // Box 353
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 18, textureX, textureY); // Box 367
		attachmentModel[11] = new ModelRendererTurbo(this, 192, 6, textureX, textureY); // Box 369
		attachmentModel[12] = new ModelRendererTurbo(this, 197, 13, textureX, textureY); // Box 370
		attachmentModel[13] = new ModelRendererTurbo(this, 187, 13, textureX, textureY); // Box 373
		attachmentModel[14] = new ModelRendererTurbo(this, 192, 13, textureX, textureY); // Box 374
		attachmentModel[15] = new ModelRendererTurbo(this, 203, 6, textureX, textureY); // Box 375
		attachmentModel[16] = new ModelRendererTurbo(this, 61, 15, textureX, textureY); // Box 376
		attachmentModel[17] = new ModelRendererTurbo(this, 167, 13, textureX, textureY); // Box 377
		attachmentModel[18] = new ModelRendererTurbo(this, 214, 6, textureX, textureY); // Box 378
		attachmentModel[19] = new ModelRendererTurbo(this, 225, 6, textureX, textureY); // Box 379
		attachmentModel[20] = new ModelRendererTurbo(this, 162, 13, textureX, textureY); // Box 380
		attachmentModel[21] = new ModelRendererTurbo(this, 225, 9, textureX, textureY); // Box 381
		attachmentModel[22] = new ModelRendererTurbo(this, 172, 13, textureX, textureY); // Box 382
		attachmentModel[23] = new ModelRendererTurbo(this, 177, 13, textureX, textureY); // Box 383
		attachmentModel[24] = new ModelRendererTurbo(this, 182, 13, textureX, textureY); // Box 384
		attachmentModel[25] = new ModelRendererTurbo(this, 64, 18, textureX, textureY); // Box 385
		attachmentModel[26] = new ModelRendererTurbo(this, 118, 13, textureX, textureY); // Box 391
		attachmentModel[27] = new ModelRendererTurbo(this, 133, 13, textureX, textureY); // Box 392
		attachmentModel[28] = new ModelRendererTurbo(this, 142, 13, textureX, textureY); // Box 393
		attachmentModel[29] = new ModelRendererTurbo(this, 202, 13, textureX, textureY); // Box 394
		attachmentModel[30] = new ModelRendererTurbo(this, 31, 15, textureX, textureY); // Box 395
		attachmentModel[31] = new ModelRendererTurbo(this, 151, 13, textureX, textureY); // Box 396
		attachmentModel[32] = new ModelRendererTurbo(this, 66, 15, textureX, textureY); // Box 397
		attachmentModel[33] = new ModelRendererTurbo(this, 96, 37, textureX, textureY); // Box 398
		attachmentModel[34] = new ModelRendererTurbo(this, 160, 16, textureX, textureY); // Box 399
		attachmentModel[35] = new ModelRendererTurbo(this, 171, 16, textureX, textureY); // Box 400
		attachmentModel[36] = new ModelRendererTurbo(this, 102, 40, textureX, textureY); // Box 401
		attachmentModel[37] = new ModelRendererTurbo(this, 59, 43, textureX, textureY); // Box 402
		attachmentModel[38] = new ModelRendererTurbo(this, 64, 43, textureX, textureY); // Box 403
		attachmentModel[39] = new ModelRendererTurbo(this, 69, 43, textureX, textureY); // Box 404
		attachmentModel[40] = new ModelRendererTurbo(this, 74, 43, textureX, textureY); // Box 405
		attachmentModel[41] = new ModelRendererTurbo(this, 79, 43, textureX, textureY); // Box 406
		attachmentModel[42] = new ModelRendererTurbo(this, 84, 43, textureX, textureY); // Box 407
		attachmentModel[43] = new ModelRendererTurbo(this, 1, 28, textureX, textureY); // Box 408
		attachmentModel[44] = new ModelRendererTurbo(this, 1, 43, textureX, textureY); // Box 409
		attachmentModel[45] = new ModelRendererTurbo(this, 1, 35, textureX, textureY); // Box 411
		attachmentModel[46] = new ModelRendererTurbo(this, 10, 43, textureX, textureY); // Box 412
		attachmentModel[47] = new ModelRendererTurbo(this, 19, 43, textureX, textureY); // Box 413
		attachmentModel[48] = new ModelRendererTurbo(this, 24, 43, textureX, textureY); // Box 414
		attachmentModel[49] = new ModelRendererTurbo(this, 29, 43, textureX, textureY); // Box 415
		attachmentModel[50] = new ModelRendererTurbo(this, 34, 43, textureX, textureY); // Box 416
		attachmentModel[51] = new ModelRendererTurbo(this, 39, 43, textureX, textureY); // Box 417
		attachmentModel[52] = new ModelRendererTurbo(this, 44, 43, textureX, textureY); // Box 418
		attachmentModel[53] = new ModelRendererTurbo(this, 49, 43, textureX, textureY); // Box 419
		attachmentModel[54] = new ModelRendererTurbo(this, 54, 43, textureX, textureY); // Box 420
		attachmentModel[55] = new ModelRendererTurbo(this, 208, 21, textureX, textureY); // Box 422
		attachmentModel[56] = new ModelRendererTurbo(this, 154, 28, textureX, textureY); // Box 423
		attachmentModel[57] = new ModelRendererTurbo(this, 75, 18, textureX, textureY); // Box 419
		attachmentModel[58] = new ModelRendererTurbo(this, 133, 16, textureX, textureY); // Box 420
		attachmentModel[59] = new ModelRendererTurbo(this, 142, 16, textureX, textureY); // Box 421
		attachmentModel[60] = new ModelRendererTurbo(this, 151, 16, textureX, textureY); // Box 422
		attachmentModel[61] = new ModelRendererTurbo(this, 46, 15, textureX, textureY); // Box 423
		attachmentModel[62] = new ModelRendererTurbo(this, 180, 16, textureX, textureY); // Box 424
		attachmentModel[63] = new ModelRendererTurbo(this, 190, 52, textureX, textureY); // Box 425
		attachmentModel[64] = new ModelRendererTurbo(this, 1, 55, textureX, textureY); // Box 426
		attachmentModel[65] = new ModelRendererTurbo(this, 185, 52, textureX, textureY); // Box 427
		attachmentModel[66] = new ModelRendererTurbo(this, 214, 16, textureX, textureY); // Box 428
		attachmentModel[67] = new ModelRendererTurbo(this, 12, 47, textureX, textureY); // Box 429
		attachmentModel[68] = new ModelRendererTurbo(this, 102, 32, textureX, textureY); // Box 430
		attachmentModel[69] = new ModelRendererTurbo(this, 175, 52, textureX, textureY); // Box 431
		attachmentModel[70] = new ModelRendererTurbo(this, 146, 32, textureX, textureY); // Box 432
		attachmentModel[71] = new ModelRendererTurbo(this, 113, 32, textureX, textureY); // Box 433
		attachmentModel[72] = new ModelRendererTurbo(this, 1, 52, textureX, textureY); // Box 434
		attachmentModel[73] = new ModelRendererTurbo(this, 124, 32, textureX, textureY); // Box 435
		attachmentModel[74] = new ModelRendererTurbo(this, 155, 52, textureX, textureY); // Box 436
		attachmentModel[75] = new ModelRendererTurbo(this, 241, 47, textureX, textureY); // Box 437
		attachmentModel[76] = new ModelRendererTurbo(this, 230, 47, textureX, textureY); // Box 438
		attachmentModel[77] = new ModelRendererTurbo(this, 246, 47, textureX, textureY); // Box 439
		attachmentModel[78] = new ModelRendererTurbo(this, 135, 32, textureX, textureY); // Box 440
		attachmentModel[79] = new ModelRendererTurbo(this, 103, 19, textureX, textureY); // Box 441
		attachmentModel[80] = new ModelRendererTurbo(this, 180, 52, textureX, textureY); // Box 442
		attachmentModel[81] = new ModelRendererTurbo(this, 237, 1, textureX, textureY); // Box 443
		attachmentModel[82] = new ModelRendererTurbo(this, 165, 52, textureX, textureY); // Box 444
		attachmentModel[83] = new ModelRendererTurbo(this, 82, 58, textureX, textureY); // Box 445
		attachmentModel[84] = new ModelRendererTurbo(this, 170, 52, textureX, textureY); // Box 446
		attachmentModel[85] = new ModelRendererTurbo(this, 150, 52, textureX, textureY); // Box 447
		attachmentModel[86] = new ModelRendererTurbo(this, 49, 58, textureX, textureY); // Box 448
		attachmentModel[87] = new ModelRendererTurbo(this, 91, 18, textureX, textureY); // Box 449
		attachmentModel[88] = new ModelRendererTurbo(this, 206, 50, textureX, textureY); // Box 450
		attachmentModel[89] = new ModelRendererTurbo(this, 160, 52, textureX, textureY); // Box 451
		attachmentModel[90] = new ModelRendererTurbo(this, 242, 11, textureX, textureY); // Box 452
		attachmentModel[91] = new ModelRendererTurbo(this, 242, 28, textureX, textureY); // Box 453
		attachmentModel[92] = new ModelRendererTurbo(this, 242, 8, textureX, textureY); // Box 454
		attachmentModel[93] = new ModelRendererTurbo(this, 228, 37, textureX, textureY); // Box 455
		attachmentModel[94] = new ModelRendererTurbo(this, 18, 63, textureX, textureY); // Box 456
		attachmentModel[95] = new ModelRendererTurbo(this, 18, 66, textureX, textureY); // Box 457
		attachmentModel[96] = new ModelRendererTurbo(this, 239, 37, textureX, textureY); // Box 458
		attachmentModel[97] = new ModelRendererTurbo(this, 54, 62, textureX, textureY); // Box 459
		attachmentModel[98] = new ModelRendererTurbo(this, 60, 58, textureX, textureY); // Box 460
		attachmentModel[99] = new ModelRendererTurbo(this, 54, 66, textureX, textureY); // Box 461
		attachmentModel[100] = new ModelRendererTurbo(this, 139, 61, textureX, textureY); // Box 462
		attachmentModel[101] = new ModelRendererTurbo(this, 71, 58, textureX, textureY); // Box 463
		attachmentModel[102] = new ModelRendererTurbo(this, 60, 62, textureX, textureY); // Box 464
		attachmentModel[103] = new ModelRendererTurbo(this, 239, 66, textureX, textureY); // Box 465
		attachmentModel[104] = new ModelRendererTurbo(this, 106, 61, textureX, textureY); // Box 466
		attachmentModel[105] = new ModelRendererTurbo(this, 60, 66, textureX, textureY); // Box 467
		attachmentModel[106] = new ModelRendererTurbo(this, 121, 58, textureX, textureY); // Box 468
		attachmentModel[107] = new ModelRendererTurbo(this, 106, 64, textureX, textureY); // Box 469
		attachmentModel[108] = new ModelRendererTurbo(this, 239, 50, textureX, textureY); // Box 470
		attachmentModel[109] = new ModelRendererTurbo(this, 118, 61, textureX, textureY); // Box 471
		attachmentModel[110] = new ModelRendererTurbo(this, 118, 64, textureX, textureY); // Box 472
		attachmentModel[111] = new ModelRendererTurbo(this, 217, 50, textureX, textureY); // Box 473
		attachmentModel[112] = new ModelRendererTurbo(this, 132, 58, textureX, textureY); // Box 474
		attachmentModel[113] = new ModelRendererTurbo(this, 228, 50, textureX, textureY); // Box 475
		attachmentModel[114] = new ModelRendererTurbo(this, 73, 61, textureX, textureY); // Box 476
		attachmentModel[115] = new ModelRendererTurbo(this, 73, 64, textureX, textureY); // Box 477
		attachmentModel[116] = new ModelRendererTurbo(this, 30, 66, textureX, textureY); // Box 478
		attachmentModel[117] = new ModelRendererTurbo(this, 84, 61, textureX, textureY); // Box 479
		attachmentModel[118] = new ModelRendererTurbo(this, 30, 63, textureX, textureY); // Box 480
		attachmentModel[119] = new ModelRendererTurbo(this, 84, 64, textureX, textureY); // Box 481
		attachmentModel[120] = new ModelRendererTurbo(this, 95, 61, textureX, textureY); // Box 482
		attachmentModel[121] = new ModelRendererTurbo(this, 201, 72, textureX, textureY); // Box 483
		attachmentModel[122] = new ModelRendererTurbo(this, 95, 64, textureX, textureY); // Box 484
		attachmentModel[123] = new ModelRendererTurbo(this, 219, 69, textureX, textureY); // Box 490
		attachmentModel[124] = new ModelRendererTurbo(this, 200, 69, textureX, textureY); // Box 492
		attachmentModel[125] = new ModelRendererTurbo(this, 35, 62, textureX, textureY); // Box 495
		attachmentModel[126] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 498
		attachmentModel[127] = new ModelRendererTurbo(this, 135, 55, textureX, textureY); // Box 499
		attachmentModel[128] = new ModelRendererTurbo(this, 128, 61, textureX, textureY); // Box 500
		attachmentModel[129] = new ModelRendererTurbo(this, 128, 64, textureX, textureY); // Box 501
		attachmentModel[130] = new ModelRendererTurbo(this, 137, 58, textureX, textureY); // Box 502
		attachmentModel[131] = new ModelRendererTurbo(this, 146, 55, textureX, textureY); // Box 503
		attachmentModel[132] = new ModelRendererTurbo(this, 157, 55, textureX, textureY); // Box 504
		attachmentModel[133] = new ModelRendererTurbo(this, 169, 58, textureX, textureY); // Box 505
		attachmentModel[134] = new ModelRendererTurbo(this, 174, 58, textureX, textureY); // Box 506
		attachmentModel[135] = new ModelRendererTurbo(this, 218, 22, textureX, textureY); // Box 507
		attachmentModel[136] = new ModelRendererTurbo(this, 212, 72, textureX, textureY); // Box 508
		attachmentModel[137] = new ModelRendererTurbo(this, 223, 72, textureX, textureY); // Box 509
		attachmentModel[138] = new ModelRendererTurbo(this, 150, 61, textureX, textureY); // Box 510
		attachmentModel[139] = new ModelRendererTurbo(this, 150, 64, textureX, textureY); // Box 511
		attachmentModel[140] = new ModelRendererTurbo(this, 249, 21, textureX, textureY); // Box 512
		attachmentModel[141] = new ModelRendererTurbo(this, 245, 72, textureX, textureY); // Box 513
		attachmentModel[142] = new ModelRendererTurbo(this, 234, 72, textureX, textureY); // Box 514
		attachmentModel[143] = new ModelRendererTurbo(this, 249, 18, textureX, textureY); // Box 515
		attachmentModel[144] = new ModelRendererTurbo(this, 251, 53, textureX, textureY); // Box 516
		attachmentModel[145] = new ModelRendererTurbo(this, 251, 56, textureX, textureY); // Box 517
		attachmentModel[146] = new ModelRendererTurbo(this, 240, 63, textureX, textureY); // Box 518
		attachmentModel[147] = new ModelRendererTurbo(this, 186, 55, textureX, textureY); // Box 519
		attachmentModel[148] = new ModelRendererTurbo(this, 139, 64, textureX, textureY); // Box 520
		attachmentModel[149] = new ModelRendererTurbo(this, 164, 58, textureX, textureY); // Box 521
		attachmentModel[150] = new ModelRendererTurbo(this, 218, 19, textureX, textureY); // Box 522
		attachmentModel[151] = new ModelRendererTurbo(this, 153, 58, textureX, textureY); // Box 523
		attachmentModel[152] = new ModelRendererTurbo(this, 168, 55, textureX, textureY); // Box 524
		attachmentModel[153] = new ModelRendererTurbo(this, 218, 25, textureX, textureY); // Box 525
		attachmentModel[154] = new ModelRendererTurbo(this, 250, 35, textureX, textureY); // Box 526
		attachmentModel[155] = new ModelRendererTurbo(this, 250, 38, textureX, textureY); // Box 527
		attachmentModel[156] = new ModelRendererTurbo(this, 38, 58, textureX, textureY); // Box 528
		attachmentModel[157] = new ModelRendererTurbo(this, 186, 58, textureX, textureY); // Box 529
		attachmentModel[158] = new ModelRendererTurbo(this, 249, 24, textureX, textureY); // Box 530
		attachmentModel[159] = new ModelRendererTurbo(this, 251, 59, textureX, textureY); // Box 531
		attachmentModel[160] = new ModelRendererTurbo(this, 251, 62, textureX, textureY); // Box 532
		attachmentModel[161] = new ModelRendererTurbo(this, 186, 61, textureX, textureY); // Box 533
		attachmentModel[162] = new ModelRendererTurbo(this, 142, 58, textureX, textureY); // Box 534
		attachmentModel[163] = new ModelRendererTurbo(this, 251, 47, textureX, textureY); // Box 535
		attachmentModel[164] = new ModelRendererTurbo(this, 251, 44, textureX, textureY); // Box 536
		attachmentModel[165] = new ModelRendererTurbo(this, 250, 41, textureX, textureY); // Box 537
		attachmentModel[166] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 538
		attachmentModel[167] = new ModelRendererTurbo(this, 17, 97, textureX, textureY); // Box 539
		attachmentModel[168] = new ModelRendererTurbo(this, 33, 97, textureX, textureY); // Box 540
		attachmentModel[169] = new ModelRendererTurbo(this, 41, 97, textureX, textureY); // Box 541
		attachmentModel[170] = new ModelRendererTurbo(this, 49, 97, textureX, textureY); // Box 542
		attachmentModel[171] = new ModelRendererTurbo(this, 57, 97, textureX, textureY); // Box 543
		attachmentModel[172] = new ModelRendererTurbo(this, 73, 97, textureX, textureY); // Box 544
		attachmentModel[173] = new ModelRendererTurbo(this, 89, 97, textureX, textureY); // Box 545
		attachmentModel[174] = new ModelRendererTurbo(this, 97, 97, textureX, textureY); // Box 546
		attachmentModel[175] = new ModelRendererTurbo(this, 105, 97, textureX, textureY); // Box 547
		attachmentModel[176] = new ModelRendererTurbo(this, 113, 97, textureX, textureY); // Box 548
		attachmentModel[177] = new ModelRendererTurbo(this, 129, 97, textureX, textureY); // Box 555
		attachmentModel[178] = new ModelRendererTurbo(this, 191, 16, textureX, textureY); // Box 556
		attachmentModel[179] = new ModelRendererTurbo(this, 221, 75, textureX, textureY); // Box 557
		attachmentModel[180] = new ModelRendererTurbo(this, 177, 97, textureX, textureY); // Box 559
		attachmentModel[181] = new ModelRendererTurbo(this, 193, 97, textureX, textureY); // Box 560
		attachmentModel[182] = new ModelRendererTurbo(this, 221, 78, textureX, textureY); // Box 561
		attachmentModel[183] = new ModelRendererTurbo(this, 227, 63, textureX, textureY); // Box 562
		attachmentModel[184] = new ModelRendererTurbo(this, 234, 75, textureX, textureY); // Box 563
		attachmentModel[185] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 564
		attachmentModel[186] = new ModelRendererTurbo(this, 17, 105, textureX, textureY); // Box 565
		attachmentModel[187] = new ModelRendererTurbo(this, 33, 105, textureX, textureY); // Box 566
		attachmentModel[188] = new ModelRendererTurbo(this, 49, 105, textureX, textureY); // Box 567
		attachmentModel[189] = new ModelRendererTurbo(this, 65, 105, textureX, textureY); // Box 568
		attachmentModel[190] = new ModelRendererTurbo(this, 81, 105, textureX, textureY); // Box 569
		attachmentModel[191] = new ModelRendererTurbo(this, 97, 105, textureX, textureY); // Box 570
		attachmentModel[192] = new ModelRendererTurbo(this, 105, 105, textureX, textureY); // Box 571
		attachmentModel[193] = new ModelRendererTurbo(this, 113, 105, textureX, textureY); // Box 572
		attachmentModel[194] = new ModelRendererTurbo(this, 121, 105, textureX, textureY); // Box 573
		attachmentModel[195] = new ModelRendererTurbo(this, 137, 105, textureX, textureY); // Box 574
		attachmentModel[196] = new ModelRendererTurbo(this, 153, 105, textureX, textureY); // Box 575
		attachmentModel[197] = new ModelRendererTurbo(this, 161, 105, textureX, textureY); // Box 576
		attachmentModel[198] = new ModelRendererTurbo(this, 169, 105, textureX, textureY); // Box 577
		attachmentModel[199] = new ModelRendererTurbo(this, 177, 105, textureX, textureY); // Box 578
		attachmentModel[200] = new ModelRendererTurbo(this, 193, 105, textureX, textureY); // Box 579
		attachmentModel[201] = new ModelRendererTurbo(this, 209, 105, textureX, textureY); // Box 580
		attachmentModel[202] = new ModelRendererTurbo(this, 217, 105, textureX, textureY); // Box 581
		attachmentModel[203] = new ModelRendererTurbo(this, 225, 105, textureX, textureY); // Box 582
		attachmentModel[204] = new ModelRendererTurbo(this, 186, 64, textureX, textureY); // Box 583
		attachmentModel[205] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 584
		attachmentModel[206] = new ModelRendererTurbo(this, 251, 50, textureX, textureY); // Box 585
		attachmentModel[207] = new ModelRendererTurbo(this, 17, 113, textureX, textureY); // Box 586
		attachmentModel[208] = new ModelRendererTurbo(this, 25, 113, textureX, textureY); // Box 587
		attachmentModel[209] = new ModelRendererTurbo(this, 33, 113, textureX, textureY); // Box 588
		attachmentModel[210] = new ModelRendererTurbo(this, 49, 113, textureX, textureY); // Box 589
		attachmentModel[211] = new ModelRendererTurbo(this, 137, 113, textureX, textureY); // Box 590
		attachmentModel[212] = new ModelRendererTurbo(this, 1, 121, textureX, textureY); // Box 591
		attachmentModel[213] = new ModelRendererTurbo(this, 89, 121, textureX, textureY); // Box 592
		attachmentModel[214] = new ModelRendererTurbo(this, 217, 113, textureX, textureY); // Box 593
		attachmentModel[215] = new ModelRendererTurbo(this, 1, 129, textureX, textureY); // Box 594
		attachmentModel[216] = new ModelRendererTurbo(this, 33, 129, textureX, textureY); // Box 595
		attachmentModel[217] = new ModelRendererTurbo(this, 65, 129, textureX, textureY); // Box 596
		attachmentModel[218] = new ModelRendererTurbo(this, 233, 121, textureX, textureY); // Box 597
		attachmentModel[219] = new ModelRendererTurbo(this, 89, 129, textureX, textureY); // Box 598
		attachmentModel[220] = new ModelRendererTurbo(this, 97, 129, textureX, textureY); // Box 599
		attachmentModel[221] = new ModelRendererTurbo(this, 241, 113, textureX, textureY); // Box 600
		attachmentModel[222] = new ModelRendererTurbo(this, 249, 113, textureX, textureY); // Box 601
		attachmentModel[223] = new ModelRendererTurbo(this, 137, 129, textureX, textureY); // Box 602
		attachmentModel[224] = new ModelRendererTurbo(this, 161, 129, textureX, textureY); // Box 603
		attachmentModel[225] = new ModelRendererTurbo(this, 201, 129, textureX, textureY); // Box 604
		attachmentModel[226] = new ModelRendererTurbo(this, 1, 137, textureX, textureY); // Box 605
		attachmentModel[227] = new ModelRendererTurbo(this, 161, 137, textureX, textureY); // Box 606
		attachmentModel[228] = new ModelRendererTurbo(this, 249, 129, textureX, textureY); // Box 607
		attachmentModel[229] = new ModelRendererTurbo(this, 1, 153, textureX, textureY); // Box 608
		attachmentModel[230] = new ModelRendererTurbo(this, 1, 145, textureX, textureY); // Box 609
		attachmentModel[231] = new ModelRendererTurbo(this, 161, 145, textureX, textureY); // Box 610
		attachmentModel[232] = new ModelRendererTurbo(this, 153, 153, textureX, textureY); // Box 611
		attachmentModel[233] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 612
		attachmentModel[234] = new ModelRendererTurbo(this, 25, 129, textureX, textureY); // Box 613
		attachmentModel[235] = new ModelRendererTurbo(this, 57, 129, textureX, textureY); // Box 614
		attachmentModel[236] = new ModelRendererTurbo(this, 81, 129, textureX, textureY); // Box 615
		attachmentModel[237] = new ModelRendererTurbo(this, 121, 129, textureX, textureY); // Box 616
		attachmentModel[238] = new ModelRendererTurbo(this, 105, 129, textureX, textureY); // Box 617
		attachmentModel[239] = new ModelRendererTurbo(this, 225, 129, textureX, textureY); // Box 618
		attachmentModel[240] = new ModelRendererTurbo(this, 241, 129, textureX, textureY); // Box 619
		attachmentModel[241] = new ModelRendererTurbo(this, 121, 137, textureX, textureY); // Box 620
		attachmentModel[242] = new ModelRendererTurbo(this, 129, 137, textureX, textureY); // Box 621
		attachmentModel[243] = new ModelRendererTurbo(this, 241, 153, textureX, textureY); // Box 622
		attachmentModel[244] = new ModelRendererTurbo(this, 153, 161, textureX, textureY); // Box 623
		attachmentModel[245] = new ModelRendererTurbo(this, 89, 145, textureX, textureY); // Box 624
		attachmentModel[246] = new ModelRendererTurbo(this, 129, 145, textureX, textureY); // Box 625
		attachmentModel[247] = new ModelRendererTurbo(this, 169, 161, textureX, textureY); // Box 626
		attachmentModel[248] = new ModelRendererTurbo(this, 177, 161, textureX, textureY); // Box 627
		attachmentModel[249] = new ModelRendererTurbo(this, 193, 161, textureX, textureY); // Box 628
		attachmentModel[250] = new ModelRendererTurbo(this, 209, 161, textureX, textureY); // Box 629
		attachmentModel[251] = new ModelRendererTurbo(this, 217, 161, textureX, textureY); // Box 630
		attachmentModel[252] = new ModelRendererTurbo(this, 225, 161, textureX, textureY); // Box 631
		attachmentModel[253] = new ModelRendererTurbo(this, 233, 161, textureX, textureY); // Box 632
		attachmentModel[254] = new ModelRendererTurbo(this, 1, 169, textureX, textureY); // Box 633
		attachmentModel[255] = new ModelRendererTurbo(this, 17, 169, textureX, textureY); // Box 634
		attachmentModel[256] = new ModelRendererTurbo(this, 33, 169, textureX, textureY); // Box 635
		attachmentModel[257] = new ModelRendererTurbo(this, 49, 169, textureX, textureY); // Box 636
		attachmentModel[258] = new ModelRendererTurbo(this, 65, 169, textureX, textureY); // Box 637
		attachmentModel[259] = new ModelRendererTurbo(this, 81, 169, textureX, textureY); // Box 638
		attachmentModel[260] = new ModelRendererTurbo(this, 97, 169, textureX, textureY); // Box 639
		attachmentModel[261] = new ModelRendererTurbo(this, 113, 169, textureX, textureY); // Box 640
		attachmentModel[262] = new ModelRendererTurbo(this, 129, 169, textureX, textureY); // Box 641
		attachmentModel[263] = new ModelRendererTurbo(this, 145, 169, textureX, textureY); // Box 642
		attachmentModel[264] = new ModelRendererTurbo(this, 161, 169, textureX, textureY); // Box 643
		attachmentModel[265] = new ModelRendererTurbo(this, 249, 161, textureX, textureY); // Box 644
		attachmentModel[266] = new ModelRendererTurbo(this, 177, 169, textureX, textureY); // Box 645
		attachmentModel[267] = new ModelRendererTurbo(this, 185, 169, textureX, textureY); // Box 646
		attachmentModel[268] = new ModelRendererTurbo(this, 193, 169, textureX, textureY); // Box 647
		attachmentModel[269] = new ModelRendererTurbo(this, 1, 177, textureX, textureY); // Box 648
		attachmentModel[270] = new ModelRendererTurbo(this, 209, 169, textureX, textureY); // Box 649
		attachmentModel[271] = new ModelRendererTurbo(this, 225, 169, textureX, textureY); // Box 650
		attachmentModel[272] = new ModelRendererTurbo(this, 241, 169, textureX, textureY); // Box 651
		attachmentModel[273] = new ModelRendererTurbo(this, 153, 177, textureX, textureY); // Box 652
		attachmentModel[274] = new ModelRendererTurbo(this, 169, 177, textureX, textureY); // Box 653
		attachmentModel[275] = new ModelRendererTurbo(this, 177, 177, textureX, textureY); // Box 654
		attachmentModel[276] = new ModelRendererTurbo(this, 185, 177, textureX, textureY); // Box 655
		attachmentModel[277] = new ModelRendererTurbo(this, 193, 177, textureX, textureY); // Box 656
		attachmentModel[278] = new ModelRendererTurbo(this, 209, 177, textureX, textureY); // Box 657
		attachmentModel[279] = new ModelRendererTurbo(this, 225, 177, textureX, textureY); // Box 658
		attachmentModel[280] = new ModelRendererTurbo(this, 233, 177, textureX, textureY); // Box 659
		attachmentModel[281] = new ModelRendererTurbo(this, 241, 177, textureX, textureY); // Box 660
		attachmentModel[282] = new ModelRendererTurbo(this, 1, 185, textureX, textureY); // Box 661
		attachmentModel[283] = new ModelRendererTurbo(this, 17, 185, textureX, textureY); // Box 662
		attachmentModel[284] = new ModelRendererTurbo(this, 249, 177, textureX, textureY); // Box 663
		attachmentModel[285] = new ModelRendererTurbo(this, 33, 185, textureX, textureY); // Box 664
		attachmentModel[286] = new ModelRendererTurbo(this, 41, 185, textureX, textureY); // Box 665
		attachmentModel[287] = new ModelRendererTurbo(this, 49, 185, textureX, textureY); // Box 666
		attachmentModel[288] = new ModelRendererTurbo(this, 65, 185, textureX, textureY); // Box 667
		attachmentModel[289] = new ModelRendererTurbo(this, 81, 185, textureX, textureY); // Box 668
		attachmentModel[290] = new ModelRendererTurbo(this, 89, 185, textureX, textureY); // Box 669
		attachmentModel[291] = new ModelRendererTurbo(this, 97, 185, textureX, textureY); // Box 670
		attachmentModel[292] = new ModelRendererTurbo(this, 105, 185, textureX, textureY); // Box 671
		attachmentModel[293] = new ModelRendererTurbo(this, 121, 185, textureX, textureY); // Box 672
		attachmentModel[294] = new ModelRendererTurbo(this, 137, 185, textureX, textureY); // Box 673
		attachmentModel[295] = new ModelRendererTurbo(this, 145, 185, textureX, textureY); // Box 674
		attachmentModel[296] = new ModelRendererTurbo(this, 153, 185, textureX, textureY); // Box 675
		attachmentModel[297] = new ModelRendererTurbo(this, 161, 185, textureX, textureY); // Box 676
		attachmentModel[298] = new ModelRendererTurbo(this, 177, 185, textureX, textureY); // Box 677
		attachmentModel[299] = new ModelRendererTurbo(this, 193, 185, textureX, textureY); // Box 678
		attachmentModel[300] = new ModelRendererTurbo(this, 201, 185, textureX, textureY); // Box 679
		attachmentModel[301] = new ModelRendererTurbo(this, 209, 185, textureX, textureY); // Box 680
		attachmentModel[302] = new ModelRendererTurbo(this, 217, 185, textureX, textureY); // Box 681
		attachmentModel[303] = new ModelRendererTurbo(this, 233, 185, textureX, textureY); // Box 682
		attachmentModel[304] = new ModelRendererTurbo(this, 249, 185, textureX, textureY); // Box 683
		attachmentModel[305] = new ModelRendererTurbo(this, 1, 193, textureX, textureY); // Box 684
		attachmentModel[306] = new ModelRendererTurbo(this, 9, 193, textureX, textureY); // Box 685
		attachmentModel[307] = new ModelRendererTurbo(this, 17, 193, textureX, textureY); // Box 686
		attachmentModel[308] = new ModelRendererTurbo(this, 33, 193, textureX, textureY); // Box 687
		attachmentModel[309] = new ModelRendererTurbo(this, 49, 193, textureX, textureY); // Box 688
		attachmentModel[310] = new ModelRendererTurbo(this, 57, 193, textureX, textureY); // Box 689
		attachmentModel[311] = new ModelRendererTurbo(this, 65, 193, textureX, textureY); // Box 690
		attachmentModel[312] = new ModelRendererTurbo(this, 73, 193, textureX, textureY); // Box 691
		attachmentModel[313] = new ModelRendererTurbo(this, 89, 193, textureX, textureY); // Box 692
		attachmentModel[314] = new ModelRendererTurbo(this, 105, 193, textureX, textureY); // Box 693
		attachmentModel[315] = new ModelRendererTurbo(this, 113, 193, textureX, textureY); // Box 694
		attachmentModel[316] = new ModelRendererTurbo(this, 121, 193, textureX, textureY); // Box 695
		attachmentModel[317] = new ModelRendererTurbo(this, 129, 193, textureX, textureY); // Box 696
		attachmentModel[318] = new ModelRendererTurbo(this, 137, 193, textureX, textureY); // Box 702
		attachmentModel[319] = new ModelRendererTurbo(this, 177, 193, textureX, textureY); // Box 391
		attachmentModel[320] = new ModelRendererTurbo(this, 193, 193, textureX, textureY); // Box 392
		attachmentModel[321] = new ModelRendererTurbo(this, 209, 193, textureX, textureY); // Box 393
		attachmentModel[322] = new ModelRendererTurbo(this, 225, 193, textureX, textureY); // Box 394
		attachmentModel[323] = new ModelRendererTurbo(this, 241, 193, textureX, textureY); // Box 395
		attachmentModel[324] = new ModelRendererTurbo(this, 1, 201, textureX, textureY); // Box 396
		attachmentModel[325] = new ModelRendererTurbo(this, 17, 201, textureX, textureY); // Box 397
		attachmentModel[326] = new ModelRendererTurbo(this, 57, 201, textureX, textureY); // Box 398
		attachmentModel[327] = new ModelRendererTurbo(this, 73, 201, textureX, textureY); // Box 399
		attachmentModel[328] = new ModelRendererTurbo(this, 97, 201, textureX, textureY); // Box 400
		attachmentModel[329] = new ModelRendererTurbo(this, 137, 201, textureX, textureY); // Box 401
		attachmentModel[330] = new ModelRendererTurbo(this, 185, 201, textureX, textureY); // Box 402
		attachmentModel[331] = new ModelRendererTurbo(this, 209, 201, textureX, textureY); // Box 403
		attachmentModel[332] = new ModelRendererTurbo(this, 233, 201, textureX, textureY); // Box 404
		attachmentModel[333] = new ModelRendererTurbo(this, 169, 201, textureX, textureY); // Box 405
		attachmentModel[334] = new ModelRendererTurbo(this, 1, 209, textureX, textureY); // Box 406
		attachmentModel[335] = new ModelRendererTurbo(this, 17, 217, textureX, textureY); // Box 407
		attachmentModel[336] = new ModelRendererTurbo(this, 89, 201, textureX, textureY); // Box 408
		attachmentModel[337] = new ModelRendererTurbo(this, 81, 217, textureX, textureY); // Box 409
		attachmentModel[338] = new ModelRendererTurbo(this, 121, 217, textureX, textureY); // Box 410
		attachmentModel[339] = new ModelRendererTurbo(this, 1, 233, textureX, textureY); // Box 411
		attachmentModel[340] = new ModelRendererTurbo(this, 33, 233, textureX, textureY); // Box 412
		attachmentModel[341] = new ModelRendererTurbo(this, 233, 217, textureX, textureY); // Box 413
		attachmentModel[342] = new ModelRendererTurbo(this, 65, 233, textureX, textureY); // Box 414
		attachmentModel[343] = new ModelRendererTurbo(this, 17, 209, textureX, textureY); // Box 415
		attachmentModel[344] = new ModelRendererTurbo(this, 129, 201, textureX, textureY); // Box 416
		attachmentModel[345] = new ModelRendererTurbo(this, 205, 16, textureX, textureY); // Box 417
		attachmentModel[346] = new ModelRendererTurbo(this, 97, 233, textureX, textureY); // Box 418
		attachmentModel[347] = new ModelRendererTurbo(this, 153, 233, textureX, textureY); // Box 419
		attachmentModel[348] = new ModelRendererTurbo(this, 169, 233, textureX, textureY); // Box 420
		attachmentModel[349] = new ModelRendererTurbo(this, 113, 233, textureX, textureY); // Box 421
		attachmentModel[350] = new ModelRendererTurbo(this, 197, 18, textureX, textureY); // Box 422
		attachmentModel[351] = new ModelRendererTurbo(this, 233, 233, textureX, textureY); // Box 423
		attachmentModel[352] = new ModelRendererTurbo(this, 201, 201, textureX, textureY); // Box 424
		attachmentModel[353] = new ModelRendererTurbo(this, 33, 209, textureX, textureY); // Box 426
		attachmentModel[354] = new ModelRendererTurbo(this, 1, 221, textureX, textureY); // Box 426
		attachmentModel[355] = new ModelRendererTurbo(this, 212, 76, textureX, textureY); // Box 420

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 9, 15, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 220
		attachmentModel[0].setRotationPoint(6F, -6F, 6F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 89, 1, 3, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 459
		attachmentModel[1].setRotationPoint(0F, 10F, -5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, 0F, 0F, -1F); // Box 460
		attachmentModel[2].setRotationPoint(0F, 9F, 6F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 1F, 1F, 0F, 1F, 1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[3].setRotationPoint(6F, -7F, 6F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 15, 1, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 1F, -1F, 0F, 0F, 0F); // Box 462
		attachmentModel[4].setRotationPoint(15F, -6F, 6F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 42, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 463
		attachmentModel[5].setRotationPoint(16F, -7F, 5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 52, 2, 1, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 464
		attachmentModel[6].setRotationPoint(6F, -9F, 5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 351
		attachmentModel[7].setRotationPoint(60F, -6F, -0.5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, -1F, -1F, -1F, -1F, -1F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 1F, 0F, -3F, 1F, 0F, 3F, 0F, 0F, 3F); // Box 352
		attachmentModel[8].setRotationPoint(58F, -6F, 2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 73, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 353
		attachmentModel[9].setRotationPoint(16F, -3F, 5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 30, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F); // Box 367
		attachmentModel[10].setRotationPoint(59F, -6F, 1.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 369
		attachmentModel[11].setRotationPoint(65.5F, -6F, -1.5F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 370
		attachmentModel[12].setRotationPoint(68.5F, -6F, -1.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 373
		attachmentModel[13].setRotationPoint(68F, -6F, -0.5F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 374
		attachmentModel[14].setRotationPoint(68.5F, -6F, 0.5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 375
		attachmentModel[15].setRotationPoint(65.5F, -6F, 0.5F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 376
		attachmentModel[16].setRotationPoint(74F, -6F, -0.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 377
		attachmentModel[17].setRotationPoint(74.5F, -6F, 0.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 378
		attachmentModel[18].setRotationPoint(71.5F, -6F, 0.5F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 379
		attachmentModel[19].setRotationPoint(71.5F, -6F, -1.5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 380
		attachmentModel[20].setRotationPoint(74.5F, -6F, -1.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 381
		attachmentModel[21].setRotationPoint(77.5F, -6F, 0.5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 382
		attachmentModel[22].setRotationPoint(80.5F, -6F, 0.5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 383
		attachmentModel[23].setRotationPoint(80F, -6F, -0.5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 384
		attachmentModel[24].setRotationPoint(80.5F, -6F, -1.5F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 385
		attachmentModel[25].setRotationPoint(77.5F, -6F, -1.5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 391
		attachmentModel[26].setRotationPoint(83F, -6F, 0.5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 392
		attachmentModel[27].setRotationPoint(86F, -6F, 0.5F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 393
		attachmentModel[28].setRotationPoint(86F, -6F, -0.5F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 394
		attachmentModel[29].setRotationPoint(86F, -6F, -1.5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F); // Box 395
		attachmentModel[30].setRotationPoint(83F, -6F, -1.5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 396
		attachmentModel[31].setRotationPoint(60F, -6F, 0.5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 397
		attachmentModel[32].setRotationPoint(60F, -6F, 0.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 30, 1, 1, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 398
		attachmentModel[33].setRotationPoint(59F, -6F, -2.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 399
		attachmentModel[34].setRotationPoint(60F, -6F, -1.5F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 400
		attachmentModel[35].setRotationPoint(60F, -6F, -1.5F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 29, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 401
		attachmentModel[36].setRotationPoint(60F, -6F, 3F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 402
		attachmentModel[37].setRotationPoint(64F, -5F, 3F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 403
		attachmentModel[38].setRotationPoint(67F, -5F, 3F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 404
		attachmentModel[39].setRotationPoint(73F, -5F, 3F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 405
		attachmentModel[40].setRotationPoint(70F, -5F, 3F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 406
		attachmentModel[41].setRotationPoint(82F, -5F, 3F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 407
		attachmentModel[42].setRotationPoint(79F, -5F, 3F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 408
		attachmentModel[43].setRotationPoint(76F, -5F, 3F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -1.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, -1.5F, 0F, 2F); // Box 409
		attachmentModel[44].setRotationPoint(86F, -5F, 3F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 411
		attachmentModel[45].setRotationPoint(85F, -5F, 3F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 1F, 0F, 2F, 1F, 0F, -2F, 0F, 0F, -2F); // Box 412
		attachmentModel[46].setRotationPoint(58F, -5F, -4F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 413
		attachmentModel[47].setRotationPoint(64F, -5F, -4F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 414
		attachmentModel[48].setRotationPoint(67F, -5F, -4F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 415
		attachmentModel[49].setRotationPoint(70F, -5F, -4F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 416
		attachmentModel[50].setRotationPoint(73F, -5F, -4F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 417
		attachmentModel[51].setRotationPoint(76F, -5F, -4F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 418
		attachmentModel[52].setRotationPoint(79F, -5F, -4F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 419
		attachmentModel[53].setRotationPoint(82F, -5F, -4F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0.5F, 0F, -2F); // Box 420
		attachmentModel[54].setRotationPoint(85F, -5F, -4F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0.5F, -1F, -1F, -1.5F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, -1.5F, 0F, -3F); // Box 422
		attachmentModel[55].setRotationPoint(86F, -6F, -3F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 27, 1, 1, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 423
		attachmentModel[56].setRotationPoint(58F, -6F, -4F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 419
		attachmentModel[57].setRotationPoint(83F, 10F, -1.5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 420
		attachmentModel[58].setRotationPoint(86F, 10F, -1.5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 421
		attachmentModel[59].setRotationPoint(86F, 10F, -0.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 422
		attachmentModel[60].setRotationPoint(86F, 10F, 0.5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 423
		attachmentModel[61].setRotationPoint(83F, 10F, 0.5F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 424
		attachmentModel[62].setRotationPoint(77F, 10F, 0.5F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 425
		attachmentModel[63].setRotationPoint(80F, 10F, 0.5F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 426
		attachmentModel[64].setRotationPoint(79.5F, 10F, -0.5F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 427
		attachmentModel[65].setRotationPoint(80F, 10F, -1.5F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 428
		attachmentModel[66].setRotationPoint(77F, 10F, -1.5F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 89, 1, 3, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 429
		attachmentModel[67].setRotationPoint(0F, 10F, 2F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 430
		attachmentModel[68].setRotationPoint(71F, 10F, -1.5F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 431
		attachmentModel[69].setRotationPoint(74F, 10F, -1.5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 432
		attachmentModel[70].setRotationPoint(73.5F, 10F, -0.5F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 433
		attachmentModel[71].setRotationPoint(71F, 10F, 0.5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 434
		attachmentModel[72].setRotationPoint(74F, 10F, 0.5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 435
		attachmentModel[73].setRotationPoint(65F, 10F, -1.5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 436
		attachmentModel[74].setRotationPoint(68F, 10F, -1.5F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 437
		attachmentModel[75].setRotationPoint(67.5F, 10F, -0.5F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 438
		attachmentModel[76].setRotationPoint(65F, 10F, 0.5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 439
		attachmentModel[77].setRotationPoint(68F, 10F, 0.5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 440
		attachmentModel[78].setRotationPoint(59F, 10F, -1.5F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 441
		attachmentModel[79].setRotationPoint(62F, 10F, -1.5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 442
		attachmentModel[80].setRotationPoint(61.5F, 10F, -0.5F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 443
		attachmentModel[81].setRotationPoint(59F, 10F, 0.5F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 444
		attachmentModel[82].setRotationPoint(62F, 10F, 0.5F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 445
		attachmentModel[83].setRotationPoint(53F, 10F, -1.5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 446
		attachmentModel[84].setRotationPoint(56F, 10F, -1.5F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 447
		attachmentModel[85].setRotationPoint(55.5F, 10F, -0.5F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 448
		attachmentModel[86].setRotationPoint(53F, 10F, 0.5F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 449
		attachmentModel[87].setRotationPoint(56F, 10F, 0.5F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 450
		attachmentModel[88].setRotationPoint(47F, 10F, -1.5F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 451
		attachmentModel[89].setRotationPoint(50F, 10F, -1.5F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 452
		attachmentModel[90].setRotationPoint(49.5F, 10F, -0.5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 453
		attachmentModel[91].setRotationPoint(47F, 10F, 0.5F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 454
		attachmentModel[92].setRotationPoint(50F, 10F, 0.5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 455
		attachmentModel[93].setRotationPoint(41F, 10F, -1.5F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 456
		attachmentModel[94].setRotationPoint(44F, 10F, -1.5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 457
		attachmentModel[95].setRotationPoint(43.5F, 10F, -0.5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 458
		attachmentModel[96].setRotationPoint(41F, 10F, 0.5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 459
		attachmentModel[97].setRotationPoint(44F, 10F, 0.5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 460
		attachmentModel[98].setRotationPoint(35F, 10F, -1.5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 461
		attachmentModel[99].setRotationPoint(38F, 10F, -1.5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 462
		attachmentModel[100].setRotationPoint(37.5F, 10F, -0.5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 463
		attachmentModel[101].setRotationPoint(35F, 10F, 0.5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 464
		attachmentModel[102].setRotationPoint(38F, 10F, 0.5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 465
		attachmentModel[103].setRotationPoint(29F, 10F, -1.5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 466
		attachmentModel[104].setRotationPoint(32F, 10F, -1.5F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 467
		attachmentModel[105].setRotationPoint(31.5F, 10F, -0.5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 468
		attachmentModel[106].setRotationPoint(29F, 10F, 0.5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 469
		attachmentModel[107].setRotationPoint(32F, 10F, 0.5F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 470
		attachmentModel[108].setRotationPoint(23F, 10F, -1.5F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 471
		attachmentModel[109].setRotationPoint(26F, 10F, -1.5F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 472
		attachmentModel[110].setRotationPoint(25.5F, 10F, -0.5F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 473
		attachmentModel[111].setRotationPoint(23F, 10F, 0.5F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 474
		attachmentModel[112].setRotationPoint(26F, 10F, 0.5F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 475
		attachmentModel[113].setRotationPoint(17F, 10F, -1.5F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 476
		attachmentModel[114].setRotationPoint(20F, 10F, -1.5F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 477
		attachmentModel[115].setRotationPoint(19.5F, 10F, -0.5F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 478
		attachmentModel[116].setRotationPoint(17F, 10F, 0.5F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 479
		attachmentModel[117].setRotationPoint(20F, 10F, 0.5F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 480
		attachmentModel[118].setRotationPoint(11F, 10F, -1.5F);

		attachmentModel[119].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 481
		attachmentModel[119].setRotationPoint(14F, 10F, -1.5F);

		attachmentModel[120].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 482
		attachmentModel[120].setRotationPoint(13.5F, 10F, -0.5F);

		attachmentModel[121].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 483
		attachmentModel[121].setRotationPoint(11F, 10F, 0.5F);

		attachmentModel[122].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F); // Box 484
		attachmentModel[122].setRotationPoint(14F, 10F, 0.5F);

		attachmentModel[123].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 490
		attachmentModel[123].setRotationPoint(0F, 10F, -1.5F);

		attachmentModel[124].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 492
		attachmentModel[124].setRotationPoint(0.5F, 10F, -0.5F);

		attachmentModel[125].addShapeBox(0F, 0F, 0F, 5, 1, 7, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 495
		attachmentModel[125].setRotationPoint(0F, 11F, -3.5F);

		attachmentModel[126].addShapeBox(0F, 0F, 0F, 73, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 498
		attachmentModel[126].setRotationPoint(16F, 1.5F, 5F);

		attachmentModel[127].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 499
		attachmentModel[127].setRotationPoint(77.5F, -2F, 5F);

		attachmentModel[128].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 500
		attachmentModel[128].setRotationPoint(80.5F, -1F, 5F);

		attachmentModel[129].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 501
		attachmentModel[129].setRotationPoint(80.5F, -0.5F, 5F);

		attachmentModel[130].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 502
		attachmentModel[130].setRotationPoint(80.5F, 0F, 5F);

		attachmentModel[131].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 503
		attachmentModel[131].setRotationPoint(77.5F, 1F, 5F);

		attachmentModel[132].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 504
		attachmentModel[132].setRotationPoint(71.5F, 1F, 5F);

		attachmentModel[133].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 505
		attachmentModel[133].setRotationPoint(74.5F, 0F, 5F);

		attachmentModel[134].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 506
		attachmentModel[134].setRotationPoint(74.5F, -0.5F, 5F);

		attachmentModel[135].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 507
		attachmentModel[135].setRotationPoint(74.5F, -1F, 5F);

		attachmentModel[136].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 508
		attachmentModel[136].setRotationPoint(71.5F, -2F, 5F);

		attachmentModel[137].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 509
		attachmentModel[137].setRotationPoint(65.5F, 1F, 5F);

		attachmentModel[138].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 510
		attachmentModel[138].setRotationPoint(68.5F, 0F, 5F);

		attachmentModel[139].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 511
		attachmentModel[139].setRotationPoint(68.5F, -0.5F, 5F);

		attachmentModel[140].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 512
		attachmentModel[140].setRotationPoint(68.5F, -1F, 5F);

		attachmentModel[141].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 513
		attachmentModel[141].setRotationPoint(65.5F, -2F, 5F);

		attachmentModel[142].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 514
		attachmentModel[142].setRotationPoint(59.5F, 1F, 5F);

		attachmentModel[143].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 515
		attachmentModel[143].setRotationPoint(62.5F, 0F, 5F);

		attachmentModel[144].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 516
		attachmentModel[144].setRotationPoint(62.5F, -0.5F, 5F);

		attachmentModel[145].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 517
		attachmentModel[145].setRotationPoint(62.5F, -1F, 5F);

		attachmentModel[146].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 518
		attachmentModel[146].setRotationPoint(59.5F, -2F, 5F);

		attachmentModel[147].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 519
		attachmentModel[147].setRotationPoint(53.5F, 1F, 5F);

		attachmentModel[148].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 520
		attachmentModel[148].setRotationPoint(56.5F, 0F, 5F);

		attachmentModel[149].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 521
		attachmentModel[149].setRotationPoint(56.5F, -0.5F, 5F);

		attachmentModel[150].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 522
		attachmentModel[150].setRotationPoint(56.5F, -1F, 5F);

		attachmentModel[151].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 523
		attachmentModel[151].setRotationPoint(53.5F, -2F, 5F);

		attachmentModel[152].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 524
		attachmentModel[152].setRotationPoint(47.5F, 1F, 5F);

		attachmentModel[153].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 525
		attachmentModel[153].setRotationPoint(50.5F, 0F, 5F);

		attachmentModel[154].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 526
		attachmentModel[154].setRotationPoint(50.5F, -0.5F, 5F);

		attachmentModel[155].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 527
		attachmentModel[155].setRotationPoint(50.5F, -1F, 5F);

		attachmentModel[156].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 528
		attachmentModel[156].setRotationPoint(47.5F, -2F, 5F);

		attachmentModel[157].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 529
		attachmentModel[157].setRotationPoint(41.5F, 1F, 5F);

		attachmentModel[158].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 530
		attachmentModel[158].setRotationPoint(44.5F, 0F, 5F);

		attachmentModel[159].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 531
		attachmentModel[159].setRotationPoint(44.5F, -0.5F, 5F);

		attachmentModel[160].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 532
		attachmentModel[160].setRotationPoint(44.5F, -1F, 5F);

		attachmentModel[161].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 533
		attachmentModel[161].setRotationPoint(41.5F, -2F, 5F);

		attachmentModel[162].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 534
		attachmentModel[162].setRotationPoint(35.5F, 1F, 5F);

		attachmentModel[163].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 535
		attachmentModel[163].setRotationPoint(38.5F, 0F, 5F);

		attachmentModel[164].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 536
		attachmentModel[164].setRotationPoint(38.5F, -0.5F, 5F);

		attachmentModel[165].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 537
		attachmentModel[165].setRotationPoint(38.5F, -1F, 5F);

		attachmentModel[166].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 538
		attachmentModel[166].setRotationPoint(35.5F, -2F, 5F);

		attachmentModel[167].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 539
		attachmentModel[167].setRotationPoint(29.5F, 1F, 5F);

		attachmentModel[168].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 540
		attachmentModel[168].setRotationPoint(32.5F, 0F, 5F);

		attachmentModel[169].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 541
		attachmentModel[169].setRotationPoint(32.5F, -0.5F, 5F);

		attachmentModel[170].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 542
		attachmentModel[170].setRotationPoint(32.5F, -1F, 5F);

		attachmentModel[171].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 543
		attachmentModel[171].setRotationPoint(29.5F, -2F, 5F);

		attachmentModel[172].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 544
		attachmentModel[172].setRotationPoint(23.5F, 1F, 5F);

		attachmentModel[173].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 545
		attachmentModel[173].setRotationPoint(26.5F, 0F, 5F);

		attachmentModel[174].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 546
		attachmentModel[174].setRotationPoint(26.5F, -0.5F, 5F);

		attachmentModel[175].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 547
		attachmentModel[175].setRotationPoint(26.5F, -1F, 5F);

		attachmentModel[176].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 548
		attachmentModel[176].setRotationPoint(23.5F, -2F, 5F);

		attachmentModel[177].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 555
		attachmentModel[177].setRotationPoint(86.5F, -0.5F, 5F);

		attachmentModel[178].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 556
		attachmentModel[178].setRotationPoint(86F, -1F, 5F);

		attachmentModel[179].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 557
		attachmentModel[179].setRotationPoint(83.5F, -2F, 5F);

		attachmentModel[180].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 559
		attachmentModel[180].setRotationPoint(83.5F, 1F, 5F);

		attachmentModel[181].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 560
		attachmentModel[181].setRotationPoint(86F, 0F, 5F);

		attachmentModel[182].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 561
		attachmentModel[182].setRotationPoint(16F, -1.5F, 5F);

		attachmentModel[183].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 562
		attachmentModel[183].setRotationPoint(16F, -0.5F, 5F);

		attachmentModel[184].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 563
		attachmentModel[184].setRotationPoint(16F, 0.5F, 5F);

		attachmentModel[185].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 564
		attachmentModel[185].setRotationPoint(83.5F, 8F, 5F);

		attachmentModel[186].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 565
		attachmentModel[186].setRotationPoint(86F, 7F, 5F);

		attachmentModel[187].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 566
		attachmentModel[187].setRotationPoint(86.5F, 6.5F, 5F);

		attachmentModel[188].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 567
		attachmentModel[188].setRotationPoint(86F, 6F, 5F);

		attachmentModel[189].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 568
		attachmentModel[189].setRotationPoint(83.5F, 5F, 5F);

		attachmentModel[190].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 569
		attachmentModel[190].setRotationPoint(77.5F, 5F, 5F);

		attachmentModel[191].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 570
		attachmentModel[191].setRotationPoint(80.5F, 6F, 5F);

		attachmentModel[192].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 571
		attachmentModel[192].setRotationPoint(80.5F, 6.5F, 5F);

		attachmentModel[193].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 572
		attachmentModel[193].setRotationPoint(80.5F, 7F, 5F);

		attachmentModel[194].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 573
		attachmentModel[194].setRotationPoint(77.5F, 8F, 5F);

		attachmentModel[195].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 574
		attachmentModel[195].setRotationPoint(71.5F, 5F, 5F);

		attachmentModel[196].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 575
		attachmentModel[196].setRotationPoint(74.5F, 6F, 5F);

		attachmentModel[197].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 576
		attachmentModel[197].setRotationPoint(74.5F, 6.5F, 5F);

		attachmentModel[198].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 577
		attachmentModel[198].setRotationPoint(74.5F, 7F, 5F);

		attachmentModel[199].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 578
		attachmentModel[199].setRotationPoint(71.5F, 8F, 5F);

		attachmentModel[200].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 579
		attachmentModel[200].setRotationPoint(65.5F, 5F, 5F);

		attachmentModel[201].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 580
		attachmentModel[201].setRotationPoint(68.5F, 6F, 5F);

		attachmentModel[202].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 581
		attachmentModel[202].setRotationPoint(68.5F, 6.5F, 5F);

		attachmentModel[203].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 582
		attachmentModel[203].setRotationPoint(68.5F, 7F, 5F);

		attachmentModel[204].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 583
		attachmentModel[204].setRotationPoint(65.5F, 8F, 5F);

		attachmentModel[205].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 584
		attachmentModel[205].setRotationPoint(59.5F, 5F, 5F);

		attachmentModel[206].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 585
		attachmentModel[206].setRotationPoint(62.5F, 6F, 5F);

		attachmentModel[207].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 586
		attachmentModel[207].setRotationPoint(62.5F, 6.5F, 5F);

		attachmentModel[208].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 587
		attachmentModel[208].setRotationPoint(62.5F, 7F, 5F);

		attachmentModel[209].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 588
		attachmentModel[209].setRotationPoint(59.5F, 8F, 5F);

		attachmentModel[210].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 589
		attachmentModel[210].setRotationPoint(16F, 5.5F, 5F);

		attachmentModel[211].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 590
		attachmentModel[211].setRotationPoint(16F, 6.5F, 5F);

		attachmentModel[212].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 591
		attachmentModel[212].setRotationPoint(16F, 7.5F, 5F);

		attachmentModel[213].addShapeBox(0F, 0F, 0F, 73, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 592
		attachmentModel[213].setRotationPoint(16F, 9F, 5F);

		attachmentModel[214].addShapeBox(0F, 0F, 0F, 8, 1, 6, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 593
		attachmentModel[214].setRotationPoint(0F, 6F, -3F);

		attachmentModel[215].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -3F, 0F, 0F, -3F, 0F); // Box 594
		attachmentModel[215].setRotationPoint(0F, 6F, 2F);

		attachmentModel[216].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 595
		attachmentModel[216].setRotationPoint(0F, 6F, -6F);

		attachmentModel[217].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 596
		attachmentModel[217].setRotationPoint(10F, 6F, -6F);

		attachmentModel[218].addShapeBox(0F, 0F, 0F, 5, 1, 6, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 597
		attachmentModel[218].setRotationPoint(10F, 6F, -3F);

		attachmentModel[219].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -3F, 0F, 0F, -3F, 0F); // Box 598
		attachmentModel[219].setRotationPoint(10F, 6F, 2F);

		attachmentModel[220].addShapeBox(0F, 0F, 0F, 2, 2, 14, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 599
		attachmentModel[220].setRotationPoint(8F, 5F, -7F);

		attachmentModel[221].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 600
		attachmentModel[221].setRotationPoint(9F, 6F, -8F);
		attachmentModel[221].rotateAngleZ = -0.78539816F;

		attachmentModel[222].addShapeBox(-1F, -1F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 601
		attachmentModel[222].setRotationPoint(9F, 6F, 7F);
		attachmentModel[222].rotateAngleZ = -0.78539816F;

		attachmentModel[223].addShapeBox(0F, 0F, 0F, 9, 15, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 602
		attachmentModel[223].setRotationPoint(6F, -6F, -7F);

		attachmentModel[224].addShapeBox(0F, 0F, 0F, 15, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F); // Box 603
		attachmentModel[224].setRotationPoint(0F, 9F, -7F);

		attachmentModel[225].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 604
		attachmentModel[225].setRotationPoint(6F, -7F, -7F);

		attachmentModel[226].addShapeBox(0F, 0F, 0F, 52, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 605
		attachmentModel[226].setRotationPoint(6F, -9F, -6F);

		attachmentModel[227].addShapeBox(0F, 0F, 0F, 42, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 606
		attachmentModel[227].setRotationPoint(16F, -7F, -6F);

		attachmentModel[228].addShapeBox(0F, 0F, 0F, 1, 15, 1, 0F, 0F, 0F, 0F, 0F, 1F, -1F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -1F, 0F, 1F, 1F, 0F, 0F, 0F); // Box 607
		attachmentModel[228].setRotationPoint(15F, -6F, -7F);

		attachmentModel[229].addShapeBox(0F, 0F, 0F, 73, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 608
		attachmentModel[229].setRotationPoint(16F, 9F, -6F);

		attachmentModel[230].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 609
		attachmentModel[230].setRotationPoint(16F, 7.5F, -6F);

		attachmentModel[231].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 610
		attachmentModel[231].setRotationPoint(16F, 6.5F, -6F);

		attachmentModel[232].addShapeBox(0F, 0F, 0F, 41, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 611
		attachmentModel[232].setRotationPoint(16F, 5.5F, -6F);

		attachmentModel[233].addShapeBox(0F, 0F, 0F, 73, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 612
		attachmentModel[233].setRotationPoint(16F, 1.5F, -6F);

		attachmentModel[234].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 613
		attachmentModel[234].setRotationPoint(59.5F, 5F, -6F);

		attachmentModel[235].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 614
		attachmentModel[235].setRotationPoint(62.5F, 6F, -6F);

		attachmentModel[236].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 615
		attachmentModel[236].setRotationPoint(62.5F, 6.5F, -6F);

		attachmentModel[237].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 616
		attachmentModel[237].setRotationPoint(59.5F, 8F, -6F);

		attachmentModel[238].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 617
		attachmentModel[238].setRotationPoint(62.5F, 7F, -6F);

		attachmentModel[239].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 618
		attachmentModel[239].setRotationPoint(65.5F, 5F, -6F);

		attachmentModel[240].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 619
		attachmentModel[240].setRotationPoint(68.5F, 6F, -6F);

		attachmentModel[241].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 620
		attachmentModel[241].setRotationPoint(68.5F, 6.5F, -6F);

		attachmentModel[242].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 621
		attachmentModel[242].setRotationPoint(68.5F, 7F, -6F);

		attachmentModel[243].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 622
		attachmentModel[243].setRotationPoint(65.5F, 8F, -6F);

		attachmentModel[244].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 623
		attachmentModel[244].setRotationPoint(71.5F, 5F, -6F);

		attachmentModel[245].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 624
		attachmentModel[245].setRotationPoint(74.5F, 6F, -6F);

		attachmentModel[246].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 625
		attachmentModel[246].setRotationPoint(74.5F, 6.5F, -6F);

		attachmentModel[247].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 626
		attachmentModel[247].setRotationPoint(74.5F, 7F, -6F);

		attachmentModel[248].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 627
		attachmentModel[248].setRotationPoint(71.5F, 8F, -6F);

		attachmentModel[249].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 628
		attachmentModel[249].setRotationPoint(77.5F, 5F, -6F);

		attachmentModel[250].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 629
		attachmentModel[250].setRotationPoint(80.5F, 6F, -6F);

		attachmentModel[251].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 630
		attachmentModel[251].setRotationPoint(80.5F, 6.5F, -6F);

		attachmentModel[252].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 631
		attachmentModel[252].setRotationPoint(80.5F, 7F, -6F);

		attachmentModel[253].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 632
		attachmentModel[253].setRotationPoint(77.5F, 8F, -6F);

		attachmentModel[254].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 633
		attachmentModel[254].setRotationPoint(83.5F, 5F, -6F);

		attachmentModel[255].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 634
		attachmentModel[255].setRotationPoint(86F, 6F, -6F);

		attachmentModel[256].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 635
		attachmentModel[256].setRotationPoint(86.5F, 6.5F, -6F);

		attachmentModel[257].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 636
		attachmentModel[257].setRotationPoint(86F, 7F, -6F);

		attachmentModel[258].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 637
		attachmentModel[258].setRotationPoint(83.5F, 8F, -6F);

		attachmentModel[259].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 638
		attachmentModel[259].setRotationPoint(83.5F, 1F, -6F);

		attachmentModel[260].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 639
		attachmentModel[260].setRotationPoint(86F, 0F, -6F);

		attachmentModel[261].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 640
		attachmentModel[261].setRotationPoint(86.5F, -0.5F, -6F);

		attachmentModel[262].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F); // Box 641
		attachmentModel[262].setRotationPoint(86F, -1F, -6F);

		attachmentModel[263].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 642
		attachmentModel[263].setRotationPoint(83.5F, -2F, -6F);

		attachmentModel[264].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 643
		attachmentModel[264].setRotationPoint(77.5F, -2F, -6F);

		attachmentModel[265].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 644
		attachmentModel[265].setRotationPoint(80.5F, -1F, -6F);

		attachmentModel[266].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 645
		attachmentModel[266].setRotationPoint(80.5F, -0.5F, -6F);

		attachmentModel[267].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 646
		attachmentModel[267].setRotationPoint(80.5F, 0F, -6F);

		attachmentModel[268].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 647
		attachmentModel[268].setRotationPoint(77.5F, 1F, -6F);

		attachmentModel[269].addShapeBox(0F, 0F, 0F, 73, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 648
		attachmentModel[269].setRotationPoint(16F, -3F, -6F);

		attachmentModel[270].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 649
		attachmentModel[270].setRotationPoint(16F, -1.5F, -6F);

		attachmentModel[271].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 650
		attachmentModel[271].setRotationPoint(16F, -0.5F, -6F);

		attachmentModel[272].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 651
		attachmentModel[272].setRotationPoint(16F, 0.5F, -6F);

		attachmentModel[273].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 652
		attachmentModel[273].setRotationPoint(71.5F, 1F, -6F);

		attachmentModel[274].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 653
		attachmentModel[274].setRotationPoint(74.5F, 0F, -6F);

		attachmentModel[275].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 654
		attachmentModel[275].setRotationPoint(74.5F, -0.5F, -6F);

		attachmentModel[276].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 655
		attachmentModel[276].setRotationPoint(74.5F, -1F, -6F);

		attachmentModel[277].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 656
		attachmentModel[277].setRotationPoint(71.5F, -2F, -6F);

		attachmentModel[278].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 657
		attachmentModel[278].setRotationPoint(65.5F, 1F, -6F);

		attachmentModel[279].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 658
		attachmentModel[279].setRotationPoint(68.5F, 0F, -6F);

		attachmentModel[280].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 659
		attachmentModel[280].setRotationPoint(68.5F, -0.5F, -6F);

		attachmentModel[281].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 660
		attachmentModel[281].setRotationPoint(68.5F, -1F, -6F);

		attachmentModel[282].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 661
		attachmentModel[282].setRotationPoint(65.5F, -2F, -6F);

		attachmentModel[283].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 662
		attachmentModel[283].setRotationPoint(59.5F, 1F, -6F);

		attachmentModel[284].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 663
		attachmentModel[284].setRotationPoint(62.5F, 0F, -6F);

		attachmentModel[285].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 664
		attachmentModel[285].setRotationPoint(62.5F, -0.5F, -6F);

		attachmentModel[286].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 665
		attachmentModel[286].setRotationPoint(62.5F, -1F, -6F);

		attachmentModel[287].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 666
		attachmentModel[287].setRotationPoint(59.5F, -2F, -6F);

		attachmentModel[288].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 667
		attachmentModel[288].setRotationPoint(53.5F, 1F, -6F);

		attachmentModel[289].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 668
		attachmentModel[289].setRotationPoint(56.5F, 0F, -6F);

		attachmentModel[290].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 669
		attachmentModel[290].setRotationPoint(56.5F, -0.5F, -6F);

		attachmentModel[291].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 670
		attachmentModel[291].setRotationPoint(56.5F, -1F, -6F);

		attachmentModel[292].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 671
		attachmentModel[292].setRotationPoint(53.5F, -2F, -6F);

		attachmentModel[293].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 672
		attachmentModel[293].setRotationPoint(47.5F, 1F, -6F);

		attachmentModel[294].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 673
		attachmentModel[294].setRotationPoint(50.5F, 0F, -6F);

		attachmentModel[295].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 674
		attachmentModel[295].setRotationPoint(50.5F, -0.5F, -6F);

		attachmentModel[296].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 675
		attachmentModel[296].setRotationPoint(50.5F, -1F, -6F);

		attachmentModel[297].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 676
		attachmentModel[297].setRotationPoint(47.5F, -2F, -6F);

		attachmentModel[298].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 677
		attachmentModel[298].setRotationPoint(41.5F, 1F, -6F);

		attachmentModel[299].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 678
		attachmentModel[299].setRotationPoint(44.5F, 0F, -6F);

		attachmentModel[300].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 679
		attachmentModel[300].setRotationPoint(44.5F, -0.5F, -6F);

		attachmentModel[301].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 680
		attachmentModel[301].setRotationPoint(44.5F, -1F, -6F);

		attachmentModel[302].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 681
		attachmentModel[302].setRotationPoint(41.5F, -2F, -6F);

		attachmentModel[303].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 682
		attachmentModel[303].setRotationPoint(35.5F, 1F, -6F);

		attachmentModel[304].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 683
		attachmentModel[304].setRotationPoint(38.5F, 0F, -6F);

		attachmentModel[305].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 684
		attachmentModel[305].setRotationPoint(38.5F, -0.5F, -6F);

		attachmentModel[306].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 685
		attachmentModel[306].setRotationPoint(38.5F, -1F, -6F);

		attachmentModel[307].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 686
		attachmentModel[307].setRotationPoint(35.5F, -2F, -6F);

		attachmentModel[308].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 687
		attachmentModel[308].setRotationPoint(29.5F, 1F, -6F);

		attachmentModel[309].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 688
		attachmentModel[309].setRotationPoint(32.5F, 0F, -6F);

		attachmentModel[310].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 689
		attachmentModel[310].setRotationPoint(32.5F, -0.5F, -6F);

		attachmentModel[311].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 690
		attachmentModel[311].setRotationPoint(32.5F, -1F, -6F);

		attachmentModel[312].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 691
		attachmentModel[312].setRotationPoint(29.5F, -2F, -6F);

		attachmentModel[313].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 692
		attachmentModel[313].setRotationPoint(23.5F, 1F, -6F);

		attachmentModel[314].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 693
		attachmentModel[314].setRotationPoint(26.5F, 0F, -6F);

		attachmentModel[315].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 694
		attachmentModel[315].setRotationPoint(26.5F, -0.5F, -6F);

		attachmentModel[316].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 695
		attachmentModel[316].setRotationPoint(26.5F, -1F, -6F);

		attachmentModel[317].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 696
		attachmentModel[317].setRotationPoint(23.5F, -2F, -6F);

		attachmentModel[318].addShapeBox(0F, 0F, 0F, 15, 1, 3, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F); // Box 702
		attachmentModel[318].setRotationPoint(0F, -6F, 3F);

		attachmentModel[319].addShapeBox(0F, 0F, 0F, 6, 11, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 391
		attachmentModel[319].setRotationPoint(0F, -2F, 6F);

		attachmentModel[320].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 392
		attachmentModel[320].setRotationPoint(0F, -8F, 4F);

		attachmentModel[321].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 2F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, -2F, 0F, -2F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -2F); // Box 393
		attachmentModel[321].setRotationPoint(3F, -6F, 6F);

		attachmentModel[322].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, -1.5F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, -2F, 1F); // Box 394
		attachmentModel[322].setRotationPoint(3F, -9F, 3F);

		attachmentModel[323].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 2F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 2F, 0F, -2F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 2F); // Box 395
		attachmentModel[323].setRotationPoint(3F, -6F, -7F);

		attachmentModel[324].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 396
		attachmentModel[324].setRotationPoint(0F, -8F, -5F);

		attachmentModel[325].addShapeBox(0F, 0F, 0F, 15, 1, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 397
		attachmentModel[325].setRotationPoint(0F, -6F, -6F);

		attachmentModel[326].addShapeBox(0F, 0F, 0F, 6, 11, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 398
		attachmentModel[326].setRotationPoint(0F, -2F, -7F);

		attachmentModel[327].addShapeBox(0F, 0F, 0F, 4, 1, 7, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 399
		attachmentModel[327].setRotationPoint(0F, 12F, -3.5F);

		attachmentModel[328].addShapeBox(0F, 0F, 0F, 9, 1, 7, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.625F, 0F, 0F, 0.625F, 0F, 0F, 0.5F, 0F, 0F); // Box 400
		attachmentModel[328].setRotationPoint(-6F, 13F, -3.5F);

		attachmentModel[329].addShapeBox(0F, 0F, 0F, 6, 7, 7, 0F, 2.5F, 0F, 0F, 1.625F, 0F, 0F, 1.625F, 0F, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F); // Box 401
		attachmentModel[329].setRotationPoint(-4F, 14F, -3.5F);

		attachmentModel[330].addShapeBox(0F, 0F, 0F, 1, 28, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, 0F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, 0F, 0F); // Box 402
		attachmentModel[330].setRotationPoint(-2F, 21F, -3.5F);

		attachmentModel[331].addShapeBox(0F, 0F, 0F, 1, 28, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, 0F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, 0F, 0F); // Box 403
		attachmentModel[331].setRotationPoint(0F, 21F, -3.5F);

		attachmentModel[332].addShapeBox(0F, 0F, 0F, 1, 3, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, -1.75F, 0.5F, 0F, -1.75F, 0.5F, 0F, 1.5F, 0F, 0F); // Box 404
		attachmentModel[332].setRotationPoint(-1F, 21F, -3.5F);

		attachmentModel[333].addShapeBox(0F, 0F, 0F, 1, 28, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, -0.5F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, -0.5F, 0F); // Box 405
		attachmentModel[333].setRotationPoint(-1F, 21F, -0.5F);

		attachmentModel[334].addShapeBox(0F, 0F, 0F, 1, 4, 7, 0F, -2.25F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2.25F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 406
		attachmentModel[334].setRotationPoint(-15F, 45F, -3.5F);

		attachmentModel[335].addShapeBox(0F, 0F, 0F, 21, 2, 7, 0F, -7.5F, 0F, 0F, 1F, -3F, 0F, 1F, -3F, 0F, -7.5F, 0F, 0F, 3F, -2F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 3F, -2F, 0F); // Box 407
		attachmentModel[335].setRotationPoint(-37F, 44F, -3.5F);

		attachmentModel[336].addShapeBox(0F, 0F, 0F, 3, 3, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 2F); // Box 408
		attachmentModel[336].setRotationPoint(-29.5F, 41F, -1.5F);

		attachmentModel[337].addShapeBox(0F, 0F, 0F, 10, 3, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 409
		attachmentModel[337].setRotationPoint(-39.5F, 41F, -3.5F);

		attachmentModel[338].addShapeBox(0F, 0F, 0F, 9, 9, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 3F, 0F, 0F); // Box 410
		attachmentModel[338].setRotationPoint(-36.5F, 32F, -3.5F);

		attachmentModel[339].addShapeBox(0F, 0F, 0F, 8, 9, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 4F, 0F, 0F); // Box 411
		attachmentModel[339].setRotationPoint(-32.5F, 23F, -3.5F);

		attachmentModel[340].addShapeBox(0F, 0F, 0F, 7, 6, 7, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 412
		attachmentModel[340].setRotationPoint(-31.5F, 17F, -3.5F);

		attachmentModel[341].addShapeBox(0F, 0F, 0F, 4, 5, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 413
		attachmentModel[341].setRotationPoint(-24.5F, 17F, -3.5F);

		attachmentModel[342].addShapeBox(0F, 0F, 0F, 8, 4, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 414
		attachmentModel[342].setRotationPoint(-29.5F, 13F, -3.5F);

		attachmentModel[343].addShapeBox(0F, 0F, 0F, 4, 1, 5, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 415
		attachmentModel[343].setRotationPoint(-24.5F, 22F, -2.5F);

		attachmentModel[344].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F); // Box 416
		attachmentModel[344].setRotationPoint(-24.5F, 22F, -1.5F);

		attachmentModel[345].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 417
		attachmentModel[345].setRotationPoint(-23.5F, 23F, -1.5F);

		attachmentModel[346].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 2F, 0F, 2F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 2F); // Box 418
		attachmentModel[346].setRotationPoint(-27.5F, 32F, -1.5F);

		attachmentModel[347].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 3F, 0F, 2F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 2F); // Box 419
		attachmentModel[347].setRotationPoint(-24.5F, 23F, -1.5F);

		attachmentModel[348].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 4F, -2F, 0F, -4F, 0F, 2F, -4F, 0F, 2F, 4F, -2F, 0F); // Box 420
		attachmentModel[348].setRotationPoint(-35.5F, 23F, -1.5F);

		attachmentModel[349].addShapeBox(0F, 0F, 0F, 2, 6, 3, 0F, 0F, 0.5F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0.5F, 0F, 4F, 0F, 0F, -3F, 0F, 2F, -3F, 0F, 2F, 4F, 0F, 0F); // Box 421
		attachmentModel[349].setRotationPoint(-31.5F, 17F, -1.5F);

		attachmentModel[350].addShapeBox(0F, 0F, 0F, 2, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F); // Box 422
		attachmentModel[350].setRotationPoint(-31.5F, 13F, -1.5F);

		attachmentModel[351].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 2F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 2F, 0F, 4F, 0F, 0F, -3F, 0F, 2F, -3F, 0F, 2F, 4F, 0F, 0F); // Box 423
		attachmentModel[351].setRotationPoint(-39.5F, 32F, -1.5F);

		attachmentModel[352].addShapeBox(0F, 0F, 0F, 4, 2, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1F, 2F, -0.5F, 1F, 2F, 0F, 0F, 0F); // Box 424
		attachmentModel[352].setRotationPoint(-43.5F, 41F, -1.5F);

		attachmentModel[353].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[353].setRotationPoint(0F, 10F, 0.5F);

		attachmentModel[354].addShapeBox(0F, 0F, 0F, 1, 3, 7, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 426
		attachmentModel[354].setRotationPoint(-21.5F, 14F, -3.5F);

		attachmentModel[355].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, -2F, 1F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, -1.5F, -1.5F); // Box 420
		attachmentModel[355].setRotationPoint(3F, -9F, -4F);

		flipAll();
	}
}