//This File was created with the Minecraft-SMP Modelling Toolbox *******
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICWTS36RearGrip
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36RearGrip extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelCICWTS36RearGrip() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[34];
		attachmentModel[0] = new ModelRendererTurbo(this, 153, 57, textureX, textureY); // Box 168
		attachmentModel[1] = new ModelRendererTurbo(this, 473, 57, textureX, textureY); // Box 169
		attachmentModel[2] = new ModelRendererTurbo(this, 393, 65, textureX, textureY); // Box 170
		attachmentModel[3] = new ModelRendererTurbo(this, 425, 65, textureX, textureY); // Box 171
		attachmentModel[4] = new ModelRendererTurbo(this, 233, 73, textureX, textureY); // Box 172
		attachmentModel[5] = new ModelRendererTurbo(this, 273, 73, textureX, textureY); // Box 173
		attachmentModel[6] = new ModelRendererTurbo(this, 305, 73, textureX, textureY); // Box 176
		attachmentModel[7] = new ModelRendererTurbo(this, 321, 73, textureX, textureY); // Box 177
		attachmentModel[8] = new ModelRendererTurbo(this, 337, 73, textureX, textureY); // Box 178
		attachmentModel[9] = new ModelRendererTurbo(this, 473, 49, textureX, textureY); // Box 179
		attachmentModel[10] = new ModelRendererTurbo(this, 353, 73, textureX, textureY); // Box 182
		attachmentModel[11] = new ModelRendererTurbo(this, 369, 73, textureX, textureY); // Box 183
		attachmentModel[12] = new ModelRendererTurbo(this, 225, 57, textureX, textureY); // Box 185
		attachmentModel[13] = new ModelRendererTurbo(this, 457, 25, textureX, textureY); // Box 186
		attachmentModel[14] = new ModelRendererTurbo(this, 241, 57, textureX, textureY); // Box 187
		attachmentModel[15] = new ModelRendererTurbo(this, 313, 105, textureX, textureY); // Box 309
		attachmentModel[16] = new ModelRendererTurbo(this, 337, 105, textureX, textureY); // Box 310
		attachmentModel[17] = new ModelRendererTurbo(this, 361, 113, textureX, textureY); // Box 311
		attachmentModel[18] = new ModelRendererTurbo(this, 169, 113, textureX, textureY); // Box 312
		attachmentModel[19] = new ModelRendererTurbo(this, 329, 105, textureX, textureY); // Box 383
		attachmentModel[20] = new ModelRendererTurbo(this, 425, 113, textureX, textureY); // Box 385
		attachmentModel[21] = new ModelRendererTurbo(this, 449, 113, textureX, textureY); // Box 386
		attachmentModel[22] = new ModelRendererTurbo(this, 481, 113, textureX, textureY); // Box 387
		attachmentModel[23] = new ModelRendererTurbo(this, 137, 121, textureX, textureY); // Box 388
		attachmentModel[24] = new ModelRendererTurbo(this, 297, 113, textureX, textureY); // Box 389
		attachmentModel[25] = new ModelRendererTurbo(this, 393, 97, textureX, textureY); // Box 390
		attachmentModel[26] = new ModelRendererTurbo(this, 481, 129, textureX, textureY); // Box 410
		attachmentModel[27] = new ModelRendererTurbo(this, 185, 145, textureX, textureY); // Box 474
		attachmentModel[28] = new ModelRendererTurbo(this, 201, 145, textureX, textureY); // Box 475
		attachmentModel[29] = new ModelRendererTurbo(this, 217, 145, textureX, textureY); // Box 476
		attachmentModel[30] = new ModelRendererTurbo(this, 225, 145, textureX, textureY); // Box 477
		attachmentModel[31] = new ModelRendererTurbo(this, 393, 145, textureX, textureY); // Box 478
		attachmentModel[32] = new ModelRendererTurbo(this, 425, 97, textureX, textureY); // Box 467
		attachmentModel[33] = new ModelRendererTurbo(this, 100, 226, textureX, textureY); // Box 433

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 2, 4, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F); // Box 168
		attachmentModel[0].setRotationPoint(-11F, 0F, -1.5F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 7, 6, 7, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 169
		attachmentModel[1].setRotationPoint(-11F, 4F, -3.5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 8, 9, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 4F, 0F, 0F); // Box 170
		attachmentModel[2].setRotationPoint(-12F, 10F, -3.5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 9, 9, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 3F, 0F, 0F); // Box 171
		attachmentModel[3].setRotationPoint(-16F, 19F, -3.5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 10, 3, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 172
		attachmentModel[4].setRotationPoint(-19F, 28F, -3.5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 8, 4, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 173
		attachmentModel[5].setRotationPoint(-9F, 0F, -3.5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 6, 3, 0F, 0F, 0.5F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0.5F, 0F, 4F, 0F, 0F, -3F, 0F, 2F, -3F, 0F, 2F, 4F, 0F, 0F); // Box 176
		attachmentModel[6].setRotationPoint(-11F, 4F, -1.5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 4F, -2F, 0F, -4F, 0F, 2F, -4F, 0F, 2F, 4F, -2F, 0F); // Box 177
		attachmentModel[7].setRotationPoint(-15F, 10F, -1.5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 2F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 2F, 0F, 4F, 0F, 0F, -3F, 0F, 2F, -3F, 0F, 2F, 4F, 0F, 0F); // Box 178
		attachmentModel[8].setRotationPoint(-19F, 19F, -1.5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 4, 2, 3, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1F, 2F, -0.5F, 1F, 2F, 0F, 0F, 0F); // Box 179
		attachmentModel[9].setRotationPoint(-23F, 28F, -1.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 3F, 0F, 2F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 2F); // Box 182
		attachmentModel[10].setRotationPoint(-4F, 10F, -1.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 3, 9, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 2F, 0F, 2F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 2F); // Box 183
		attachmentModel[11].setRotationPoint(-7F, 19F, -1.5F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 3, 3, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 2F); // Box 185
		attachmentModel[12].setRotationPoint(-9F, 28F, -1.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 186
		attachmentModel[13].setRotationPoint(-3F, 10F, -1.5F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 187
		attachmentModel[14].setRotationPoint(20.5F, -6F, -4F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 28, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, 0F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, 0F, 0F); // Box 309
		attachmentModel[15].setRotationPoint(18.5F, 8F, -3.5F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 28, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, 0F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, 0F, 0F); // Box 310
		attachmentModel[16].setRotationPoint(20.5F, 8F, -3.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 21, 2, 7, 0F, -8.5F, 0F, 0F, 2F, -3F, 0F, 2F, -3F, 0F, -8.5F, 0F, 0F, 2F, -2F, 0F, 1F, 3F, 0F, 1F, 3F, 0F, 2F, -2F, 0F); // Box 311
		attachmentModel[17].setRotationPoint(-17.5F, 31F, -3.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 6, 1, 8, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 312
		attachmentModel[18].setRotationPoint(20.5F, -4F, -4F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 383
		attachmentModel[19].setRotationPoint(20.5F, -6F, 3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 4, 2, 7, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 385
		attachmentModel[20].setRotationPoint(20.5F, -3F, -3.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 6, 7, 7, 0F, 2.5F, 0F, 0F, 1.625F, 0F, 0F, 1.625F, 0F, 0F, 2.5F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F); // Box 386
		attachmentModel[21].setRotationPoint(16.5F, 1F, -3.5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 4, 7, 0F, -2.25F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2.25F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 387
		attachmentModel[22].setRotationPoint(5.5F, 32F, -3.5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 28, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 14F, -0.5F, 0F, -14F, 0F, 0F, -14F, 0F, 0F, 14F, -0.5F, 0F); // Box 388
		attachmentModel[23].setRotationPoint(19.5F, 8F, -0.5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 389
		attachmentModel[24].setRotationPoint(22F, -5F, 3.5F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 390
		attachmentModel[25].setRotationPoint(23F, -6F, 4F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 3, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, -1.75F, 0.5F, 0F, -1.75F, 0.5F, 0F, 1.5F, 0F, 0F); // Box 410
		attachmentModel[26].setRotationPoint(19.5F, 8F, -3.5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 4, 5, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 474
		attachmentModel[27].setRotationPoint(-4F, 4F, -3.5F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 4, 1, 5, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 475
		attachmentModel[28].setRotationPoint(-4F, 9F, -2.5F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F); // Box 476
		attachmentModel[29].setRotationPoint(-4F, 9F, -1.5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 4, 1, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 477
		attachmentModel[30].setRotationPoint(20.5F, -1F, -3.5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 9, 1, 7, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.625F, 0F, 0F, 0.625F, 0F, 0F, 0.5F, 0F, 0F); // Box 478
		attachmentModel[31].setRotationPoint(14.5F, 0F, -3.5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 467
		attachmentModel[32].setRotationPoint(23F, -4F, 4F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 3, 7, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 433
		attachmentModel[33].setRotationPoint(-1F, 1F, -3.5F);
	}
}