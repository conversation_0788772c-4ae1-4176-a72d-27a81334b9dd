//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICWTS36UpperPicatinnyRail
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36UpperPicatinnyRail extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelCICWTS36UpperPicatinnyRail() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[44];
		attachmentModel[0] = new ModelRendererTurbo(this, 481, 1, textureX, textureY); // Box 8
		attachmentModel[1] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 9
		attachmentModel[2] = new ModelRendererTurbo(this, 201, 17, textureX, textureY); // Box 13
		attachmentModel[3] = new ModelRendererTurbo(this, 297, 17, textureX, textureY); // Box 14
		attachmentModel[4] = new ModelRendererTurbo(this, 497, 1, textureX, textureY); // Box 15
		attachmentModel[5] = new ModelRendererTurbo(this, 481, 9, textureX, textureY); // Box 16
		attachmentModel[6] = new ModelRendererTurbo(this, 497, 9, textureX, textureY); // Box 17
		attachmentModel[7] = new ModelRendererTurbo(this, 497, 17, textureX, textureY); // Box 18
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 19
		attachmentModel[9] = new ModelRendererTurbo(this, 97, 25, textureX, textureY); // Box 21
		attachmentModel[10] = new ModelRendererTurbo(this, 105, 25, textureX, textureY); // Box 22
		attachmentModel[11] = new ModelRendererTurbo(this, 129, 25, textureX, textureY); // Box 23
		attachmentModel[12] = new ModelRendererTurbo(this, 153, 25, textureX, textureY); // Box 24
		attachmentModel[13] = new ModelRendererTurbo(this, 177, 25, textureX, textureY); // Box 25
		attachmentModel[14] = new ModelRendererTurbo(this, 201, 25, textureX, textureY); // Box 26
		attachmentModel[15] = new ModelRendererTurbo(this, 225, 25, textureX, textureY); // Box 27
		attachmentModel[16] = new ModelRendererTurbo(this, 249, 25, textureX, textureY); // Box 28
		attachmentModel[17] = new ModelRendererTurbo(this, 273, 25, textureX, textureY); // Box 29
		attachmentModel[18] = new ModelRendererTurbo(this, 297, 25, textureX, textureY); // Box 30
		attachmentModel[19] = new ModelRendererTurbo(this, 321, 25, textureX, textureY); // Box 31
		attachmentModel[20] = new ModelRendererTurbo(this, 345, 25, textureX, textureY); // Box 32
		attachmentModel[21] = new ModelRendererTurbo(this, 369, 25, textureX, textureY); // Box 33
		attachmentModel[22] = new ModelRendererTurbo(this, 393, 25, textureX, textureY); // Box 34
		attachmentModel[23] = new ModelRendererTurbo(this, 417, 25, textureX, textureY); // Box 35
		attachmentModel[24] = new ModelRendererTurbo(this, 441, 25, textureX, textureY); // Box 36
		attachmentModel[25] = new ModelRendererTurbo(this, 465, 25, textureX, textureY); // Box 37
		attachmentModel[26] = new ModelRendererTurbo(this, 489, 25, textureX, textureY); // Box 38
		attachmentModel[27] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 39
		attachmentModel[28] = new ModelRendererTurbo(this, 25, 33, textureX, textureY); // Box 40
		attachmentModel[29] = new ModelRendererTurbo(this, 49, 33, textureX, textureY); // Box 41
		attachmentModel[30] = new ModelRendererTurbo(this, 73, 33, textureX, textureY); // Box 42
		attachmentModel[31] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 43
		attachmentModel[32] = new ModelRendererTurbo(this, 121, 33, textureX, textureY); // Box 44
		attachmentModel[33] = new ModelRendererTurbo(this, 145, 33, textureX, textureY); // Box 47
		attachmentModel[34] = new ModelRendererTurbo(this, 169, 33, textureX, textureY); // Box 48
		attachmentModel[35] = new ModelRendererTurbo(this, 145, 25, textureX, textureY); // Box 52
		attachmentModel[36] = new ModelRendererTurbo(this, 209, 81, textureX, textureY); // Box 248
		attachmentModel[37] = new ModelRendererTurbo(this, 193, 121, textureX, textureY); // Box 395
		attachmentModel[38] = new ModelRendererTurbo(this, 457, 169, textureX, textureY); // Box 401
		attachmentModel[39] = new ModelRendererTurbo(this, 489, 169, textureX, textureY); // Box 402
		attachmentModel[40] = new ModelRendererTurbo(this, 137, 185, textureX, textureY); // Box 421
		attachmentModel[41] = new ModelRendererTurbo(this, 425, 145, textureX, textureY); // Box 422
		attachmentModel[42] = new ModelRendererTurbo(this, 425, 153, textureX, textureY); // Box 423
		attachmentModel[43] = new ModelRendererTurbo(this, 17, 193, textureX, textureY); // Box 454

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 4, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 8
		attachmentModel[0].setRotationPoint(-6F, -8F, -1F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 96, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 9
		attachmentModel[1].setRotationPoint(-5F, -7F, -3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 44, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 13
		attachmentModel[2].setRotationPoint(-5F, -5F, -3F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 96, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 14
		attachmentModel[3].setRotationPoint(-5F, -7F, 1F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F); // Box 15
		attachmentModel[4].setRotationPoint(91F, -7F, -1F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 3, 3, 2, 0F, 0F, 0F, 0F, -1F, 0F, 2F, -1F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 2F, -1F, 0F, 2F, 0F, 0F, 0F); // Box 16
		attachmentModel[5].setRotationPoint(-7F, -7F, -1F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F); // Box 17
		attachmentModel[6].setRotationPoint(-5F, -8F, -3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 18
		attachmentModel[7].setRotationPoint(-5F, -8F, 1F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 44, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 19
		attachmentModel[8].setRotationPoint(-5F, -5F, 2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 21
		attachmentModel[9].setRotationPoint(47F, -3.5F, -0.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 22
		attachmentModel[10].setRotationPoint(0F, -8F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 23
		attachmentModel[11].setRotationPoint(8F, -8F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 24
		attachmentModel[12].setRotationPoint(12F, -8F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 25
		attachmentModel[13].setRotationPoint(16F, -8F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 26
		attachmentModel[14].setRotationPoint(20F, -8F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 27
		attachmentModel[15].setRotationPoint(24F, -8F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 28
		attachmentModel[16].setRotationPoint(28F, -8F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 29
		attachmentModel[17].setRotationPoint(32F, -8F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 30
		attachmentModel[18].setRotationPoint(36F, -8F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 31
		attachmentModel[19].setRotationPoint(40F, -8F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 32
		attachmentModel[20].setRotationPoint(44F, -8F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 33
		attachmentModel[21].setRotationPoint(48F, -8F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 34
		attachmentModel[22].setRotationPoint(52F, -8F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[23].setRotationPoint(56F, -8F, -3F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 36
		attachmentModel[24].setRotationPoint(60F, -8F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 37
		attachmentModel[25].setRotationPoint(64F, -8F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 38
		attachmentModel[26].setRotationPoint(68F, -8F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 39
		attachmentModel[27].setRotationPoint(72F, -8F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 40
		attachmentModel[28].setRotationPoint(76F, -8F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 41
		attachmentModel[29].setRotationPoint(80F, -8F, -3F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 42
		attachmentModel[30].setRotationPoint(84F, -8F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 43
		attachmentModel[31].setRotationPoint(88F, -8F, -3F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 10, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 44
		attachmentModel[32].setRotationPoint(37F, -5F, -1.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 10, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, 0.25F, 0F, 0F, 0.25F); // Box 47
		attachmentModel[33].setRotationPoint(-5F, -4F, 2F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 10, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, -0.25F, 0F, 0F, -0.25F); // Box 48
		attachmentModel[34].setRotationPoint(-5F, -4F, -3F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 52
		attachmentModel[35].setRotationPoint(92F, -8F, -1F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 10, 3, 1, 0F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 248
		attachmentModel[36].setRotationPoint(-5F, -3F, 2F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 395
		attachmentModel[37].setRotationPoint(4F, -8F, -3F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 13, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 401
		attachmentModel[38].setRotationPoint(37F, -1.5F, -0.5F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 402
		attachmentModel[39].setRotationPoint(47F, -2.5F, -1.5F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 10, 3, 1, 0F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, -0.25F, 0F, 0F, -0.25F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 421
		attachmentModel[40].setRotationPoint(-5F, -3F, -3F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 422
		attachmentModel[41].setRotationPoint(37F, -5F, -2F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 423
		attachmentModel[42].setRotationPoint(37F, -5F, 1F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 10, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 454
		attachmentModel[43].setRotationPoint(37F, -7F, -1F);

		flipAll();
	}
}