//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: Trijicon ACOG 4x32 TA31F
// Model Creator: 
// Created on: 09.11.2019 - 16:36:12
// Last changed on: 09.11.2019 - 16:36:12

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelTrijiconACOG4x32TA31FEPR extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelTrijiconACOG4x32TA31FEPR() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[17];
		attachmentModel[0] = new ModelRendererTurbo(this, 137, 65, textureX, textureY); // Box 143
		attachmentModel[1] = new ModelRendererTurbo(this, 105, 65, textureX, textureY); // Box 144
		attachmentModel[2] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 145
		attachmentModel[3] = new ModelRendererTurbo(this, 145, 65, textureX, textureY); // Box 146
		attachmentModel[4] = new ModelRendererTurbo(this, 153, 65, textureX, textureY); // Box 147
		attachmentModel[5] = new ModelRendererTurbo(this, 161, 65, textureX, textureY); // Box 148
		attachmentModel[6] = new ModelRendererTurbo(this, 161, 65, textureX, textureY); // Box 149
		attachmentModel[7] = new ModelRendererTurbo(this, 185, 65, textureX, textureY); // Box 150
		attachmentModel[8] = new ModelRendererTurbo(this, 209, 65, textureX, textureY); // Box 151
		attachmentModel[9] = new ModelRendererTurbo(this, 225, 65, textureX, textureY); // Box 152
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 153
		attachmentModel[11] = new ModelRendererTurbo(this, 49, 73, textureX, textureY); // Box 154
		attachmentModel[12] = new ModelRendererTurbo(this, 73, 73, textureX, textureY); // Box 155
		attachmentModel[13] = new ModelRendererTurbo(this, 97, 73, textureX, textureY); // Box 156
		attachmentModel[14] = new ModelRendererTurbo(this, 145, 73, textureX, textureY); // Box 157
		attachmentModel[15] = new ModelRendererTurbo(this, 177, 65, textureX, textureY); // Box 158
		attachmentModel[16] = new ModelRendererTurbo(this, 241, 65, textureX, textureY); // Box 159

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3.5F, 0.5F, 0F, 3.5F); // Box 143
		attachmentModel[0].setRotationPoint(-0.75F, 0F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0.25F, 0F, 0F, 0.25F, 0F, 0F, 0.25F, 0.5F, 0.5F, 0.25F, 0.5F); // Box 144
		attachmentModel[1].setRotationPoint(-0.75F, 3F, 5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.25F, 0.5F, 0F, 0.25F, 0.5F, 0F, 0.25F, 0F, 0.5F, 0.25F, 0F); // Box 145
		attachmentModel[2].setRotationPoint(-0.75F, 3F, -6F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 3.5F, 0F, 0F, 3.5F, 0F, 0F, -3F, 0.5F, 0F, -3F); // Box 146
		attachmentModel[3].setRotationPoint(-0.75F, 0F, -3F);

		attachmentModel[4].addShapeBox(-0.5F, 0F, -0.5F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 147
		attachmentModel[4].setRotationPoint(0F, 4.25F, 5.75F);
		attachmentModel[4].rotateAngleY = 0.78539816F;

		attachmentModel[5].addShapeBox(-0.5F, 0F, -0.5F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 148
		attachmentModel[5].setRotationPoint(0F, 4.25F, -5.75F);
		attachmentModel[5].rotateAngleY = 0.78539816F;

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 1, 7, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 149
		attachmentModel[6].setRotationPoint(-0.75F, -1F, -3.5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 7, 2, 6, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, -1F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0.5F, -1F, 0F); // Box 150
		attachmentModel[7].setRotationPoint(-2.25F, -2F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, -0.5F, 0F, 0F, 1F, 0F, 0F, 2F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, -1F, 0F, -1F, -1F, 0F, 0F, -1F, 0F, 0.5F, -1F, 0F); // Box 151
		attachmentModel[8].setRotationPoint(-2.25F, -2F, -4F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0.5F, 0F, 0F, 2F, 0F, 0F, 1F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, -1F, 0F, 0F, -1F, 0F, -1F, -1F, 0F, -0.5F, -1F, 0F); // Box 152
		attachmentModel[9].setRotationPoint(-2.25F, -2F, 3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 14, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 153
		attachmentModel[10].setRotationPoint(-2.75F, -3F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 154
		attachmentModel[11].setRotationPoint(-2.75F, -4F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 155
		attachmentModel[12].setRotationPoint(1.25F, -4F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 156
		attachmentModel[13].setRotationPoint(5.25F, -4F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 157
		attachmentModel[14].setRotationPoint(9.25F, -4F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 158
		attachmentModel[15].setRotationPoint(7.75F, -2F, 2F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 159
		attachmentModel[16].setRotationPoint(7.75F, -2F, -4F);

		flipAll();
	}
}