//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: AN/PEQ-15A
// Model Creator: 
// Created on: 09.06.2020 - 21:46:54
// Last changed on: 09.06.2020 - 21:46:54

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelANPEQ15A extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelANPEQ15A() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[109];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 0
		attachmentModel[1] = new ModelRendererTurbo(this, 17, 1, textureX, textureY); // Box 1
		attachmentModel[2] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 2
		attachmentModel[3] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 3
		attachmentModel[4] = new ModelRendererTurbo(this, 89, 1, textureX, textureY); // Box 4
		attachmentModel[5] = new ModelRendererTurbo(this, 129, 1, textureX, textureY); // Box 5
		attachmentModel[6] = new ModelRendererTurbo(this, 185, 1, textureX, textureY); // Box 6
		attachmentModel[7] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 7
		attachmentModel[8] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 8
		attachmentModel[9] = new ModelRendererTurbo(this, 89, 9, textureX, textureY); // Box 9
		attachmentModel[10] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 10
		attachmentModel[11] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 11
		attachmentModel[12] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 12
		attachmentModel[13] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 13
		attachmentModel[14] = new ModelRendererTurbo(this, 121, 9, textureX, textureY); // Box 14
		attachmentModel[15] = new ModelRendererTurbo(this, 185, 9, textureX, textureY); // Box 15
		attachmentModel[16] = new ModelRendererTurbo(this, 41, 17, textureX, textureY); // Box 16
		attachmentModel[17] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 17
		attachmentModel[18] = new ModelRendererTurbo(this, 81, 1, textureX, textureY); // Box 18
		attachmentModel[19] = new ModelRendererTurbo(this, 185, 17, textureX, textureY); // Box 19
		attachmentModel[20] = new ModelRendererTurbo(this, 177, 9, textureX, textureY); // Box 20
		attachmentModel[21] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 21
		attachmentModel[22] = new ModelRendererTurbo(this, 233, 9, textureX, textureY); // Box 22
		attachmentModel[23] = new ModelRendererTurbo(this, 241, 9, textureX, textureY); // Box 23
		attachmentModel[24] = new ModelRendererTurbo(this, 121, 1, textureX, textureY); // Box 24
		attachmentModel[25] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 25
		attachmentModel[26] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 26
		attachmentModel[27] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 27
		attachmentModel[28] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 28
		attachmentModel[29] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 29
		attachmentModel[30] = new ModelRendererTurbo(this, 241, 9, textureX, textureY); // Box 30
		attachmentModel[31] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 31
		attachmentModel[32] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 32
		attachmentModel[33] = new ModelRendererTurbo(this, 249, 17, textureX, textureY); // Box 33
		attachmentModel[34] = new ModelRendererTurbo(this, 129, 25, textureX, textureY); // Box 34
		attachmentModel[35] = new ModelRendererTurbo(this, 139, 25, textureX, textureY); // Box 35
		attachmentModel[36] = new ModelRendererTurbo(this, 161, 25, textureX, textureY); // Box 36
		attachmentModel[37] = new ModelRendererTurbo(this, 193, 25, textureX, textureY); // Box 37
		attachmentModel[38] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 38
		attachmentModel[39] = new ModelRendererTurbo(this, 225, 25, textureX, textureY); // Box 39
		attachmentModel[40] = new ModelRendererTurbo(this, 24, 32, textureX, textureY); // Box 40
		attachmentModel[41] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 41
		attachmentModel[42] = new ModelRendererTurbo(this, 233, 25, textureX, textureY); // Box 42
		attachmentModel[43] = new ModelRendererTurbo(this, 249, 25, textureX, textureY); // Box 43
		attachmentModel[44] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 44
		attachmentModel[45] = new ModelRendererTurbo(this, 33, 25, textureX, textureY); // Box 45
		attachmentModel[46] = new ModelRendererTurbo(this, 113, 33, textureX, textureY); // Box 46
		attachmentModel[47] = new ModelRendererTurbo(this, 161, 33, textureX, textureY); // Box 47
		attachmentModel[48] = new ModelRendererTurbo(this, 177, 33, textureX, textureY); // Box 48
		attachmentModel[49] = new ModelRendererTurbo(this, 201, 33, textureX, textureY); // Box 49
		attachmentModel[50] = new ModelRendererTurbo(this, 225, 33, textureX, textureY); // Box 50
		attachmentModel[51] = new ModelRendererTurbo(this, 241, 33, textureX, textureY); // Box 51
		attachmentModel[52] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 52
		attachmentModel[53] = new ModelRendererTurbo(this, 17, 41, textureX, textureY); // Box 53
		attachmentModel[54] = new ModelRendererTurbo(this, 33, 41, textureX, textureY); // Box 54
		attachmentModel[55] = new ModelRendererTurbo(this, 105, 41, textureX, textureY); // Box 55
		attachmentModel[56] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 56
		attachmentModel[57] = new ModelRendererTurbo(this, 137, 41, textureX, textureY); // Box 57
		attachmentModel[58] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 63
		attachmentModel[59] = new ModelRendererTurbo(this, 169, 41, textureX, textureY); // Box 64
		attachmentModel[60] = new ModelRendererTurbo(this, 121, 33, textureX, textureY); // Box 65
		attachmentModel[61] = new ModelRendererTurbo(this, 177, 41, textureX, textureY); // Box 66
		attachmentModel[62] = new ModelRendererTurbo(this, 121, 25, textureX, textureY); // Box 67
		attachmentModel[63] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 68
		attachmentModel[64] = new ModelRendererTurbo(this, 201, 41, textureX, textureY); // Box 69
		attachmentModel[65] = new ModelRendererTurbo(this, 209, 41, textureX, textureY); // Box 70
		attachmentModel[66] = new ModelRendererTurbo(this, 217, 41, textureX, textureY); // Box 71
		attachmentModel[67] = new ModelRendererTurbo(this, 193, 41, textureX, textureY); // Box 72
		attachmentModel[68] = new ModelRendererTurbo(this, 233, 41, textureX, textureY); // Box 73
		attachmentModel[69] = new ModelRendererTurbo(this, 241, 41, textureX, textureY); // Box 74
		attachmentModel[70] = new ModelRendererTurbo(this, 249, 41, textureX, textureY); // Box 75
		attachmentModel[71] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 76
		attachmentModel[72] = new ModelRendererTurbo(this, 9, 49, textureX, textureY); // Box 77
		attachmentModel[73] = new ModelRendererTurbo(this, 9, 49, textureX, textureY); // Box 78
		attachmentModel[74] = new ModelRendererTurbo(this, 225, 41, textureX, textureY); // Box 79
		attachmentModel[75] = new ModelRendererTurbo(this, 25, 49, textureX, textureY); // Box 80
		attachmentModel[76] = new ModelRendererTurbo(this, 33, 49, textureX, textureY); // Box 81
		attachmentModel[77] = new ModelRendererTurbo(this, 57, 49, textureX, textureY); // Box 82
		attachmentModel[78] = new ModelRendererTurbo(this, 65, 49, textureX, textureY); // Box 83
		attachmentModel[79] = new ModelRendererTurbo(this, 73, 49, textureX, textureY); // Box 84
		attachmentModel[80] = new ModelRendererTurbo(this, 81, 49, textureX, textureY); // Box 85
		attachmentModel[81] = new ModelRendererTurbo(this, 97, 49, textureX, textureY); // Box 86
		attachmentModel[82] = new ModelRendererTurbo(this, 105, 49, textureX, textureY); // Box 87
		attachmentModel[83] = new ModelRendererTurbo(this, 113, 49, textureX, textureY); // Box 88
		attachmentModel[84] = new ModelRendererTurbo(this, 121, 49, textureX, textureY); // Box 89
		attachmentModel[85] = new ModelRendererTurbo(this, 137, 49, textureX, textureY); // Box 91
		attachmentModel[86] = new ModelRendererTurbo(this, 161, 49, textureX, textureY); // Box 92
		attachmentModel[87] = new ModelRendererTurbo(this, 169, 49, textureX, textureY); // Box 93
		attachmentModel[88] = new ModelRendererTurbo(this, 193, 49, textureX, textureY); // Box 94
		attachmentModel[89] = new ModelRendererTurbo(this, 201, 49, textureX, textureY); // Box 95
		attachmentModel[90] = new ModelRendererTurbo(this, 209, 49, textureX, textureY); // Box 96
		attachmentModel[91] = new ModelRendererTurbo(this, 217, 49, textureX, textureY); // Box 97
		attachmentModel[92] = new ModelRendererTurbo(this, 233, 49, textureX, textureY); // Box 98
		attachmentModel[93] = new ModelRendererTurbo(this, 249, 49, textureX, textureY); // Box 99
		attachmentModel[94] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 100
		attachmentModel[95] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 101
		attachmentModel[96] = new ModelRendererTurbo(this, 33, 57, textureX, textureY); // Box 102
		attachmentModel[97] = new ModelRendererTurbo(this, 25, 57, textureX, textureY); // Box 103
		attachmentModel[98] = new ModelRendererTurbo(this, 49, 57, textureX, textureY); // Box 104
		attachmentModel[99] = new ModelRendererTurbo(this, 65, 57, textureX, textureY); // Box 105
		attachmentModel[100] = new ModelRendererTurbo(this, 81, 57, textureX, textureY); // Box 106
		attachmentModel[101] = new ModelRendererTurbo(this, 89, 57, textureX, textureY); // Box 107
		attachmentModel[102] = new ModelRendererTurbo(this, 105, 57, textureX, textureY); // Box 108
		attachmentModel[103] = new ModelRendererTurbo(this, 121, 57, textureX, textureY); // Box 109
		attachmentModel[104] = new ModelRendererTurbo(this, 41, 41, textureX, textureY); // Box 110
		attachmentModel[105] = new ModelRendererTurbo(this, 97, 41, textureX, textureY); // Box 111
		attachmentModel[106] = new ModelRendererTurbo(this, 129, 33, textureX, textureY); // Box 114
		attachmentModel[107] = new ModelRendererTurbo(this, 41, 49, textureX, textureY); // Box 115
		attachmentModel[108] = new ModelRendererTurbo(this, 135, 33, textureX, textureY); // Box 116

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 2, 3, 7, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 0
		attachmentModel[0].setRotationPoint(-11.5F, -4.5F, 2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 2, 2, 3, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0.5F, 0F, 2F, 0.5F, 0F, 2F, 0F, 0F, 2F); // Box 1
		attachmentModel[1].setRotationPoint(-11.5F, -6.5F, 4F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 2, 2, 3, 0F, 0F, 0F, 2F, 0.5F, 0F, 2F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 2
		attachmentModel[2].setRotationPoint(-11.5F, -1.5F, 4F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 16, 5, 6, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 3
		attachmentModel[3].setRotationPoint(-8F, -5F, 3.5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 13, 2, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F); // Box 4
		attachmentModel[4].setRotationPoint(-8F, -7F, 5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 24, 2, 2, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 5
		attachmentModel[5].setRotationPoint(-8F, 0F, 5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 6, 2, 2, 0F, 0F, -1F, -1F, 0F, 0F, -2F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 6
		attachmentModel[6].setRotationPoint(-8F, 0F, 3F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 7, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 7
		attachmentModel[7].setRotationPoint(4F, 0F, 3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 8, 4, 4, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 8
		attachmentModel[8].setRotationPoint(8F, -4F, 5.5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 10, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F); // Box 9
		attachmentModel[9].setRotationPoint(6F, -7F, 5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 10
		attachmentModel[10].setRotationPoint(4F, -3F, 9F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 11
		attachmentModel[11].setRotationPoint(5F, -4F, 9F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 12
		attachmentModel[12].setRotationPoint(5F, -2F, 9F);

		attachmentModel[13].addShapeBox(0F, 1F, 0F, 2, 2, 3, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F); // Box 13
		attachmentModel[13].setRotationPoint(4.5F, -8F, 5F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 24, 5, 4, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 14
		attachmentModel[14].setRotationPoint(-8F, -7F, 1F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 19, 1, 6, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 15
		attachmentModel[15].setRotationPoint(-8F, -1.5F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 24, 5, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 16
		attachmentModel[16].setRotationPoint(-8F, -7F, -2.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 6, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 17
		attachmentModel[17].setRotationPoint(-8F, -6F, -8.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 18
		attachmentModel[18].setRotationPoint(-8F, -7F, -3.5F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 18, 5, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 1.5F, 0.5F, 0F); // Box 19
		attachmentModel[19].setRotationPoint(-2F, -7F, -3.5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 20
		attachmentModel[20].setRotationPoint(-6.5F, -8F, -6F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 21
		attachmentModel[21].setRotationPoint(-5.5F, -8F, -5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 22
		attachmentModel[22].setRotationPoint(-5.5F, -8F, -7F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 1.5F, 0F, 0.5F, 1.5F, 0F, 0.5F, 0F); // Box 23
		attachmentModel[23].setRotationPoint(-9F, -7F, -7F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 24
		attachmentModel[24].setRotationPoint(-8F, -7F, -6F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 8, 3, 1, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 25
		attachmentModel[25].setRotationPoint(-3F, -4.5F, -9.5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 7, 5, 1, 0F, 1.5F, 0F, 0F, 0.5F, 0F, 0F, 2F, 0F, 0.5F, 0F, 0F, 0.5F, 1.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 2F, 0.5F, 0.5F, 0F, 0.5F, 0.5F); // Box 26
		attachmentModel[26].setRotationPoint(-2F, -7F, -8.5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 9, 5, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 3F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 27
		attachmentModel[27].setRotationPoint(-2F, -7F, -7F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 28
		attachmentModel[28].setRotationPoint(3F, -7.5F, -6F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 29
		attachmentModel[29].setRotationPoint(4F, -7.5F, -7F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 30
		attachmentModel[30].setRotationPoint(4F, -7.5F, -5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 31
		attachmentModel[31].setRotationPoint(4F, -7.5F, 5.5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 32
		attachmentModel[32].setRotationPoint(5F, -7.5F, 4.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 33
		attachmentModel[33].setRotationPoint(5F, -7.5F, 6.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0.5F, 0.5F, 0F, -1.5F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0.5F, 0.5F, 0F, -1.5F, 0F, 0F, 0F); // Box 34
		attachmentModel[34].setRotationPoint(-8F, -7F, -8F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 6, 5, 2, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[35].setRotationPoint(10F, -7F, -6F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 10, 4, 2, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0.5F, 0.5F, 0.5F, 0.5F, 0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 36
		attachmentModel[36].setRotationPoint(5.5F, -6F, -8F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 9, 2, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 37
		attachmentModel[37].setRotationPoint(7F, -4F, -9.5F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0.5F, 0.5F, 0.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0.5F, 3F, 0F, 0.5F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F); // Box 38
		attachmentModel[38].setRotationPoint(6F, -4F, -9.5F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 39
		attachmentModel[39].setRotationPoint(6F, -6F, -8.5F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 13, 2, 4, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 40
		attachmentModel[40].setRotationPoint(3F, 0F, -8F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 13, 1, 7, 0F, 1.5F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.75F, 1.5F, 0.5F, 0.75F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 41
		attachmentModel[41].setRotationPoint(3F, -1F, -10F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 6, 3, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 42
		attachmentModel[42].setRotationPoint(-2F, -1F, -4F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 43
		attachmentModel[43].setRotationPoint(0F, -0.5F, -5F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 44
		attachmentModel[44].setRotationPoint(-0.5F, 0F, -7F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 45
		attachmentModel[45].setRotationPoint(0.5F, 1F, -8F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 46
		attachmentModel[46].setRotationPoint(0.5F, -1F, -8F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 4, 2, 2, 0F, 0F, 0F, 1F, 2.5F, 0F, 1F, 2.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 47
		attachmentModel[47].setRotationPoint(-6F, -1.5F, -9F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 2, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 48
		attachmentModel[48].setRotationPoint(-8F, -1.5F, -9F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 8, 1, 2, 0F, 0F, 0.5F, 0.75F, 0F, 0.5F, 0.75F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 49
		attachmentModel[49].setRotationPoint(8F, -1F, 3F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 50
		attachmentModel[50].setRotationPoint(16F, -3F, 3F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 5, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 51
		attachmentModel[51].setRotationPoint(16F, -5F, 5F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 5, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[52].setRotationPoint(16F, 0F, 5F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 53
		attachmentModel[53].setRotationPoint(16F, -3F, 8F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 54
		attachmentModel[54].setRotationPoint(16F, -5F, 8F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 55
		attachmentModel[55].setRotationPoint(16F, -5F, 3F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, 1F, 0F, -1F, 1F); // Box 56
		attachmentModel[56].setRotationPoint(16F, -1F, 3F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 5, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 1F, 0F, -1F, 1F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 57
		attachmentModel[57].setRotationPoint(16F, -1F, 8F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 63
		attachmentModel[58].setRotationPoint(3.5F, -4.5F, -10.5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 64
		attachmentModel[59].setRotationPoint(2.5F, -3.5F, -10.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 65
		attachmentModel[60].setRotationPoint(3.5F, -2.5F, -10.5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 1, 4, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 66
		attachmentModel[61].setRotationPoint(19.5F, -4F, 4F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 67
		attachmentModel[62].setRotationPoint(-3F, -7.5F, -2F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 68
		attachmentModel[63].setRotationPoint(-1F, -7.5F, -4F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 1F); // Box 69
		attachmentModel[64].setRotationPoint(-3F, -7.5F, -4F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F); // Box 70
		attachmentModel[65].setRotationPoint(-3F, -7.5F, 3F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 71
		attachmentModel[66].setRotationPoint(-1F, -7.5F, 3F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 72
		attachmentModel[67].setRotationPoint(4F, -7.5F, -2F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, -2F, 0F, 0F, 0F); // Box 73
		attachmentModel[68].setRotationPoint(3F, -7.5F, 3F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, -1F, 0F, 1F, 0F, 0F, 0F); // Box 74
		attachmentModel[69].setRotationPoint(3F, -7.5F, -4F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 75
		attachmentModel[70].setRotationPoint(-9F, -1F, -3F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 76
		attachmentModel[71].setRotationPoint(-9F, -1F, -1F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 1F, 0F, 0.5F, 1F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 77
		attachmentModel[72].setRotationPoint(-10F, -1F, -2F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 1.5F, 1F, 0F, 1.5F, -1F, 0F, 0F); // Box 78
		attachmentModel[73].setRotationPoint(-9F, -5.5F, -7F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 79
		attachmentModel[74].setRotationPoint(-12F, -3.5F, 4F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 80
		attachmentModel[75].setRotationPoint(-12F, -4.5F, 5F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 81
		attachmentModel[76].setRotationPoint(-12F, -2.5F, 5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 82
		attachmentModel[77].setRotationPoint(-8.5F, -2F, -8.5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 83
		attachmentModel[78].setRotationPoint(-8.5F, -3F, -9.5F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 84
		attachmentModel[79].setRotationPoint(-8.5F, -4F, -8.5F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 85
		attachmentModel[80].setRotationPoint(11F, -1.5F, -0.5F);

		attachmentModel[81].addShapeBox(0F, -0.5F, -0.5F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 86
		attachmentModel[81].setRotationPoint(15F, -0.75F, 0F);
		attachmentModel[81].rotateAngleX = -0.78539816F;

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 87
		attachmentModel[82].setRotationPoint(-7F, -3.5F, -10F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 88
		attachmentModel[83].setRotationPoint(-5.5F, -6F, -10F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 89
		attachmentModel[84].setRotationPoint(-4F, -3.5F, -10F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, -1F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, -1F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F); // Box 91
		attachmentModel[85].setRotationPoint(-7F, -6F, -10F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 92
		attachmentModel[86].setRotationPoint(-5.5F, -4F, -10F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.75F, -0.25F, 0F, -0.75F, -0.25F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 93
		attachmentModel[87].setRotationPoint(-7.5F, -5F, -10F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, -0.5F, -0.25F, 0F, -0.5F, -0.25F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 94
		attachmentModel[88].setRotationPoint(-3F, -5F, -10F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 0F, -0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F); // Box 95
		attachmentModel[89].setRotationPoint(-4F, -6F, -10F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 96
		attachmentModel[90].setRotationPoint(-8F, -4.5F, -9.5F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, -0.1F, 0F, 0F, -0.1F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, -1F, -0.1F, 0F, -1F, -0.1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 97
		attachmentModel[91].setRotationPoint(-7F, -5.5F, -9.5F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 4, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 98
		attachmentModel[92].setRotationPoint(-7F, -5.5F, -9.5F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 99
		attachmentModel[93].setRotationPoint(-6F, -3.5F, -10F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 6, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 100
		attachmentModel[94].setRotationPoint(-2F, -7.25F, -3F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 1F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 101
		attachmentModel[95].setRotationPoint(-3.5F, -7F, -6F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 3, 3, 7, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 102
		attachmentModel[96].setRotationPoint(17.49F, -3.5F, 2.5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 3, 2, 3, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F, 0.5F, 0F, 2F); // Box 103
		attachmentModel[97].setRotationPoint(17.49F, -5.5F, 4.5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 3, 2, 3, 0F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F, 0.5F, 0F, 2F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 104
		attachmentModel[98].setRotationPoint(17.49F, -0.5F, 4.5F);

		attachmentModel[99].addShapeBox(-1F, 0F, -1F, 2, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 105
		attachmentModel[99].setRotationPoint(13F, -7.5F, -5.5F);
		attachmentModel[99].rotateAngleY = 0.78539816F;

		attachmentModel[100].addShapeBox(-1F, 0F, -1F, 2, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 106
		attachmentModel[100].setRotationPoint(13F, 0.5F, -5.5F);
		attachmentModel[100].rotateAngleY = 0.78539816F;

		attachmentModel[101].addShapeBox(0F, -1F, -3F, 2, 2, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 107
		attachmentModel[101].setRotationPoint(-10.5F, -3F, 5.5F);
		attachmentModel[101].rotateAngleX = -0.78539816F;

		attachmentModel[102].addShapeBox(0F, -3F, -1F, 2, 2, 2, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0.5F, 0F, 2F, 0.5F, 0F, 2F, 0F, 0F, 2F); // Box 108
		attachmentModel[102].setRotationPoint(-10.5F, -3F, 5.5F);
		attachmentModel[102].rotateAngleX = -0.78539816F;

		attachmentModel[103].addShapeBox(0F, 1F, -1F, 2, 2, 2, 0F, 0F, 0F, 2F, 0.5F, 0F, 2F, 0.5F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 109
		attachmentModel[103].setRotationPoint(-10.5F, -3F, 5.5F);
		attachmentModel[103].rotateAngleX = -0.78539816F;

		attachmentModel[104].addShapeBox(0F, -4F, -8.5F, 1, 1, 6, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.75F, 0F, 0.5F, 0.75F, 0F, 0.5F, 0.75F, 0F, 0F, 0.75F, 0F); // Box 110
		attachmentModel[104].setRotationPoint(16F, -5.5F, -1F);
		attachmentModel[104].rotateAngleX = 0.78539816F;

		attachmentModel[105].addShapeBox(0F, -2.25F, -6.5F, 1, 2, 2, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 111
		attachmentModel[105].setRotationPoint(16F, -5.5F, -1F);
		attachmentModel[105].rotateAngleX = 0.78539816F;

		attachmentModel[106].addShapeBox(0F, -2.5F, -2F, 1, 2, 1, 0F, 0F, 1.5F, 0.5F, 0.5F, 1.5F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0.5F, 0.5F, 2F, 0.5F, 0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 114
		attachmentModel[106].setRotationPoint(16F, -5.5F, -1F);
		attachmentModel[106].rotateAngleX = 0.78539816F;

		attachmentModel[107].addShapeBox(0F, 0.5F, -8.5F, 1, 1, 6, 0F, 0F, 0.75F, 0F, 0.5F, 0.75F, 0F, 0.5F, 0.75F, 0F, 0F, 0.75F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 115
		attachmentModel[107].setRotationPoint(16F, -5.5F, -1F);
		attachmentModel[107].rotateAngleX = 0.78539816F;

		attachmentModel[108].addShapeBox(0F, -2.5F, -10F, 1, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 1.5F, 0.5F, 0F, 1.5F, 0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 2F, 0.5F, 0F, 2F, 0.5F); // Box 116
		attachmentModel[108].setRotationPoint(16F, -5.5F, -1F);
		attachmentModel[108].rotateAngleX = 0.78539816F;

		laserFrom.set(1F, 1.1F / 16F, 7.2F / 16F);

		flipAll();
	}
}