//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC Honeybee Silencer@5.56mm
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICHoneybeeSilencer556mm extends ModelAttachment //Same as Filename
{
	int textureX = 128;
	int textureY = 128;

	public ModelCICHoneybeeSilencer556mm() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[50];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 581
		attachmentModel[1] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 582
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 583
		attachmentModel[3] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 584
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 585
		attachmentModel[5] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 586
		attachmentModel[6] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 587
		attachmentModel[7] = new ModelRendererTurbo(this, 89, 17, textureX, textureY); // Box 588
		attachmentModel[8] = new ModelRendererTurbo(this, 73, 17, textureX, textureY); // Box 589
		attachmentModel[9] = new ModelRendererTurbo(this, 105, 17, textureX, textureY); // Box 590
		attachmentModel[10] = new ModelRendererTurbo(this, 113, 25, textureX, textureY); // Box 591
		attachmentModel[11] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 592
		attachmentModel[12] = new ModelRendererTurbo(this, 17, 49, textureX, textureY); // Box 593
		attachmentModel[13] = new ModelRendererTurbo(this, 33, 49, textureX, textureY); // Box 594
		attachmentModel[14] = new ModelRendererTurbo(this, 41, 49, textureX, textureY); // Box 595
		attachmentModel[15] = new ModelRendererTurbo(this, 81, 17, textureX, textureY); // Box 596
		attachmentModel[16] = new ModelRendererTurbo(this, 9, 49, textureX, textureY); // Box 597
		attachmentModel[17] = new ModelRendererTurbo(this, 73, 49, textureX, textureY); // Box 598
		attachmentModel[18] = new ModelRendererTurbo(this, 89, 49, textureX, textureY); // Box 599
		attachmentModel[19] = new ModelRendererTurbo(this, 25, 49, textureX, textureY); // Box 600
		attachmentModel[20] = new ModelRendererTurbo(this, 105, 49, textureX, textureY); // Box 601
		attachmentModel[21] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 602
		attachmentModel[22] = new ModelRendererTurbo(this, 25, 65, textureX, textureY); // Box 603
		attachmentModel[23] = new ModelRendererTurbo(this, 49, 65, textureX, textureY); // Box 604
		attachmentModel[24] = new ModelRendererTurbo(this, 17, 57, textureX, textureY); // Box 605
		attachmentModel[25] = new ModelRendererTurbo(this, 17, 73, textureX, textureY); // Box 606
		attachmentModel[26] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 607
		attachmentModel[27] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 608
		attachmentModel[28] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 609
		attachmentModel[29] = new ModelRendererTurbo(this, 17, 9, textureX, textureY); // Box 610
		attachmentModel[30] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 611
		attachmentModel[31] = new ModelRendererTurbo(this, 25, 17, textureX, textureY); // Box 612
		attachmentModel[32] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 613
		attachmentModel[33] = new ModelRendererTurbo(this, 89, 17, textureX, textureY); // Box 614
		attachmentModel[34] = new ModelRendererTurbo(this, 121, 17, textureX, textureY); // Box 615
		attachmentModel[35] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 616
		attachmentModel[36] = new ModelRendererTurbo(this, 17, 1, textureX, textureY); // Box 618
		attachmentModel[37] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 619
		attachmentModel[38] = new ModelRendererTurbo(this, 121, 33, textureX, textureY); // Box 620
		attachmentModel[39] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 621
		attachmentModel[40] = new ModelRendererTurbo(this, 65, 49, textureX, textureY); // Box 622
		attachmentModel[41] = new ModelRendererTurbo(this, 81, 49, textureX, textureY); // Box 623
		attachmentModel[42] = new ModelRendererTurbo(this, 97, 49, textureX, textureY); // Box 624
		attachmentModel[43] = new ModelRendererTurbo(this, 121, 49, textureX, textureY); // Box 625
		attachmentModel[44] = new ModelRendererTurbo(this, 113, 17, textureX, textureY); // Box 626
		attachmentModel[45] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 627
		attachmentModel[46] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 628
		attachmentModel[47] = new ModelRendererTurbo(this, 73, 65, textureX, textureY); // Box 629
		attachmentModel[48] = new ModelRendererTurbo(this, 97, 57, textureX, textureY); // Box 630
		attachmentModel[49] = new ModelRendererTurbo(this, 105, 89, textureX, textureY); // Box 631

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 5, 5, 4, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 581
		attachmentModel[0].setRotationPoint(4F, -2.5F, -5F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 46, 3, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 582
		attachmentModel[1].setRotationPoint(15F, -5.5F, -2.5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 8, 3, 5, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 583
		attachmentModel[2].setRotationPoint(1F, 2.5F, -2.5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 5, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 584
		attachmentModel[3].setRotationPoint(4F, -2.5F, 1F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 57, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 585
		attachmentModel[4].setRotationPoint(4F, -2.5F, -1F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 57, 1, 2, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 586
		attachmentModel[5].setRotationPoint(4F, 1.5F, -1F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 3, 1, 9, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 587
		attachmentModel[6].setRotationPoint(0F, 1.5F, -4.5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 3, 1, 9, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 588
		attachmentModel[7].setRotationPoint(0F, -2.5F, -4.5F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 589
		attachmentModel[8].setRotationPoint(0F, -2F, -4.5F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 590
		attachmentModel[9].setRotationPoint(0F, -2F, 1.5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 591
		attachmentModel[10].setRotationPoint(0F, 1F, 1.5F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 592
		attachmentModel[11].setRotationPoint(0F, 1F, -4.5F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 3, 2, 2, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 593
		attachmentModel[12].setRotationPoint(0F, -1F, 2.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 3, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 594
		attachmentModel[13].setRotationPoint(0F, -1F, -4.5F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 8, 3, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 595
		attachmentModel[14].setRotationPoint(1F, -5.5F, -2.5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 3, 5, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 3F); // Box 596
		attachmentModel[15].setRotationPoint(9F, -5.5F, -2.5F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 3, 5, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 2.5F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 2.5F); // Box 597
		attachmentModel[16].setRotationPoint(14F, -5.5F, -2.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 1, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F); // Box 598
		attachmentModel[17].setRotationPoint(9F, -2.5F, 1F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 1, 5, 4, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 599
		attachmentModel[18].setRotationPoint(9F, -2.5F, -5F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 3, 5, 0F, 0F, 0F, 3F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F); // Box 600
		attachmentModel[19].setRotationPoint(9F, 2.5F, -2.5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 4, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 601
		attachmentModel[20].setRotationPoint(10F, -2.5F, -5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 4, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 602
		attachmentModel[21].setRotationPoint(10F, -2.5F, 1F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 4, 2, 5, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 2.5F); // Box 603
		attachmentModel[22].setRotationPoint(10F, -4.5F, -2.5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 4, 2, 5, 0F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0F, 2.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 604
		attachmentModel[23].setRotationPoint(10F, 2.5F, -2.5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 3, 5, 0F, 0F, 0F, 2.5F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 2.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F); // Box 605
		attachmentModel[24].setRotationPoint(14F, 2.5F, -2.5F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 46, 3, 5, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 606
		attachmentModel[25].setRotationPoint(15F, 2.5F, -2.5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 46, 5, 4, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 607
		attachmentModel[26].setRotationPoint(15F, -2.5F, -5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 46, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 608
		attachmentModel[27].setRotationPoint(15F, -2.5F, 1F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 1F); // Box 609
		attachmentModel[28].setRotationPoint(61F, -1.5F, 3.5F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 1F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 1F, 1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 610
		attachmentModel[29].setRotationPoint(61F, -4.5F, -1.5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, -1F, 2F, 0F, -1F, 3F); // Box 611
		attachmentModel[30].setRotationPoint(61F, -4.5F, 1.5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -1F, 3F, 0F, -1F, 2F, 0F, 0F, -2F, 0F, 0F, -1F); // Box 612
		attachmentModel[31].setRotationPoint(61F, -4.5F, -2.5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 1F, 1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 613
		attachmentModel[32].setRotationPoint(61F, -1.5F, -4.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, -1F, 3F, 0F, -1F, 2F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -2F, 0F); // Box 614
		attachmentModel[33].setRotationPoint(61F, 1.5F, -2.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, -1F, 2F, 0F, -1F, 3F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 615
		attachmentModel[34].setRotationPoint(61F, 1.5F, 1.5F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 1F, 1F); // Box 616
		attachmentModel[35].setRotationPoint(61F, 3.5F, -1.5F);

		attachmentModel[36].addShapeBox(0F, -5.5F, -0.5F, 2, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 618
		attachmentModel[36].setRotationPoint(62F, 0F, 0F);

		attachmentModel[37].addShapeBox(0F, -5.5F, -0.5F, 2, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F); // Box 619
		attachmentModel[37].setRotationPoint(62F, 0F, 0F);
		attachmentModel[37].rotateAngleX = -0.78539816F;

		attachmentModel[38].addShapeBox(0F, -0.5F, 4.5F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 620
		attachmentModel[38].setRotationPoint(62F, 0F, 0F);

		attachmentModel[39].addShapeBox(0F, -0.5F, 4.5F, 2, 1, 1, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 621
		attachmentModel[39].setRotationPoint(62F, 0F, 0F);
		attachmentModel[39].rotateAngleX = -0.78539816F;

		attachmentModel[40].addShapeBox(0F, 4.5F, -0.5F, 2, 1, 1, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 622
		attachmentModel[40].setRotationPoint(62F, 0F, 0F);

		attachmentModel[41].addShapeBox(0F, 4.5F, -0.5F, 2, 1, 1, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 623
		attachmentModel[41].setRotationPoint(62F, 0F, 0F);
		attachmentModel[41].rotateAngleX = -0.78539816F;

		attachmentModel[42].addShapeBox(0F, -0.5F, -5.5F, 2, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 624
		attachmentModel[42].setRotationPoint(62F, 0F, 0F);

		attachmentModel[43].addShapeBox(0F, -0.5F, -5.5F, 2, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F); // Box 625
		attachmentModel[43].setRotationPoint(62F, 0F, 0F);
		attachmentModel[43].rotateAngleX = -0.78539816F;

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 1, 2, 5, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 2F); // Box 626
		attachmentModel[44].setRotationPoint(0F, -4.5F, -2.5F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 2, 5, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F); // Box 627
		attachmentModel[45].setRotationPoint(0F, 2.5F, -2.5F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 3, 5, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 628
		attachmentModel[46].setRotationPoint(1F, -2.5F, 4.5F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 3, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 629
		attachmentModel[47].setRotationPoint(1F, -2.5F, -5.5F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 630
		attachmentModel[48].setRotationPoint(14F, -2.5F, -5F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 5, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0F); // Box 631
		attachmentModel[49].setRotationPoint(14F, -2.5F, 1F);

		flipAll();
	}
}