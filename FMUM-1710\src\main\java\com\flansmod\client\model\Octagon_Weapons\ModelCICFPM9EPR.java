//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CIC FP-M9 EPR
// Model Creator: 
// Created on: 24.08.2019 - 09:46:57
// Last changed on: 24.08.2019 - 09:46:57

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICFPM9EPR extends ModelAttachment //Same as Filename
{
	int textureX = 128;
	int textureY = 64;

	public ModelCICFPM9EPR() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[23];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 226
		attachmentModel[1] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 227
		attachmentModel[2] = new ModelRendererTurbo(this, 49, 1, textureX, textureY); // Box 228
		attachmentModel[3] = new ModelRendererTurbo(this, 65, 1, textureX, textureY); // Box 229
		attachmentModel[4] = new ModelRendererTurbo(this, 81, 1, textureX, textureY); // Box 230
		attachmentModel[5] = new ModelRendererTurbo(this, 97, 1, textureX, textureY); // Box 231
		attachmentModel[6] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 232
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 233
		attachmentModel[8] = new ModelRendererTurbo(this, 41, 9, textureX, textureY); // Box 234
		attachmentModel[9] = new ModelRendererTurbo(this, 73, 9, textureX, textureY); // Box 235
		attachmentModel[10] = new ModelRendererTurbo(this, 97, 9, textureX, textureY); // Box 236
		attachmentModel[11] = new ModelRendererTurbo(this, 41, 17, textureX, textureY); // Box 237
		attachmentModel[12] = new ModelRendererTurbo(this, 65, 17, textureX, textureY); // Box 238
		attachmentModel[13] = new ModelRendererTurbo(this, 81, 17, textureX, textureY); // Box 239
		attachmentModel[14] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 240
		attachmentModel[15] = new ModelRendererTurbo(this, 33, 25, textureX, textureY); // Box 241
		attachmentModel[16] = new ModelRendererTurbo(this, 57, 25, textureX, textureY); // Box 242
		attachmentModel[17] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 243
		attachmentModel[18] = new ModelRendererTurbo(this, 105, 25, textureX, textureY); // Box 244
		attachmentModel[19] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 245
		attachmentModel[20] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 246
		attachmentModel[21] = new ModelRendererTurbo(this, 49, 41, textureX, textureY); // Box 247
		attachmentModel[22] = new ModelRendererTurbo(this, 73, 41, textureX, textureY); // Box 253

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 14, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 226
		attachmentModel[0].setRotationPoint(-2F, -2F, -4F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 227
		attachmentModel[1].setRotationPoint(-0.5F, -1F, -6.5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 228
		attachmentModel[2].setRotationPoint(-0.5F, -2F, -7F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 229
		attachmentModel[3].setRotationPoint(-0.5F, 0F, -7F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 230
		attachmentModel[4].setRotationPoint(7.5F, 0F, -7F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 231
		attachmentModel[5].setRotationPoint(7.5F, -1F, -6.5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 232
		attachmentModel[6].setRotationPoint(7.5F, -2F, -7F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 15, 2, 6, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 233
		attachmentModel[7].setRotationPoint(-2F, 1F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 14, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 234
		attachmentModel[8].setRotationPoint(-2F, -2F, 3F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 235
		attachmentModel[9].setRotationPoint(-2F, 3F, -3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 236
		attachmentModel[10].setRotationPoint(3F, 3F, -3F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 237
		attachmentModel[11].setRotationPoint(7F, 3F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 3, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 238
		attachmentModel[12].setRotationPoint(11F, 3F, -3F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 14, 2, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 239
		attachmentModel[13].setRotationPoint(-2F, -4F, 4F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 14, 10, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 240
		attachmentModel[14].setRotationPoint(-2F, -14F, 6F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 14, 3, 1, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 241
		attachmentModel[15].setRotationPoint(-2F, -17F, 4F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 14, 1, 7, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 242
		attachmentModel[16].setRotationPoint(-2F, -17F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 14, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 243
		attachmentModel[17].setRotationPoint(-2F, -18F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 244
		attachmentModel[18].setRotationPoint(-2F, -19F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 245
		attachmentModel[19].setRotationPoint(2F, -19F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 246
		attachmentModel[20].setRotationPoint(6F, -19F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 2, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 247
		attachmentModel[21].setRotationPoint(10F, -19F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 14, 1, 7, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 253
		attachmentModel[22].setRotationPoint(-2F, 0F, -3F);
	}
}