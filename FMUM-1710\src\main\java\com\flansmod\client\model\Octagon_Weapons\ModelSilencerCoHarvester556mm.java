//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: SilencerCo Harvester@5.56mm
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelSilencerCoHarvester556mm extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelSilencerCoHarvester556mm() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[43];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 473
		attachmentModel[1] = new ModelRendererTurbo(this, 177, 1, textureX, textureY); // Box 474
		attachmentModel[2] = new ModelRendererTurbo(this, 193, 1, textureX, textureY); // Box 475
		attachmentModel[3] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 476
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 477
		attachmentModel[5] = new ModelRendererTurbo(this, 209, 1, textureX, textureY); // Box 478
		attachmentModel[6] = new ModelRendererTurbo(this, 225, 1, textureX, textureY); // Box 479
		attachmentModel[7] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 480
		attachmentModel[8] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 520
		attachmentModel[9] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 521
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 522
		attachmentModel[11] = new ModelRendererTurbo(this, 193, 9, textureX, textureY); // Box 523
		attachmentModel[12] = new ModelRendererTurbo(this, 225, 9, textureX, textureY); // Box 524
		attachmentModel[13] = new ModelRendererTurbo(this, 241, 9, textureX, textureY); // Box 525
		attachmentModel[14] = new ModelRendererTurbo(this, 177, 17, textureX, textureY); // Box 526
		attachmentModel[15] = new ModelRendererTurbo(this, 209, 17, textureX, textureY); // Box 527
		attachmentModel[16] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 528
		attachmentModel[17] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 529
		attachmentModel[18] = new ModelRendererTurbo(this, 169, 25, textureX, textureY); // Box 530
		attachmentModel[19] = new ModelRendererTurbo(this, 185, 25, textureX, textureY); // Box 531
		attachmentModel[20] = new ModelRendererTurbo(this, 201, 25, textureX, textureY); // Box 532
		attachmentModel[21] = new ModelRendererTurbo(this, 217, 25, textureX, textureY); // Box 533
		attachmentModel[22] = new ModelRendererTurbo(this, 233, 25, textureX, textureY); // Box 534
		attachmentModel[23] = new ModelRendererTurbo(this, 169, 17, textureX, textureY); // Box 535
		attachmentModel[24] = new ModelRendererTurbo(this, 217, 33, textureX, textureY); // Box 536
		attachmentModel[25] = new ModelRendererTurbo(this, 249, 25, textureX, textureY); // Box 537
		attachmentModel[26] = new ModelRendererTurbo(this, 177, 41, textureX, textureY); // Box 538
		attachmentModel[27] = new ModelRendererTurbo(this, 193, 41, textureX, textureY); // Box 539
		attachmentModel[28] = new ModelRendererTurbo(this, 233, 41, textureX, textureY); // Box 540
		attachmentModel[29] = new ModelRendererTurbo(this, 249, 33, textureX, textureY); // Box 541
		attachmentModel[30] = new ModelRendererTurbo(this, 209, 41, textureX, textureY); // Box 542
		attachmentModel[31] = new ModelRendererTurbo(this, 209, 49, textureX, textureY); // Box 543
		attachmentModel[32] = new ModelRendererTurbo(this, 177, 57, textureX, textureY); // Box 544
		attachmentModel[33] = new ModelRendererTurbo(this, 193, 57, textureX, textureY); // Box 545
		attachmentModel[34] = new ModelRendererTurbo(this, 225, 57, textureX, textureY); // Box 546
		attachmentModel[35] = new ModelRendererTurbo(this, 249, 41, textureX, textureY); // Box 547
		attachmentModel[36] = new ModelRendererTurbo(this, 225, 49, textureX, textureY); // Box 548
		attachmentModel[37] = new ModelRendererTurbo(this, 241, 57, textureX, textureY); // Box 549
		attachmentModel[38] = new ModelRendererTurbo(this, 209, 65, textureX, textureY); // Box 550
		attachmentModel[39] = new ModelRendererTurbo(this, 161, 73, textureX, textureY); // Box 551
		attachmentModel[40] = new ModelRendererTurbo(this, 177, 73, textureX, textureY); // Box 552
		attachmentModel[41] = new ModelRendererTurbo(this, 225, 73, textureX, textureY); // Box 553
		attachmentModel[42] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 554

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 81, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F); // Box 473
		attachmentModel[0].setRotationPoint(2F, -7F, -2.99999999999999F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 2, 3, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 474
		attachmentModel[1].setRotationPoint(0F, -6F, 2F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 3, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 475
		attachmentModel[2].setRotationPoint(0F, -1F, 2F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 80, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 476
		attachmentModel[3].setRotationPoint(3F, -3F, 1F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 80, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 477
		attachmentModel[4].setRotationPoint(3F, -3F, -1F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 3, 4, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F); // Box 478
		attachmentModel[5].setRotationPoint(0F, -6F, -2F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 479
		attachmentModel[6].setRotationPoint(0F, -7F, -2F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 3, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 480
		attachmentModel[7].setRotationPoint(0F, 1F, 2F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 81, 4, 6, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 520
		attachmentModel[8].setRotationPoint(2F, 3F, -2.99999999999999F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 80, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 521
		attachmentModel[9].setRotationPoint(3F, -3F, -7F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 80, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 522
		attachmentModel[10].setRotationPoint(3F, 1F, -1F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 3, 4, 4, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 523
		attachmentModel[11].setRotationPoint(0F, 2F, -2F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 3, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 524
		attachmentModel[12].setRotationPoint(0F, -2F, 2F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 3, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 525
		attachmentModel[13].setRotationPoint(0F, -2F, -6F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 3, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 526
		attachmentModel[14].setRotationPoint(0F, -1F, -6F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 3, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 527
		attachmentModel[15].setRotationPoint(0F, 1F, -6F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 2, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 528
		attachmentModel[16].setRotationPoint(0F, 6F, -2F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 3, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 529
		attachmentModel[17].setRotationPoint(0F, -3F, 6F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 2, 3, 5, 0F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -4F, 0F, 1F, -4F); // Box 530
		attachmentModel[18].setRotationPoint(0F, -6F, -7F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 2, 3, 5, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 531
		attachmentModel[19].setRotationPoint(0F, 3F, -7F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 2, 3, 5, 0F, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -4F, 0F, 1F, -4F); // Box 532
		attachmentModel[20].setRotationPoint(0F, 3F, 2F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 3, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 533
		attachmentModel[21].setRotationPoint(0F, -3F, -7F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 4, 6, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 534
		attachmentModel[22].setRotationPoint(85F, 3F, -2.99999999999999F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 535
		attachmentModel[23].setRotationPoint(85F, 1F, -1F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 536
		attachmentModel[24].setRotationPoint(85F, -3F, -7F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 537
		attachmentModel[25].setRotationPoint(85F, -3F, -1F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 538
		attachmentModel[26].setRotationPoint(85F, -3F, 1F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F); // Box 539
		attachmentModel[27].setRotationPoint(85F, -7F, -2.99999999999999F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 540
		attachmentModel[28].setRotationPoint(88F, -3F, 1F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 541
		attachmentModel[29].setRotationPoint(88F, -3F, -1F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 542
		attachmentModel[30].setRotationPoint(88F, 1F, -1F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 543
		attachmentModel[31].setRotationPoint(88F, -3F, -7F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F); // Box 544
		attachmentModel[32].setRotationPoint(88F, -7F, -2.99999999999999F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 4, 6, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 545
		attachmentModel[33].setRotationPoint(88F, 3F, -2.99999999999999F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 546
		attachmentModel[34].setRotationPoint(91F, -3F, 1F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 547
		attachmentModel[35].setRotationPoint(91F, -3F, -1F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 548
		attachmentModel[36].setRotationPoint(91F, 1F, -1F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 6, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 549
		attachmentModel[37].setRotationPoint(91F, -3F, -7F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 4, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F); // Box 550
		attachmentModel[38].setRotationPoint(91F, -7F, -2.99999999999999F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 4, 6, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 551
		attachmentModel[39].setRotationPoint(91F, 3F, -2.99999999999999F);

		attachmentModel[40].addShapeBox(0F, -6.5F, -0.5F, 8, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 552
		attachmentModel[40].setRotationPoint(83F, 0F, 0F);
		attachmentModel[40].rotateAngleX = -1.04719755F;

		attachmentModel[41].addShapeBox(0F, -6.5F, -0.5F, 8, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 553
		attachmentModel[41].setRotationPoint(83F, 0F, 0F);
		attachmentModel[41].rotateAngleX = 1.04719755F;

		attachmentModel[42].addShapeBox(0F, -6.5F, -0.5F, 8, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 554
		attachmentModel[42].setRotationPoint(83F, 0F, 0F);
		attachmentModel[42].rotateAngleX = -3.14159265F;



		flipAll();
	}
}