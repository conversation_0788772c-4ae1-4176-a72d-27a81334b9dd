//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: Muzzle Brake@9mm Mod 0
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.Vector3f;

public class ModelMuzzleBrake9mmMod0 extends ModelAttachment //Same as Filename
{
	int textureX = 64;
	int textureY = 32;

	public ModelMuzzleBrake9mmMod0() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[40];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 473
		attachmentModel[1] = new ModelRendererTurbo(this, 25, 1, textureX, textureY); // Box 476
		attachmentModel[2] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 477
		attachmentModel[3] = new ModelRendererTurbo(this, 57, 1, textureX, textureY); // Box 489
		attachmentModel[4] = new ModelRendererTurbo(this, 2, 9, textureX, textureY); // Box 491
		attachmentModel[5] = new ModelRendererTurbo(this, 21, 9, textureX, textureY); // Box 495
		attachmentModel[6] = new ModelRendererTurbo(this, 41, 9, textureX, textureY); // Box 496
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 497
		attachmentModel[8] = new ModelRendererTurbo(this, 33, 17, textureX, textureY); // Box 498
		attachmentModel[9] = new ModelRendererTurbo(this, 57, 9, textureX, textureY); // Box 505
		attachmentModel[10] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 506
		attachmentModel[11] = new ModelRendererTurbo(this, 33, 1, textureX, textureY); // Box 23
		attachmentModel[12] = new ModelRendererTurbo(this, 1, 26, textureX, textureY); // Box 24
		attachmentModel[13] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 25
		attachmentModel[14] = new ModelRendererTurbo(this, 17, 25, textureX, textureY); // Box 26
		attachmentModel[15] = new ModelRendererTurbo(this, 25, 25, textureX, textureY); // Box 27
		attachmentModel[16] = new ModelRendererTurbo(this, 33, 25, textureX, textureY); // Box 28
		attachmentModel[17] = new ModelRendererTurbo(this, 41, 25, textureX, textureY); // Box 29
		attachmentModel[18] = new ModelRendererTurbo(this, 49, 25, textureX, textureY); // Box 30
		attachmentModel[19] = new ModelRendererTurbo(this, 57, 25, textureX, textureY); // Box 31
		attachmentModel[20] = new ModelRendererTurbo(this, 25, 5, textureX, textureY); // Box 32
		attachmentModel[21] = new ModelRendererTurbo(this, 33, 11, textureX, textureY); // Box 33
		attachmentModel[22] = new ModelRendererTurbo(this, 17, 5, textureX, textureY); // Box 34
		attachmentModel[23] = new ModelRendererTurbo(this, 51, 6, textureX, textureY); // Box 35
		attachmentModel[24] = new ModelRendererTurbo(this, 9, 5, textureX, textureY); // Box 36
		attachmentModel[25] = new ModelRendererTurbo(this, 10, 26, textureX, textureY); // Box 37
		attachmentModel[26] = new ModelRendererTurbo(this, 49, 12, textureX, textureY); // Box 38
		attachmentModel[27] = new ModelRendererTurbo(this, 1, 5, textureX, textureY); // Box 39
		attachmentModel[28] = new ModelRendererTurbo(this, 26, 17, textureX, textureY); // Box 40
		attachmentModel[29] = new ModelRendererTurbo(this, 26, 9, textureX, textureY); // Box 41
		attachmentModel[30] = new ModelRendererTurbo(this, 42, 17, textureX, textureY); // Box 42
		attachmentModel[31] = new ModelRendererTurbo(this, 44, 4, textureX, textureY); // Box 43
		attachmentModel[32] = new ModelRendererTurbo(this, 41, 21, textureX, textureY); // Box 44
		attachmentModel[33] = new ModelRendererTurbo(this, 57, 17, textureX, textureY); // Box 50
		attachmentModel[34] = new ModelRendererTurbo(this, 33, 21, textureX, textureY); // Box 51
		attachmentModel[35] = new ModelRendererTurbo(this, 50, 1, textureX, textureY); // Box 52
		attachmentModel[36] = new ModelRendererTurbo(this, 9, 21, textureX, textureY); // Box 53
		attachmentModel[37] = new ModelRendererTurbo(this, 25, 21, textureX, textureY); // Box 54
		attachmentModel[38] = new ModelRendererTurbo(this, 17, 21, textureX, textureY); // Box 55
		attachmentModel[39] = new ModelRendererTurbo(this, 1, 21, textureX, textureY); // Box 56

		attachmentModel[0].addShapeBox(0F, -2F, 2F, 9, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 473
		attachmentModel[0].setRotationPoint(0F, -1F, -3F);

		attachmentModel[1].addShapeBox(0F, 0F, 5F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 476
		attachmentModel[1].setRotationPoint(0F, -1F, -3F);

		attachmentModel[2].addShapeBox(0F, 3F, 4F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 477
		attachmentModel[2].setRotationPoint(0F, -2F, -2F);

		attachmentModel[3].addShapeBox(0F, 1F, 3F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 489
		attachmentModel[3].setRotationPoint(9F, -2F, -2F);

		attachmentModel[4].addShapeBox(0F, 1F, 3F, 6, 1, 6, 0F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, -0.5F, -1.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 491
		attachmentModel[4].setRotationPoint(3F, 0F, -6F);

		attachmentModel[5].addShapeBox(0F, -2F, 4F, 3, 2, 1, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 495
		attachmentModel[5].setRotationPoint(0F, -1F, -3F);

		attachmentModel[6].addShapeBox(0F, 3F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 496
		attachmentModel[6].setRotationPoint(0F, -2F, -3F);

		attachmentModel[7].addShapeBox(0F, 3F, 2F, 10, 1, 2, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 497
		attachmentModel[7].setRotationPoint(0F, -1F, -3F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 498
		attachmentModel[8].setRotationPoint(0F, -1F, -3F);

		attachmentModel[9].addShapeBox(0F, -2F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 505
		attachmentModel[9].setRotationPoint(9F, -1F, -1F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 506
		attachmentModel[10].setRotationPoint(9F, -1F, -3F);

		attachmentModel[11].addShapeBox(0F, -2F, 0F, 1, 1, 4, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 23
		attachmentModel[11].setRotationPoint(9F, 3F, -2F);

		attachmentModel[12].addShapeBox(0F, -2F, 4F, 3, 2, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 24
		attachmentModel[12].setRotationPoint(0F, -1F, -6F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 25
		attachmentModel[13].setRotationPoint(9F, -2F, -3F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 26
		attachmentModel[14].setRotationPoint(9F, 1F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 27
		attachmentModel[15].setRotationPoint(9F, 1F, 1F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 28
		attachmentModel[16].setRotationPoint(9F, -2F, 1F);

		attachmentModel[17].addShapeBox(0F, -2F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 29
		attachmentModel[17].setRotationPoint(7F, -1F, -1F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 30
		attachmentModel[18].setRotationPoint(7F, -1F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 31
		attachmentModel[19].setRotationPoint(7F, -2F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 32
		attachmentModel[20].setRotationPoint(7F, 1F, -3F);

		attachmentModel[21].addShapeBox(0F, -2F, 0F, 1, 1, 4, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 33
		attachmentModel[21].setRotationPoint(7F, 3F, -2F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 34
		attachmentModel[22].setRotationPoint(7F, 1F, 1F);

		attachmentModel[23].addShapeBox(0F, 1F, 3F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[23].setRotationPoint(7F, -2F, -2F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 36
		attachmentModel[24].setRotationPoint(7F, -2F, 1F);

		attachmentModel[25].addShapeBox(0F, -2F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 37
		attachmentModel[25].setRotationPoint(5F, -1F, -1F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 38
		attachmentModel[26].setRotationPoint(5F, -1F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 39
		attachmentModel[27].setRotationPoint(5F, -2F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 40
		attachmentModel[28].setRotationPoint(5F, 1F, -3F);

		attachmentModel[29].addShapeBox(0F, -2F, 0F, 1, 1, 4, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 41
		attachmentModel[29].setRotationPoint(5F, 3F, -2F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 42
		attachmentModel[30].setRotationPoint(5F, 1F, 1F);

		attachmentModel[31].addShapeBox(0F, 1F, 3F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 43
		attachmentModel[31].setRotationPoint(5F, -2F, -2F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 44
		attachmentModel[32].setRotationPoint(5F, -2F, 1F);

		attachmentModel[33].addShapeBox(0F, -2F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F, 0F, -0.5F, 1.5F); // Box 50
		attachmentModel[33].setRotationPoint(3F, -1F, -1F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 51
		attachmentModel[34].setRotationPoint(3F, -2F, 1F);

		attachmentModel[35].addShapeBox(0F, 1F, 3F, 1, 2, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[35].setRotationPoint(3F, -2F, -2F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 53
		attachmentModel[36].setRotationPoint(3F, -1F, -3F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 54
		attachmentModel[37].setRotationPoint(3F, -2F, -3F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 55
		attachmentModel[38].setRotationPoint(3F, 1F, 1F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 56
		attachmentModel[39].setRotationPoint(3F, 1F, -3F);

		muzzleFlashPoint = new Vector3f(4F / 16F, 0F, 0F);

		flipAll();
	}
}