//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: CICWTS36UpperReceiver
// Model Creator: 
// Created on: 22.12.2019 - 19:48:03
// Last changed on: 22.12.2019 - 19:48:03

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelCICWTS36UpperReceiver extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelCICWTS36UpperReceiver() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[42];
		attachmentModel[0] = new ModelRendererTurbo(this, 81, 89, textureX, textureY); // Box 234
		attachmentModel[1] = new ModelRendererTurbo(this, 89, 105, textureX, textureY); // Box 264
		attachmentModel[2] = new ModelRendererTurbo(this, 145, 145, textureX, textureY); // Box 442
		attachmentModel[3] = new ModelRendererTurbo(this, 169, 145, textureX, textureY); // Box 448
		attachmentModel[4] = new ModelRendererTurbo(this, 121, 137, textureX, textureY); // Box 467
		attachmentModel[5] = new ModelRendererTurbo(this, 297, 137, textureX, textureY); // Box 469
		attachmentModel[6] = new ModelRendererTurbo(this, 121, 145, textureX, textureY); // Box 471
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 161, textureX, textureY); // Box 481
		attachmentModel[8] = new ModelRendererTurbo(this, 129, 161, textureX, textureY); // Box 482
		attachmentModel[9] = new ModelRendererTurbo(this, 257, 161, textureX, textureY); // Box 483
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 169, textureX, textureY); // Box 484
		attachmentModel[11] = new ModelRendererTurbo(this, 129, 169, textureX, textureY); // Box 485
		attachmentModel[12] = new ModelRendererTurbo(this, 281, 153, textureX, textureY); // Box 496
		attachmentModel[13] = new ModelRendererTurbo(this, 321, 153, textureX, textureY); // Box 497
		attachmentModel[14] = new ModelRendererTurbo(this, 425, 161, textureX, textureY); // Box 376
		attachmentModel[15] = new ModelRendererTurbo(this, 473, 153, textureX, textureY); // Box 377
		attachmentModel[16] = new ModelRendererTurbo(this, 265, 169, textureX, textureY); // Box 385
		attachmentModel[17] = new ModelRendererTurbo(this, 281, 169, textureX, textureY); // Box 387
		attachmentModel[18] = new ModelRendererTurbo(this, 97, 185, textureX, textureY); // Box 413
		attachmentModel[19] = new ModelRendererTurbo(this, 305, 129, textureX, textureY); // Box 417
		attachmentModel[20] = new ModelRendererTurbo(this, 313, 185, textureX, textureY); // Box 403
		attachmentModel[21] = new ModelRendererTurbo(this, 329, 185, textureX, textureY); // Box 404
		attachmentModel[22] = new ModelRendererTurbo(this, 337, 185, textureX, textureY); // Box 405
		attachmentModel[23] = new ModelRendererTurbo(this, 361, 185, textureX, textureY); // Box 406
		attachmentModel[24] = new ModelRendererTurbo(this, 377, 185, textureX, textureY); // Box 407
		attachmentModel[25] = new ModelRendererTurbo(this, 465, 185, textureX, textureY); // Box 457
		attachmentModel[26] = new ModelRendererTurbo(this, 289, 217, textureX, textureY); // Box 499
		attachmentModel[27] = new ModelRendererTurbo(this, 465, 217, textureX, textureY); // Box 463
		attachmentModel[28] = new ModelRendererTurbo(this, 1, 225, textureX, textureY); // Box 466
		attachmentModel[29] = new ModelRendererTurbo(this, 489, 217, textureX, textureY); // Box 471
		attachmentModel[30] = new ModelRendererTurbo(this, 41, 225, textureX, textureY); // Box 472
		attachmentModel[31] = new ModelRendererTurbo(this, 113, 225, textureX, textureY); // Box 434
		attachmentModel[32] = new ModelRendererTurbo(this, 401, 193, textureX, textureY); // Box 437
		attachmentModel[33] = new ModelRendererTurbo(this, 153, 225, textureX, textureY); // Box 438
		attachmentModel[34] = new ModelRendererTurbo(this, 177, 225, textureX, textureY); // Box 441
		attachmentModel[35] = new ModelRendererTurbo(this, 193, 225, textureX, textureY); // Box 442
		attachmentModel[36] = new ModelRendererTurbo(this, 217, 225, textureX, textureY); // Box 443
		attachmentModel[37] = new ModelRendererTurbo(this, 401, 225, textureX, textureY); // Box 443
		attachmentModel[38] = new ModelRendererTurbo(this, 433, 225, textureX, textureY); // Box 444
		attachmentModel[39] = new ModelRendererTurbo(this, 473, 225, textureX, textureY); // Box 446
		attachmentModel[40] = new ModelRendererTurbo(this, 1, 233, textureX, textureY); // Box 447
		attachmentModel[41] = new ModelRendererTurbo(this, 25, 233, textureX, textureY); // Box 448

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 46, 5, 1, 0F, -3F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, -3F, 0F, -3F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F); // Box 234
		attachmentModel[0].setRotationPoint(67F, 4F, 6F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 46, 5, 1, 0F, -3F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, -3F, 0F, 3F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F); // Box 264
		attachmentModel[1].setRotationPoint(67F, 4F, -7F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 442
		attachmentModel[2].setRotationPoint(113F, 5F, -6F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		attachmentModel[3].setRotationPoint(113F, 5F, 5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 467
		attachmentModel[4].setRotationPoint(123F, 5F, 5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 469
		attachmentModel[5].setRotationPoint(123F, 12F, -2F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 471
		attachmentModel[6].setRotationPoint(123F, 5F, -6F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 60, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 481
		attachmentModel[7].setRotationPoint(4F, 4F, 6F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 64, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 482
		attachmentModel[8].setRotationPoint(0F, 0F, 6F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 64, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 483
		attachmentModel[9].setRotationPoint(0F, -1F, -3F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 60, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 484
		attachmentModel[10].setRotationPoint(4F, 4F, -7F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 64, 4, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 485
		attachmentModel[11].setRotationPoint(0F, 0F, -7F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 15, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 496
		attachmentModel[12].setRotationPoint(113F, 5F, -5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 15, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 497
		attachmentModel[13].setRotationPoint(113F, 11F, -2F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 8, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 1F, 0F, -3F, 0F, 0F, -3F); // Box 376
		attachmentModel[14].setRotationPoint(113F, 2F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 1F, 0F, -3F); // Box 377
		attachmentModel[15].setRotationPoint(123F, 2F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 385
		attachmentModel[16].setRotationPoint(123F, 2F, 2F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 8, 3, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 1F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 387
		attachmentModel[17].setRotationPoint(113F, 2F, 2F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 15, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 413
		attachmentModel[18].setRotationPoint(113F, 5F, 4F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 3, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 417
		attachmentModel[19].setRotationPoint(112F, 6F, -6F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F); // Box 403
		attachmentModel[20].setRotationPoint(1F, 4F, 6F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 3, 3, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F); // Box 404
		attachmentModel[21].setRotationPoint(1F, 4F, -7F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 6, 2, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 1.5F); // Box 405
		attachmentModel[22].setRotationPoint(-3F, 0F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -1.5F, 0F, 0F, -1.5F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 406
		attachmentModel[23].setRotationPoint(-3F, 2F, 5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, -1.5F, 0F, 0F, -1.5F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 407
		attachmentModel[24].setRotationPoint(-3F, 2F, -6F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 2F, 0F, 0F, 0F, 0F, 1F); // Box 457
		attachmentModel[25].setRotationPoint(64F, -1F, 2F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 5, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 499
		attachmentModel[26].setRotationPoint(64F, -1F, -2F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 7, 5, 1, 0F, 0F, 0F, 0F, -1F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, -2.5F, 0F, -7F, 0F, 0F, -6.5F, 0F, -0.2143F, 0F, 0F, 0F); // Box 463
		attachmentModel[27].setRotationPoint(64F, 4F, 6F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 15, 4, 1, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -3F, 0F, -1F, -3F, 0F, -1F, 3F, 0F, -1F, 3F); // Box 466
		attachmentModel[28].setRotationPoint(113F, 2F, 1F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 1F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 1F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 471
		attachmentModel[29].setRotationPoint(123F, 9F, 2F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 5, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 1F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 1F, -1F, 0F); // Box 472
		attachmentModel[30].setRotationPoint(123F, 9F, -3F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 15, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, -1F, 3F, 0F, -1F, 3F, 0F, -1F, -3F, 0F, -1F, -3F); // Box 434
		attachmentModel[31].setRotationPoint(113F, 2F, -2F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 3, 2, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 437
		attachmentModel[32].setRotationPoint(112F, 6F, 4F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 7, 4, 1, 0F, 0F, 0F, 0F, -1F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, -3F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 3F); // Box 438
		attachmentModel[33].setRotationPoint(64F, 0F, 3F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 5, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 1F, 2F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 441
		attachmentModel[34].setRotationPoint(64F, -1F, -3F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 7, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, -3F); // Box 442
		attachmentModel[35].setRotationPoint(64F, 0F, -4F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 7, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, -3F, -1F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, -6.5F, 0F, -0.2143F, -4F, -2.5F, 1.5F, 0F, 0F, 0F); // Box 443
		attachmentModel[36].setRotationPoint(64F, 4F, -7F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 443
		attachmentModel[37].setRotationPoint(113F, 12F, -2F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 15, 4, 1, 0F, 0F, -1F, 3F, 0F, -1F, 3F, 0F, -1F, -3F, 0F, -1F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, -1F, -1F); // Box 444
		attachmentModel[38].setRotationPoint(113F, 8F, -2F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 1F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 1F, -1F, 0F, 0F, -1F, 0F); // Box 446
		attachmentModel[39].setRotationPoint(113F, 9F, -3F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, -3F, 1F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 447
		attachmentModel[40].setRotationPoint(113F, 9F, 2F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 15, 4, 1, 0F, 0F, -1F, -3F, 0F, -1F, -3F, 0F, -1F, 3F, 0F, -1F, 3F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 448
		attachmentModel[41].setRotationPoint(113F, 8F, 1F);

		flipAll();
	}
}