//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: EOTech EXPS3-0
// Model Creator: 
// Created on: 26.10.2019 - 21:10:21
// Last changed on: 26.10.2019 - 21:10:21

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAtSight;
import com.flansmod.client.tmt.ModelRendererTurbo;
import com.flansmod.common.vector.Vector3f;

public class ModelEOTechEXPS30 extends ModelAtSight //Same as Filename
{
	int textureX = 128;
	int textureY = 128;

	public ModelEOTechEXPS30() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[109];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 0
		attachmentModel[1] = new ModelRendererTurbo(this, 41, 1, textureX, textureY); // Box 1
		attachmentModel[2] = new ModelRendererTurbo(this, 65, 1, textureX, textureY); // Box 2
		attachmentModel[3] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 3
		attachmentModel[4] = new ModelRendererTurbo(this, 49, 17, textureX, textureY); // Box 4
		attachmentModel[5] = new ModelRendererTurbo(this, 89, 1, textureX, textureY); // Box 5
		attachmentModel[6] = new ModelRendererTurbo(this, 97, 17, textureX, textureY); // Box 6
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 7
		attachmentModel[8] = new ModelRendererTurbo(this, 105, 1, textureX, textureY); // Box 8
		attachmentModel[9] = new ModelRendererTurbo(this, 105, 25, textureX, textureY); // Box 9
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 10
		attachmentModel[11] = new ModelRendererTurbo(this, 33, 33, textureX, textureY); // Box 12
		attachmentModel[12] = new ModelRendererTurbo(this, 57, 33, textureX, textureY); // Box 13
		attachmentModel[13] = new ModelRendererTurbo(this, 81, 1, textureX, textureY); // Box 14
		attachmentModel[14] = new ModelRendererTurbo(this, 89, 1, textureX, textureY); // Box 15
		attachmentModel[15] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 16
		attachmentModel[16] = new ModelRendererTurbo(this, 49, 9, textureX, textureY); // Box 17
		attachmentModel[17] = new ModelRendererTurbo(this, 97, 33, textureX, textureY); // Box 18
		attachmentModel[18] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 19
		attachmentModel[19] = new ModelRendererTurbo(this, 1, 14, textureX, textureY); // Box 20
		attachmentModel[20] = new ModelRendererTurbo(this, 41, 25, textureX, textureY); // Box 23
		attachmentModel[21] = new ModelRendererTurbo(this, 41, 41, textureX, textureY); // Box 26
		attachmentModel[22] = new ModelRendererTurbo(this, 57, 41, textureX, textureY); // Box 27
		attachmentModel[23] = new ModelRendererTurbo(this, 73, 41, textureX, textureY); // Box 28
		attachmentModel[24] = new ModelRendererTurbo(this, 89, 41, textureX, textureY); // Box 29
		attachmentModel[25] = new ModelRendererTurbo(this, 105, 41, textureX, textureY); // Box 30
		attachmentModel[26] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 31
		attachmentModel[27] = new ModelRendererTurbo(this, 17, 49, textureX, textureY); // Box 32
		attachmentModel[28] = new ModelRendererTurbo(this, 33, 49, textureX, textureY); // Box 33
		attachmentModel[29] = new ModelRendererTurbo(this, 49, 49, textureX, textureY); // Box 34
		attachmentModel[30] = new ModelRendererTurbo(this, 81, 49, textureX, textureY); // Box 35
		attachmentModel[31] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 36
		attachmentModel[32] = new ModelRendererTurbo(this, 33, 57, textureX, textureY); // Box 37
		attachmentModel[33] = new ModelRendererTurbo(this, 49, 57, textureX, textureY); // Box 38
		attachmentModel[34] = new ModelRendererTurbo(this, 81, 57, textureX, textureY); // Box 39
		attachmentModel[35] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 40
		attachmentModel[36] = new ModelRendererTurbo(this, 97, 65, textureX, textureY); // Box 41
		attachmentModel[37] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 42
		attachmentModel[38] = new ModelRendererTurbo(this, 33, 73, textureX, textureY); // Box 43
		attachmentModel[39] = new ModelRendererTurbo(this, 113, 49, textureX, textureY); // Box 44
		attachmentModel[40] = new ModelRendererTurbo(this, 113, 57, textureX, textureY); // Box 45
		attachmentModel[41] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 47
		attachmentModel[42] = new ModelRendererTurbo(this, 121, 41, textureX, textureY); // Box 48
		attachmentModel[43] = new ModelRendererTurbo(this, 33, 65, textureX, textureY); // Box 49
		attachmentModel[44] = new ModelRendererTurbo(this, 41, 65, textureX, textureY); // Box 50
		attachmentModel[45] = new ModelRendererTurbo(this, 57, 73, textureX, textureY); // Box 51
		attachmentModel[46] = new ModelRendererTurbo(this, 65, 73, textureX, textureY); // Box 52
		attachmentModel[47] = new ModelRendererTurbo(this, 73, 73, textureX, textureY); // Box 53
		attachmentModel[48] = new ModelRendererTurbo(this, 81, 73, textureX, textureY); // Box 54
		attachmentModel[49] = new ModelRendererTurbo(this, 89, 73, textureX, textureY); // Box 55
		attachmentModel[50] = new ModelRendererTurbo(this, 97, 73, textureX, textureY); // Box 57
		attachmentModel[51] = new ModelRendererTurbo(this, 105, 73, textureX, textureY); // Box 58
		attachmentModel[52] = new ModelRendererTurbo(this, 113, 73, textureX, textureY); // Box 59
		attachmentModel[53] = new ModelRendererTurbo(this, 121, 73, textureX, textureY); // Box 60
		attachmentModel[54] = new ModelRendererTurbo(this, 1, 81, textureX, textureY); // Box 61
		attachmentModel[55] = new ModelRendererTurbo(this, 9, 81, textureX, textureY); // Box 62
		attachmentModel[56] = new ModelRendererTurbo(this, 33, 81, textureX, textureY); // Box 63
		attachmentModel[57] = new ModelRendererTurbo(this, 57, 81, textureX, textureY); // Box 64
		attachmentModel[58] = new ModelRendererTurbo(this, 89, 81, textureX, textureY); // Box 65
		attachmentModel[59] = new ModelRendererTurbo(this, 121, 81, textureX, textureY); // Box 66
		attachmentModel[60] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 67
		attachmentModel[61] = new ModelRendererTurbo(this, 9, 89, textureX, textureY); // Box 68
		attachmentModel[62] = new ModelRendererTurbo(this, 25, 89, textureX, textureY); // Box 69
		attachmentModel[63] = new ModelRendererTurbo(this, 41, 89, textureX, textureY); // Box 70
		attachmentModel[64] = new ModelRendererTurbo(this, 49, 89, textureX, textureY); // Box 71
		attachmentModel[65] = new ModelRendererTurbo(this, 73, 89, textureX, textureY); // Box 72
		attachmentModel[66] = new ModelRendererTurbo(this, 81, 89, textureX, textureY); // Box 73
		attachmentModel[67] = new ModelRendererTurbo(this, 89, 89, textureX, textureY); // Box 74
		attachmentModel[68] = new ModelRendererTurbo(this, 105, 89, textureX, textureY); // Box 75
		attachmentModel[69] = new ModelRendererTurbo(this, 121, 89, textureX, textureY); // Box 76
		attachmentModel[70] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 77
		attachmentModel[71] = new ModelRendererTurbo(this, 17, 97, textureX, textureY); // Box 78
		attachmentModel[72] = new ModelRendererTurbo(this, 25, 97, textureX, textureY); // Box 79
		attachmentModel[73] = new ModelRendererTurbo(this, 89, 89, textureX, textureY); // Box 80
		attachmentModel[74] = new ModelRendererTurbo(this, 57, 89, textureX, textureY); // Box 81
		attachmentModel[75] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 82
		attachmentModel[76] = new ModelRendererTurbo(this, 33, 97, textureX, textureY); // Box 83
		attachmentModel[77] = new ModelRendererTurbo(this, 33, 97, textureX, textureY); // Box 86
		attachmentModel[78] = new ModelRendererTurbo(this, 49, 97, textureX, textureY); // Box 87
		attachmentModel[79] = new ModelRendererTurbo(this, 121, 97, textureX, textureY); // Box 88
		attachmentModel[80] = new ModelRendererTurbo(this, 89, 105, textureX, textureY); // Box 89
		attachmentModel[81] = new ModelRendererTurbo(this, 97, 105, textureX, textureY); // Box 90
		attachmentModel[82] = new ModelRendererTurbo(this, 57, 97, textureX, textureY); // Box 91
		attachmentModel[83] = new ModelRendererTurbo(this, 105, 105, textureX, textureY); // Box 92
		attachmentModel[84] = new ModelRendererTurbo(this, 113, 105, textureX, textureY); // Box 93
		attachmentModel[85] = new ModelRendererTurbo(this, 17, 105, textureX, textureY); // Box 107
		attachmentModel[86] = new ModelRendererTurbo(this, 65, 113, textureX, textureY); // Box 108
		attachmentModel[87] = new ModelRendererTurbo(this, 89, 113, textureX, textureY); // Box 109
		attachmentModel[88] = new ModelRendererTurbo(this, 1, 121, textureX, textureY); // Box 110
		attachmentModel[89] = new ModelRendererTurbo(this, 33, 121, textureX, textureY); // Box 191
		attachmentModel[90] = new ModelRendererTurbo(this, 121, 105, textureX, textureY); // Box 192
		attachmentModel[91] = new ModelRendererTurbo(this, 73, 97, textureX, textureY); // Box 193
		attachmentModel[92] = new ModelRendererTurbo(this, 121, 113, textureX, textureY); // Box 194
		attachmentModel[93] = new ModelRendererTurbo(this, 81, 97, textureX, textureY); // Box 101
		attachmentModel[94] = new ModelRendererTurbo(this, 49, 121, textureX, textureY); // Box 102
		attachmentModel[95] = new ModelRendererTurbo(this, 57, 121, textureX, textureY); // Box 103
		attachmentModel[96] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 104
		attachmentModel[97] = new ModelRendererTurbo(this, 49, 105, textureX, textureY); // Box 105
		attachmentModel[98] = new ModelRendererTurbo(this, 98, 53, textureX, textureY); // Box 103
		attachmentModel[99] = new ModelRendererTurbo(this, 65, 121, textureX, textureY); // Box 104
		attachmentModel[100] = new ModelRendererTurbo(this, 73, 121, textureX, textureY); // Box 105
		attachmentModel[101] = new ModelRendererTurbo(this, 81, 121, textureX, textureY); // Box 104
		attachmentModel[102] = new ModelRendererTurbo(this, 33, 84, textureX, textureY); // Box 105
		attachmentModel[103] = new ModelRendererTurbo(this, 113, 121, textureX, textureY); // Box 106
		attachmentModel[104] = new ModelRendererTurbo(this, 121, 121, textureX, textureY); // Box 108
		attachmentModel[105] = new ModelRendererTurbo(this, 34, 25, textureX, textureY); // Box 109
		attachmentModel[106] = new ModelRendererTurbo(this, 40, 28, textureX, textureY); // Box 110
		attachmentModel[107] = new ModelRendererTurbo(this, 33, 28, textureX, textureY); // Box 111
		attachmentModel[108] = new ModelRendererTurbo(this, 1, 43, textureX, textureY); // Box 114

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 16, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 0
		attachmentModel[0].setRotationPoint(-11F, 0F, -5F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 12, 2, 2, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F); // Box 1
		attachmentModel[1].setRotationPoint(-11.5F, 0F, 3F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 1, 2, 9, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 2
		attachmentModel[2].setRotationPoint(-12F, -2F, -4.5F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 16, 2, 10, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 3
		attachmentModel[3].setRotationPoint(-11F, -2F, -5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 15, 5, 10, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F); // Box 4
		attachmentModel[4].setRotationPoint(-11F, -7F, -5F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 1, 4, 9, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 1F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, 1F, 0.5F, 0.5F); // Box 5
		attachmentModel[5].setRotationPoint(-11F, -6.5F, -4.5F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, -0.5F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -0.5F, 0.5F, 0F, 1.1667F, 0.5F, 0F, 0.6667F, 0.5F, 0F, 0.6667F, 0.5F, 0F, 1.1667F, 0.5F, 0F); // Box 6
		attachmentModel[6].setRotationPoint(-9F, -10F, -5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, -0.5F, 0.5F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -0.5F, 0.5F, 0F, 1.1667F, 0.5F, 0F, 0.6667F, 0.5F, 0F, 0.6667F, 0.5F, 0F, 1.1667F, 0.5F, 0F); // Box 7
		attachmentModel[7].setRotationPoint(-9F, -10F, 4F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 9, 2, 1, 0F, 0.1667F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.1667F, 0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F); // Box 8
		attachmentModel[8].setRotationPoint(-8F, -12F, 4F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 9, 2, 1, 0F, 0.1667F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0.1667F, 0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F); // Box 9
		attachmentModel[9].setRotationPoint(-8F, -12F, -5F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 9, 1, 8, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 10
		attachmentModel[10].setRotationPoint(-8F, -14F, -4F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 8, 2, 1, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0F, 1F, 0.5F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 12
		attachmentModel[11].setRotationPoint(-3F, 0F, -6.5F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 16, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 13
		attachmentModel[12].setRotationPoint(-12F, -1.5F, -6.5F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, -2F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 14
		attachmentModel[13].setRotationPoint(-12F, -7.5F, -6.5F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 15
		attachmentModel[14].setRotationPoint(-7F, -3.5F, -6.5F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 2, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 16
		attachmentModel[15].setRotationPoint(-3F, -5.5F, -6.5F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 3, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 17
		attachmentModel[16].setRotationPoint(-11F, -7.5F, -6.5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 12, 1, 1, 0F, 0F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 18
		attachmentModel[17].setRotationPoint(-10F, -9.5F, -6.5F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 19
		attachmentModel[18].setRotationPoint(-3F, -7.5F, -6.5F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 20
		attachmentModel[19].setRotationPoint(4F, -5.5F, -6.5F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 23
		attachmentModel[20].setRotationPoint(4F, -2.5F, -6.5F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 26
		attachmentModel[21].setRotationPoint(-11F, -8.5F, -6.5F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 27
		attachmentModel[22].setRotationPoint(-3F, -8.5F, -6.5F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 28
		attachmentModel[23].setRotationPoint(-7F, -5.5F, -6F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 29
		attachmentModel[24].setRotationPoint(-7F, -7.5F, -6F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 30
		attachmentModel[25].setRotationPoint(-7F, -6.5F, -5.5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 31
		attachmentModel[26].setRotationPoint(0F, -4.5F, -5.5F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 32
		attachmentModel[27].setRotationPoint(0F, -5.5F, -6F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 33
		attachmentModel[28].setRotationPoint(0F, -3.5F, -6F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0.4F, 0F, 0F, 0.4F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 1F, 0F, 0F); // Box 34
		attachmentModel[29].setRotationPoint(-9F, -11.5F, -6.5F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.4F, 0F, 0F, 0.4F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[30].setRotationPoint(-9F, -13.5F, -6.5F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 36
		attachmentModel[31].setRotationPoint(-9F, -15.5F, -4.5F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, -1F, -1F, 0F, -1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 37
		attachmentModel[32].setRotationPoint(-9F, -15.5F, 3.5F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 11, 1, 9, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 38
		attachmentModel[33].setRotationPoint(-9F, -15.5F, -4.5F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.4F, 0F, 0F, 0.4F, 0F, 0F, 0F, 0F, 0F); // Box 39
		attachmentModel[34].setRotationPoint(-9F, -13.5F, 5.5F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 0F, 0F, 0F, 0.4F, 0F, 0F, 0.4F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.8F, 0F, 0F, 0.8F, 0F, 0F, 1F, 0F, 0F); // Box 40
		attachmentModel[35].setRotationPoint(-9F, -11.5F, 5.5F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 13, 1, 1, 0F, 0F, 0F, 0F, -0.2F, 0F, 0F, -0.2F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 1F, 0F, 1F); // Box 41
		attachmentModel[36].setRotationPoint(-10F, -9.5F, 5.5F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 11, 3, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 42
		attachmentModel[37].setRotationPoint(-7F, -8.5F, 6.5F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 9, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 43
		attachmentModel[38].setRotationPoint(-4F, -5.5F, 6.5F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 44
		attachmentModel[39].setRotationPoint(-7F, -5.5F, 6.5F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 45
		attachmentModel[40].setRotationPoint(-11F, -4.5F, 6.5F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 47
		attachmentModel[41].setRotationPoint(-10F, -8.5F, 6.5F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 48
		attachmentModel[42].setRotationPoint(-10F, -5.5F, 6.5F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F); // Box 49
		attachmentModel[43].setRotationPoint(-8F, -7.5F, 6.5F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F); // Box 50
		attachmentModel[44].setRotationPoint(-11F, -7.5F, 6.5F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F); // Box 51
		attachmentModel[45].setRotationPoint(-11F, -8.5F, 6.5F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -1F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, -0.5F, -0.5F, 0F, -1F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 52
		attachmentModel[46].setRotationPoint(-8F, -8.5F, 6.5F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 53
		attachmentModel[47].setRotationPoint(-8F, -5.5F, 6.5F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -2F, 0F, 0F, 0F, -1F, 0F, -0.5F, -0.5F, 0F, -1F, 0F, 0F); // Box 54
		attachmentModel[48].setRotationPoint(-11F, -5.5F, 6.5F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F); // Box 55
		attachmentModel[49].setRotationPoint(-12F, -4F, 6.5F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -1F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, -0.5F, -0.5F, 0F, -1F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F); // Box 57
		attachmentModel[50].setRotationPoint(-5F, -4F, 6.5F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, -0.5F); // Box 58
		attachmentModel[51].setRotationPoint(-5F, -3F, 6.5F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, -0.5F, 0F, 0.5F, 0F); // Box 59
		attachmentModel[52].setRotationPoint(-12F, -4F, 6.5F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 60
		attachmentModel[53].setRotationPoint(-5F, -1.5F, 6.5F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, -1F, 0F, -0.5F, 0.5F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, -2F, 0F, 0F, 0F, -1F, 0F, -0.5F, -0.5F, 0F, -1F, 0F, 0F); // Box 61
		attachmentModel[54].setRotationPoint(-12F, -1.5F, 6.5F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 62
		attachmentModel[55].setRotationPoint(-4F, -1.5F, 6.5F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 63
		attachmentModel[56].setRotationPoint(-11.5F, -0.5F, 6.5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 12, 5, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 1F, 0F, 0.5F); // Box 64
		attachmentModel[57].setRotationPoint(-10.5F, -7.5F, 5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 13, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 65
		attachmentModel[58].setRotationPoint(-11.5F, -2.5F, 5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 66
		attachmentModel[59].setRotationPoint(-11F, -3F, 6.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 67
		attachmentModel[60].setRotationPoint(-7.5F, -3F, 6.5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F); // Box 68
		attachmentModel[61].setRotationPoint(-11F, -4F, 6.5F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 69
		attachmentModel[62].setRotationPoint(-11F, -1.5F, 6.5F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 2, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 70
		attachmentModel[63].setRotationPoint(-10F, -7.5F, 6.5F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 8, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 71
		attachmentModel[64].setRotationPoint(-11F, 0F, 6F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 72
		attachmentModel[65].setRotationPoint(-2.5F, 0F, 6F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 73
		attachmentModel[66].setRotationPoint(-2F, 0F, 6F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 1.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 74
		attachmentModel[67].setRotationPoint(-2F, -0.5F, 6F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 3, 1, 1, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F); // Box 75
		attachmentModel[68].setRotationPoint(-2F, 1F, 6F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 76
		attachmentModel[69].setRotationPoint(0.5F, 0F, 6F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 3, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F); // Box 77
		attachmentModel[70].setRotationPoint(-0.5F, -0.5F, 5F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 78
		attachmentModel[71].setRotationPoint(3F, 0F, 5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, -0.75F, 0F, 0F, -0.75F, 0F, -0.25F, 0F, 0F, 0F, 0F, 0F, 0F, -0.75F, 0F, 0F, -0.75F, 0F, -0.25F, 0F, 0F, 0F); // Box 79
		attachmentModel[72].setRotationPoint(2.75F, 0F, 5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 2, 5, 10, 0F, 0F, 0F, 0F, -0.5F, 0F, 0.5F, -0.5F, 0F, 1F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F, 1F, 0.5F, 0.5F, 0F); // Box 80
		attachmentModel[73].setRotationPoint(4F, -7F, -5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 2, 6, 11, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0F, 0F, -0.5F, 0F); // Box 81
		attachmentModel[74].setRotationPoint(5.5F, -7F, -5F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 2, 6, 11, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0.5F, 0.5F, 0.5F, 0.5F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 82
		attachmentModel[75].setRotationPoint(7.5F, -7F, -5F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 2, 5, 11, 0F, 0F, 1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0.5F, 0F, -1.5F, 0.5F, 0F, -1.5F, 0F, 0F, 0.5F, 0F); // Box 83
		attachmentModel[76].setRotationPoint(10F, -6F, -5F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 86
		attachmentModel[77].setRotationPoint(8.25F, -6.75F, -6.5F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 87
		attachmentModel[78].setRotationPoint(7.75F, -6.75F, -7F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 88
		attachmentModel[79].setRotationPoint(9.25F, -6.75F, -7F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0.5F, 0F, -1F, 0.5F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0.5F, 0F, -1F, 0.5F); // Box 89
		attachmentModel[80].setRotationPoint(5.75F, -5.75F, -7F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 2, 4, 1, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0.5F, 0F, 1F, 0.5F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0.5F, 0F, 1F, 0.5F); // Box 90
		attachmentModel[81].setRotationPoint(9.75F, -5.75F, -7F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 91
		attachmentModel[82].setRotationPoint(-10.6F, -7.5F, 4F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 92
		attachmentModel[83].setRotationPoint(-12.5F, -1.5F, 2.5F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 93
		attachmentModel[84].setRotationPoint(-12.5F, -1.5F, -4.5F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.1667F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.1667F, -0.5F, 0F); // Box 107
		attachmentModel[85].setRotationPoint(-8F, -13F, -5F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 9, 1, 1, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.1667F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0.1667F, -0.5F, 0F); // Box 108
		attachmentModel[86].setRotationPoint(-8F, -13F, 4F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 13, 1, 1, 0F, 0.1667F, -0.5F, 0F, -0.3333F, -0.5F, 0F, -0.3333F, -0.5F, 0F, 0.1667F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 109
		attachmentModel[87].setRotationPoint(-10F, -8F, 4F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 13, 1, 1, 0F, 0.1667F, -0.5F, 0F, -0.3333F, -0.5F, 0F, -0.3333F, -0.5F, 0F, 0.1667F, -0.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0.5F, 0F, 0.5F); // Box 110
		attachmentModel[88].setRotationPoint(-10F, -8F, -5F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F); // Box 191
		attachmentModel[89].setRotationPoint(3F, 0F, 3F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 1, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, 0F, -2.5F, 0F, 1F, -2.5F, 0F); // Box 192
		attachmentModel[90].setRotationPoint(0.5F, -0.5F, 4F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 1F, 0F, 0F); // Box 193
		attachmentModel[91].setRotationPoint(1.5F, 0F, 3F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 194
		attachmentModel[92].setRotationPoint(1.5F, 1F, 3F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, -0.5F, -0.5F, -0.5F, 0F, -0.5F, 0F); // Box 101
		attachmentModel[93].setRotationPoint(2.75F, -0.5F, 5F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 102
		attachmentModel[94].setRotationPoint(-4F, -3.5F, -6.5F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 103
		attachmentModel[95].setRotationPoint(-8F, -3.5F, -6.5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 104
		attachmentModel[96].setRotationPoint(-3F, -6.5F, -6.5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 105
		attachmentModel[97].setRotationPoint(-3F, -2.5F, -6.5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 1, 1, 10, 0F, 0F, 0.01F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.01F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 103
		attachmentModel[98].setRotationPoint(3F, -7F, -5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 1, 3, 1, 0F, 0F, -2F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -2F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F); // Box 104
		attachmentModel[99].setRotationPoint(3F, -8.5F, -6.5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, -2.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -2.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 105
		attachmentModel[100].setRotationPoint(-12F, -8F, 6.5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 12, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0.5F, -0.5F, 0.5F); // Box 104
		attachmentModel[101].setRotationPoint(-10.5F, -8F, 5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0.5F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0.5F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 105
		attachmentModel[102].setRotationPoint(-11.5F, -0.5F, -6.5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 106
		attachmentModel[103].setRotationPoint(-0.5F, 0.25F, -6.5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 108
		attachmentModel[104].setRotationPoint(0F, -0.25F, -7F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 109
		attachmentModel[105].setRotationPoint(0F, 0.75F, -7F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, 0F, -0.5F, -1F, 0F, -0.5F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -1F, 0F, 0F, -0.5F, 0F); // Box 110
		attachmentModel[106].setRotationPoint(-0.5F, 0.25F, -7.5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 2, 1, 1, 0F, 0F, -1F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -1F, 0F, -1F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, -1F, 0F, 0F); // Box 111
		attachmentModel[107].setRotationPoint(-0.5F, 0.25F, -7.5F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 16, 3, 1, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, 0F, 0.5F, 0.5F, 0F); // Box 114
		attachmentModel[108].setRotationPoint(-11F, -1.5F, -6F);

		pupilRefCenter.set(-3F /16F, 10F / 16F, 0F);
		reticleAt[0].set(4096F, 10F / 160F, 0F);
		reticleScale = 0.15F;

		reticleBorder = new Vector3f[6];
		reticleBorder[0] = new Vector3f(-3F / 160F, 12.5F / 160F, -4F / 160F);
		reticleBorder[1] = new Vector3f(-3F / 160F, 13F / 160F, -3.5F / 160F);
		reticleBorder[2] = new Vector3f(-3F / 160F, 13F / 160F, 3.5F / 160F);
		reticleBorder[3] = new Vector3f(-3F / 160F, 12.5F / 160F, 4F / 160F);
		reticleBorder[4] = new Vector3f(-3F / 160F, 7F / 160F, 4F / 160F);
		reticleBorder[5] = new Vector3f(-3F / 160F, 7F / 160F, -4F / 160F);

		flipAll();
	}
}