buildscript {
	repositories {
		maven {
			url = "https://repo1.maven.org/maven2/"
		}
		maven {
			name = "forge"
			url = "http://files.minecraftforge.net/maven"
		}
		maven {
			name = "sonatype"
			url = "https://oss.sonatype.org/content/repositories/snapshots/"
		}
		/*
		 * Mirror from Lss233.
		 * Try this if you always fail on downloading resources.
		 *
		maven {
			url = "https://lss233.com/artifactory/minecraft"
		}
		/* Mirror */
	}
	dependencies {
		classpath 'net.minecraftforge.gradle:ForgeGradle:1.2-SNAPSHOT'
	}
}
apply plugin: 'forge'

sourceCompatibility = 8
targetCompatibility = 8
version = "1.0"
group = "com.flansmod"
archivesBaseName = "Flan's Mod Ultimate 1.1 Modified"

minecraft {
	version = "1.7.10-10.13.4.1614-1.7.10"
	runDir = "run"

	replace "@MODID@", "flansmod"
	replace "@MOD_NAME@", project.archivesBaseName
	replace "@VERSION@", project.version
	replaceIn "FlansMod.java"
}

processResources {
	// This will ensure that this task is redone when the versions change.
	inputs.property "version", project.version
	inputs.property "mcversion", project.minecraft.version
	
	// Replace stuff in mcmod.info, nothing else
	from(sourceSets.main.resources.srcDirs) {
		include 'mcmod.info'
		
		// Replace version and mcversion
		expand 'version':project.version, 'mcversion':project.minecraft.version
	}
	
	// Copy everything else, thats not the mcmod.info
	from(sourceSets.main.resources.srcDirs) {
		exclude 'mcmod.info'
	}
}

version = "${project.minecraft.version}-${project.version}"

task octagonWeapons(type: Jar) {
	from(zipTree("${destinationDir}/${archiveName}")) {
		include 'com/flansmod/client/model/Octagon_Weapons/'
	}
	from 'run/Flan/Octagon Weapons'
	baseName = 'Octagon Weapons'
	appendix = 'Content Pack'
}

task octagonSpecialWeapons(type: Jar) {
	from(zipTree("${destinationDir}/${archiveName}")) {
		include 'com/flansmod/client/model/Octagon_Special_Weapons/'
	}
	from 'run/Flan/Octagon Special Weapons'
	baseName = 'Octagon Special Weapons'
	appendix = 'Content Pack'
}

task octagonSpecialBullets(type: Jar) {
	from(zipTree("${destinationDir}/${archiveName}")) {
		include 'com/flansmod/client/model/Octagon_Special_Bullets/'
	}
	from 'run/Flan/Octagon Special Bullets'
	baseName = 'Octagon Special Bullets'
	appendix = 'Content Pack'
}

task contentPacks() {
	dependsOn octagonWeapons
	dependsOn octagonSpecialWeapons
	dependsOn octagonSpecialBullets
}

//task clearOutput(type: Delete) {
//	delete 'build/output/'
//}
//
//task outputJar(type: Jar) {
//	destinationDir = new File("build/output/mods/")
//	from(zipTree("build/libs/${archiveName}")) {
//		exclude '**/Octagon Weapons/'
//		exclude '**/Octagon Special Weapons/'
//		exclude '**/Octagon Special Bullets/'
//	}
//}
//
//task outputPacks(type: Copy) {
//	from('build/libs/') {
//		include "**/*${version}.jar"
//		exclude "**/Flan's Mod*.jar"
//	}
//	into 'build/output/Flan/'
//}

build {
	dependsOn contentPacks
//	dependsOn clearOutput
//	dependsOn outputJar
//	dependsOn outputPacks
}
