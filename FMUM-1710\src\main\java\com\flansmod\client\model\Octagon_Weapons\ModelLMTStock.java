//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: LMT Stock
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelLMTStock extends ModelAttachment //Same as Filename
{
	int textureX = 256;
	int textureY = 128;

	public ModelLMTStock() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[79];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 248
		attachmentModel[1] = new ModelRendererTurbo(this, 113, 1, textureX, textureY); // Box 249
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 250
		attachmentModel[3] = new ModelRendererTurbo(this, 113, 9, textureX, textureY); // Box 251
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 17, textureX, textureY); // Box 252
		attachmentModel[5] = new ModelRendererTurbo(this, 105, 17, textureX, textureY); // Box 253
		attachmentModel[6] = new ModelRendererTurbo(this, 105, 25, textureX, textureY); // Box 254
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 255
		attachmentModel[8] = new ModelRendererTurbo(this, 217, 9, textureX, textureY); // Box 256
		attachmentModel[9] = new ModelRendererTurbo(this, 105, 33, textureX, textureY); // Box 257
		attachmentModel[10] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 258
		attachmentModel[11] = new ModelRendererTurbo(this, 225, 1, textureX, textureY); // Box 259
		attachmentModel[12] = new ModelRendererTurbo(this, 233, 1, textureX, textureY); // Box 260
		attachmentModel[13] = new ModelRendererTurbo(this, 201, 17, textureX, textureY); // Box 261
		attachmentModel[14] = new ModelRendererTurbo(this, 201, 25, textureX, textureY); // Box 262
		attachmentModel[15] = new ModelRendererTurbo(this, 241, 1, textureX, textureY); // Box 263
		attachmentModel[16] = new ModelRendererTurbo(this, 249, 1, textureX, textureY); // Box 264
		attachmentModel[17] = new ModelRendererTurbo(this, 233, 17, textureX, textureY); // Box 265
		attachmentModel[18] = new ModelRendererTurbo(this, 193, 33, textureX, textureY); // Box 266
		attachmentModel[19] = new ModelRendererTurbo(this, 97, 41, textureX, textureY); // Box 267
		attachmentModel[20] = new ModelRendererTurbo(this, 225, 33, textureX, textureY); // Box 268
		attachmentModel[21] = new ModelRendererTurbo(this, 129, 41, textureX, textureY); // Box 269
		attachmentModel[22] = new ModelRendererTurbo(this, 233, 25, textureX, textureY); // Box 270
		attachmentModel[23] = new ModelRendererTurbo(this, 161, 41, textureX, textureY); // Box 273
		attachmentModel[24] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 274
		attachmentModel[25] = new ModelRendererTurbo(this, 1, 49, textureX, textureY); // Box 275
		attachmentModel[26] = new ModelRendererTurbo(this, 241, 17, textureX, textureY); // Box 276
		attachmentModel[27] = new ModelRendererTurbo(this, 33, 49, textureX, textureY); // Box 278
		attachmentModel[28] = new ModelRendererTurbo(this, 145, 49, textureX, textureY); // Box 284
		attachmentModel[29] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 286
		attachmentModel[30] = new ModelRendererTurbo(this, 33, 57, textureX, textureY); // Box 287
		attachmentModel[31] = new ModelRendererTurbo(this, 225, 41, textureX, textureY); // Box 288
		attachmentModel[32] = new ModelRendererTurbo(this, 113, 57, textureX, textureY); // Box 289
		attachmentModel[33] = new ModelRendererTurbo(this, 241, 41, textureX, textureY); // Box 290
		attachmentModel[34] = new ModelRendererTurbo(this, 153, 57, textureX, textureY); // Box 291
		attachmentModel[35] = new ModelRendererTurbo(this, 169, 57, textureX, textureY); // Box 292
		attachmentModel[36] = new ModelRendererTurbo(this, 193, 57, textureX, textureY); // Box 293
		attachmentModel[37] = new ModelRendererTurbo(this, 209, 57, textureX, textureY); // Box 294
		attachmentModel[38] = new ModelRendererTurbo(this, 225, 57, textureX, textureY); // Box 295
		attachmentModel[39] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 296
		attachmentModel[40] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 297
		attachmentModel[41] = new ModelRendererTurbo(this, 241, 57, textureX, textureY); // Box 298
		attachmentModel[42] = new ModelRendererTurbo(this, 145, 65, textureX, textureY); // Box 299
		attachmentModel[43] = new ModelRendererTurbo(this, 225, 17, textureX, textureY); // Box 325
		attachmentModel[44] = new ModelRendererTurbo(this, 217, 41, textureX, textureY); // Box 326
		attachmentModel[45] = new ModelRendererTurbo(this, 161, 65, textureX, textureY); // Box 327
		attachmentModel[46] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 328
		attachmentModel[47] = new ModelRendererTurbo(this, 209, 65, textureX, textureY); // Box 329
		attachmentModel[48] = new ModelRendererTurbo(this, 225, 65, textureX, textureY); // Box 330
		attachmentModel[49] = new ModelRendererTurbo(this, 241, 65, textureX, textureY); // Box 331
		attachmentModel[50] = new ModelRendererTurbo(this, 33, 73, textureX, textureY); // Box 333
		attachmentModel[51] = new ModelRendererTurbo(this, 49, 73, textureX, textureY); // Box 338
		attachmentModel[52] = new ModelRendererTurbo(this, 25, 65, textureX, textureY); // Box 339
		attachmentModel[53] = new ModelRendererTurbo(this, 73, 73, textureX, textureY); // Box 340
		attachmentModel[54] = new ModelRendererTurbo(this, 153, 73, textureX, textureY); // Box 533
		attachmentModel[55] = new ModelRendererTurbo(this, 89, 73, textureX, textureY); // Box 534
		attachmentModel[56] = new ModelRendererTurbo(this, 193, 73, textureX, textureY); // Box 79
		attachmentModel[57] = new ModelRendererTurbo(this, 209, 73, textureX, textureY); // Box 80
		attachmentModel[58] = new ModelRendererTurbo(this, 225, 73, textureX, textureY); // Box 81
		attachmentModel[59] = new ModelRendererTurbo(this, 33, 81, textureX, textureY); // Box 84
		attachmentModel[60] = new ModelRendererTurbo(this, 241, 73, textureX, textureY); // Box 85
		attachmentModel[61] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 86
		attachmentModel[62] = new ModelRendererTurbo(this, 57, 81, textureX, textureY); // Box 87
		attachmentModel[63] = new ModelRendererTurbo(this, 153, 81, textureX, textureY); // Box 88
		attachmentModel[64] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 89
		attachmentModel[65] = new ModelRendererTurbo(this, 97, 97, textureX, textureY); // Box 90
		attachmentModel[66] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 91
		attachmentModel[67] = new ModelRendererTurbo(this, 249, 41, textureX, textureY); // Box 92
		attachmentModel[68] = new ModelRendererTurbo(this, 249, 49, textureX, textureY); // Box 79
		attachmentModel[69] = new ModelRendererTurbo(this, 113, 65, textureX, textureY); // Box 80
		attachmentModel[70] = new ModelRendererTurbo(this, 121, 65, textureX, textureY); // Box 81
		attachmentModel[71] = new ModelRendererTurbo(this, 217, 65, textureX, textureY); // Box 82
		attachmentModel[72] = new ModelRendererTurbo(this, 233, 65, textureX, textureY); // Box 83
		attachmentModel[73] = new ModelRendererTurbo(this, 249, 65, textureX, textureY); // Box 84
		attachmentModel[74] = new ModelRendererTurbo(this, 41, 73, textureX, textureY); // Box 85
		attachmentModel[75] = new ModelRendererTurbo(this, 73, 81, textureX, textureY); // Box 86
		attachmentModel[76] = new ModelRendererTurbo(this, 201, 97, textureX, textureY); // Box 87
		attachmentModel[77] = new ModelRendererTurbo(this, 105, 105, textureX, textureY); // Box 88
		attachmentModel[78] = new ModelRendererTurbo(this, 1, 113, textureX, textureY); // Box 89

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 51, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 248
		attachmentModel[0].setRotationPoint(-51F, -5F, -2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 51, 3, 1, 0F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 249
		attachmentModel[1].setRotationPoint(-51F, -5F, 4F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 51, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 250
		attachmentModel[2].setRotationPoint(-51F, -2F, 3.99999999999999F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 47, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F); // Box 251
		attachmentModel[3].setRotationPoint(-50F, 5F, 1.99999999999999F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 47, 6, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 252
		attachmentModel[4].setRotationPoint(-50F, 3F, 3.99999999999999F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 45, 2, 2, 0F, 2F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 253
		attachmentModel[5].setRotationPoint(-48F, 5F, 5.99999999999999F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 44, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 254
		attachmentModel[6].setRotationPoint(-48F, 5F, 7.99999999999999F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 47, 5, 1, 0F, 2F, 0F, 3F, 2F, 0F, 3F, 2F, 0F, -3F, 2F, 0F, -3F, -1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F); // Box 255
		attachmentModel[7].setRotationPoint(-49F, 0F, 8F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 15, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 256
		attachmentModel[8].setRotationPoint(-19F, 9F, 1.99999999999999F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 45, 2, 1, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 2F, 0F, -2F); // Box 257
		attachmentModel[9].setRotationPoint(-49F, 7F, 8F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 47, 3, 1, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 258
		attachmentModel[10].setRotationPoint(-50F, 0F, 3.99999999999999F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 5, 1, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 259
		attachmentModel[11].setRotationPoint(-7F, 9F, 0.999999999999988F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 1, 5, 1, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 260
		attachmentModel[12].setRotationPoint(-7F, 9F, -2F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 261
		attachmentModel[13].setRotationPoint(-27F, 13F, -1.99999999999999F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 11, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 262
		attachmentModel[14].setRotationPoint(-19F, 9F, 0.999999999999988F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 263
		attachmentModel[15].setRotationPoint(-8F, 9F, 0.999999999999988F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 264
		attachmentModel[16].setRotationPoint(-8F, 12F, 0.999999999999988F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 1, 5, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 265
		attachmentModel[17].setRotationPoint(-19F, 9F, -1F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 10, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 266
		attachmentModel[18].setRotationPoint(-22F, 14F, -3F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 9, 1, 6, 0F, 1F, -7F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, -7F, 0F, 0F, 7F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 7F, 0F); // Box 267
		attachmentModel[19].setRotationPoint(-32F, 14F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 12, 1, 1, 0F, 0.3333F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.3333F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 268
		attachmentModel[20].setRotationPoint(-23F, 13F, -3F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 12, 2, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, -0.6667F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -0.6667F, 0F, 0F); // Box 269
		attachmentModel[21].setRotationPoint(-24F, 11F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 9, 4, 1, 0F, 0F, -7F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -7F, 0F, 0F, 7F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 7F, 0F); // Box 270
		attachmentModel[22].setRotationPoint(-33F, 10F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 9, 4, 1, 0F, 0F, -7F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -7F, 0F, 0F, 7F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 7F, 0F); // Box 273
		attachmentModel[23].setRotationPoint(-33F, 10F, 1.99999999999999F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 12, 1, 4, 0F, 0F, 0F, 0F, 0F, 8F, 0F, 0F, 8F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -8F, 0F, 0F, -8F, 0F, 0F, 0F, 0F); // Box 274
		attachmentModel[24].setRotationPoint(-39F, 21F, -2F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 12, 1, 4, 0F, 0F, 0F, 1F, 0F, 8F, 0F, 0F, 8F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, -8F, 0F, 0F, -8F, 0F, 0F, 0F, 1F); // Box 275
		attachmentModel[25].setRotationPoint(-51F, 29F, -2F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F); // Box 276
		attachmentModel[26].setRotationPoint(-3F, 5F, 3.99999999999999F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 51, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 278
		attachmentModel[27].setRotationPoint(-51F, -2F, -5F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 44, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 284
		attachmentModel[28].setRotationPoint(-48F, 5F, -9F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 15, 1, 4, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 286
		attachmentModel[29].setRotationPoint(-19F, 9F, -6F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 32, 1, 12, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 287
		attachmentModel[30].setRotationPoint(-51F, 9F, -6F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 6, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 288
		attachmentModel[31].setRotationPoint(-51F, 10F, -0.500000000000004F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 15, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 289
		attachmentModel[32].setRotationPoint(-45F, 10F, -0.500000000000004F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 2, 11, 1, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 1F, 0F); // Box 290
		attachmentModel[33].setRotationPoint(-46F, 14F, -0.500000000000004F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 4, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 2F, 0F); // Box 291
		attachmentModel[34].setRotationPoint(-30F, 10F, -0.500000000000004F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 7, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 292
		attachmentModel[35].setRotationPoint(-26F, 10F, -0.499999999999996F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 3, 10, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 293
		attachmentModel[36].setRotationPoint(-51F, 15F, -0.500000000000004F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 5, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1.5F, 0F, 0F, -1.5F, 0F, 0F, 1F, 0F); // Box 294
		attachmentModel[37].setRotationPoint(-51F, 25F, -0.500000000000004F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 3, 3, 4, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 3F, 1F, 0F, 3F); // Box 295
		attachmentModel[38].setRotationPoint(-54F, -5F, -2F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 4, 12, 10, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 296
		attachmentModel[39].setRotationPoint(-55F, -2F, -5F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 4, 19, 8, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 297
		attachmentModel[40].setRotationPoint(-55F, 10F, -4F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 3, 3, 4, 0F, 1F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 2F, 1F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 298
		attachmentModel[41].setRotationPoint(-54F, 29F, -2F);

		attachmentModel[42].addShapeBox(-1.5F, 0F, -1.5F, 3, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 299
		attachmentModel[42].setRotationPoint(-17F, 15F, 0F);
		attachmentModel[42].rotateAngleY = 0.78539816F;

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 325
		attachmentModel[43].setRotationPoint(-8F, 9F, -2F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 1, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 326
		attachmentModel[44].setRotationPoint(-8F, 12F, -2F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 11, 5, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 327
		attachmentModel[45].setRotationPoint(-19F, 9F, -2F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 328
		attachmentModel[46].setRotationPoint(-42.5F, 16F, -1.5F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 329
		attachmentModel[47].setRotationPoint(-42.5F, 19F, -1.5F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F); // Box 330
		attachmentModel[48].setRotationPoint(-41F, 17.5F, -1.5F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F); // Box 331
		attachmentModel[49].setRotationPoint(-44F, 17.5F, -1.5F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, -0.5F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1F, 0F, 0.5F, -1F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0.5F, -1F, 0F); // Box 333
		attachmentModel[50].setRotationPoint(-43.5F, 17F, -1.5F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 10, 5, 1, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0.5F, 3F, 0F); // Box 338
		attachmentModel[51].setRotationPoint(-40F, 14F, -0.5F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 3, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 339
		attachmentModel[52].setRotationPoint(-43.5F, 14F, -0.500000000000004F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 3, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, 0F); // Box 340
		attachmentModel[53].setRotationPoint(-43.5F, 19.5F, -0.5F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 14, 1, 2, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.4F, 0F, 0F, 0.4F, 0F, 0F, 0F, 0F, 0F); // Box 533
		attachmentModel[54].setRotationPoint(-18F, 9F, -1F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 12, 1, 2, 0F, 0F, 0F, 0F, 0.6F, 0F, 0F, 0.6F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 534
		attachmentModel[55].setRotationPoint(-18F, 13F, -1F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, -0.5F, 1F, 0F, -0.5F, 1F, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 0F, -0.5F, -0.5F, 0F); // Box 79
		attachmentModel[56].setRotationPoint(-41.5F, 17F, -1.5F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, -0.5F, -0.5F, 0F, 0.5F, -1F, 0F, 0.5F, -1F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 1F, 0F, -0.5F, 1F, 0F, 0F, 0F, 0F); // Box 80
		attachmentModel[57].setRotationPoint(-41.5F, 18F, -1.5F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0.5F, -1F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0.5F, -1F, 0F, -0.5F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 1F, 0F); // Box 81
		attachmentModel[58].setRotationPoint(-43.5F, 18F, -1.5F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 7, 1, 3, 0F, -1F, 0F, 0F, 1F, -7F, 0F, 1F, -7F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 7F, 0F, 0F, 7F, 0F, 0F, 0F, 0F); // Box 84
		attachmentModel[59].setRotationPoint(-51F, 9F, -1.5F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 2, 1, 3, 0F, -1F, 0F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F); // Box 85
		attachmentModel[60].setRotationPoint(-41F, 19F, -1.5F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 51, 3, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, -1F, 2F, 0F, -1F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 86
		attachmentModel[61].setRotationPoint(-51F, -5F, -5F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F); // Box 87
		attachmentModel[62].setRotationPoint(-3F, 5F, -6F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 47, 6, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 88
		attachmentModel[63].setRotationPoint(-50F, 3F, -6F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 45, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 2F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 2F, 2F, 0F); // Box 89
		attachmentModel[64].setRotationPoint(-48F, 5F, -8F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 47, 2, 2, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 90
		attachmentModel[65].setRotationPoint(-50F, 5F, -4F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 47, 3, 1, 0F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 91
		attachmentModel[66].setRotationPoint(-50F, 0F, -5F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 92
		attachmentModel[67].setRotationPoint(-48F, 15F, -0.5F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F); // Box 79
		attachmentModel[68].setRotationPoint(-47F, 15F, -0.5F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 80
		attachmentModel[69].setRotationPoint(-47F, 24F, -0.5F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 81
		attachmentModel[70].setRotationPoint(-48F, 24F, -0.5F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F); // Box 82
		attachmentModel[71].setRotationPoint(-45F, 12F, -0.5F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 83
		attachmentModel[72].setRotationPoint(-45F, 13F, -0.5F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F); // Box 84
		attachmentModel[73].setRotationPoint(-31F, 13F, -0.5F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -1F, -0.5F, 0F, 0.5F, -0.5F, 0F, 0.5F, -0.5F, 0F, -1F, -0.5F, 0F); // Box 85
		attachmentModel[74].setRotationPoint(-31F, 12F, -0.5F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 12, 2, 1, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, -0.6667F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -0.6667F, 0F, 0F); // Box 86
		attachmentModel[75].setRotationPoint(-24F, 11F, 2F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 12, 1, 1, 0F, 0.3333F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.3333F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 87
		attachmentModel[76].setRotationPoint(-23F, 13F, 2F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 47, 5, 1, 0F, 2F, 0F, -3F, 2F, 0F, -3F, 2F, 0F, 3F, 2F, 0F, 3F, -1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -1F, 0F, 0F); // Box 88
		attachmentModel[77].setRotationPoint(-49F, 0F, -9F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 45, 2, 1, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 2F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 2F, 0F, 2F); // Box 89
		attachmentModel[78].setRotationPoint(-49F, 7F, -9F);

		flipAll();
	}
}