//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2021 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: HK416
// Model Creator: 
// Created on: 22.06.2019 - 16:45:55
// Last changed on: 22.06.2019 - 16:45:55

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelHK416BarrelNut extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 256;

	public ModelHK416BarrelNut() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[32];
		attachmentModel[0] = new ModelRendererTurbo(this, 337, 161, textureX, textureY); // Box 587
		attachmentModel[1] = new ModelRendererTurbo(this, 369, 161, textureX, textureY); // Box 589
		attachmentModel[2] = new ModelRendererTurbo(this, 393, 161, textureX, textureY); // Box 590
		attachmentModel[3] = new ModelRendererTurbo(this, 417, 161, textureX, textureY); // Box 591
		attachmentModel[4] = new ModelRendererTurbo(this, 449, 89, textureX, textureY); // Box 595
		attachmentModel[5] = new ModelRendererTurbo(this, 121, 105, textureX, textureY); // Box 597
		attachmentModel[6] = new ModelRendererTurbo(this, 449, 161, textureX, textureY); // Box 599
		attachmentModel[7] = new ModelRendererTurbo(this, 473, 161, textureX, textureY); // Box 602
		attachmentModel[8] = new ModelRendererTurbo(this, 481, 161, textureX, textureY); // Box 611
		attachmentModel[9] = new ModelRendererTurbo(this, 1, 168, textureX, textureY); // Box 612
		attachmentModel[10] = new ModelRendererTurbo(this, 17, 168, textureX, textureY); // Box 613
		attachmentModel[11] = new ModelRendererTurbo(this, 33, 168, textureX, textureY); // Box 615
		attachmentModel[12] = new ModelRendererTurbo(this, 56, 168, textureX, textureY); // Box 617
		attachmentModel[13] = new ModelRendererTurbo(this, 273, 209, textureX, textureY); // Box 586
		attachmentModel[14] = new ModelRendererTurbo(this, 297, 209, textureX, textureY); // Box 587
		attachmentModel[15] = new ModelRendererTurbo(this, 320, 208, textureX, textureY); // Box 588
		attachmentModel[16] = new ModelRendererTurbo(this, 337, 209, textureX, textureY); // Box 589
		attachmentModel[17] = new ModelRendererTurbo(this, 360, 208, textureX, textureY); // Box 590
		attachmentModel[18] = new ModelRendererTurbo(this, 376, 208, textureX, textureY); // Box 591
		attachmentModel[19] = new ModelRendererTurbo(this, 457, 209, textureX, textureY); // Box 592
		attachmentModel[20] = new ModelRendererTurbo(this, 481, 209, textureX, textureY); // Box 593
		attachmentModel[21] = new ModelRendererTurbo(this, 505, 209, textureX, textureY); // Box 596
		attachmentModel[22] = new ModelRendererTurbo(this, 9, 217, textureX, textureY); // Box 598
		attachmentModel[23] = new ModelRendererTurbo(this, 25, 217, textureX, textureY); // Box 600
		attachmentModel[24] = new ModelRendererTurbo(this, 153, 105, textureX, textureY); // Box 581
		attachmentModel[25] = new ModelRendererTurbo(this, 497, 209, textureX, textureY); // Box 582
		attachmentModel[26] = new ModelRendererTurbo(this, 17, 217, textureX, textureY); // Box 583
		attachmentModel[27] = new ModelRendererTurbo(this, 489, 209, textureX, textureY); // Box 584
		attachmentModel[28] = new ModelRendererTurbo(this, 465, 161, textureX, textureY); // Box 585
		attachmentModel[29] = new ModelRendererTurbo(this, 401, 89, textureX, textureY); // Box 586
		attachmentModel[30] = new ModelRendererTurbo(this, 457, 161, textureX, textureY); // Box 587
		attachmentModel[31] = new ModelRendererTurbo(this, 1, 217, textureX, textureY); // Box 588

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 587
		attachmentModel[0].setRotationPoint(0F, -6F, -2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 589
		attachmentModel[1].setRotationPoint(0F, -2F, 5F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 590
		attachmentModel[2].setRotationPoint(0F, -2F, -6F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 8, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 591
		attachmentModel[3].setRotationPoint(0F, 5F, -2F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F); // Box 595
		attachmentModel[4].setRotationPoint(8F, 5F, -2F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F); // Box 597
		attachmentModel[5].setRotationPoint(8F, -6F, -2F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F); // Box 599
		attachmentModel[6].setRotationPoint(8F, -2F, 5F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 602
		attachmentModel[7].setRotationPoint(8F, -2F, -6F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 5, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 611
		attachmentModel[8].setRotationPoint(10F, -6F, -2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 5, 4, 2, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F); // Box 612
		attachmentModel[9].setRotationPoint(10F, -6F, 2F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 5, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 613
		attachmentModel[10].setRotationPoint(10F, -2F, 4F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 5, 2, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 615
		attachmentModel[11].setRotationPoint(10F, 4F, -2F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 5, 4, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 617
		attachmentModel[12].setRotationPoint(10F, -2F, -6F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F); // Box 586
		attachmentModel[13].setRotationPoint(0F, -6F, 2F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 587
		attachmentModel[14].setRotationPoint(0F, -6F, -3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 5, 4, 2, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -2F, -1F, 0F, -2F, -1F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F); // Box 588
		attachmentModel[15].setRotationPoint(10F, -6F, -3F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, -1F, 0F); // Box 589
		attachmentModel[16].setRotationPoint(0F, 2F, -3F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 5, 4, 2, 0F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -2F, -1F, 0F, -2F, -1F); // Box 590
		attachmentModel[17].setRotationPoint(10F, 2F, -3F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 5, 4, 2, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 591
		attachmentModel[18].setRotationPoint(10F, 2F, 2F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 8, 4, 1, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 3F, 0F, 0F, 3F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 592
		attachmentModel[19].setRotationPoint(0F, 2F, 2F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -1F, -1F, 0F, 0F, -1F, 0F, 0F, -3F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 3F); // Box 593
		attachmentModel[20].setRotationPoint(8F, -6F, 2F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, -1F, 0F, -1F, -1F, 0F, -2F, 0F, 0F, -1F, 0F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -3F); // Box 596
		attachmentModel[21].setRotationPoint(8F, -6F, -3F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, -1F, -1F, 0F, -2F, 0F, 0F, -1F, 0F); // Box 598
		attachmentModel[22].setRotationPoint(8F, 2F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, -3F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, -1F, 0F, 0F, -2F, 0F, 0F, -1F, -1F, 0F, 0F, -1F); // Box 600
		attachmentModel[23].setRotationPoint(8F, 2F, 2F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 581
		attachmentModel[24].setRotationPoint(9F, -5F, -2F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 582
		attachmentModel[25].setRotationPoint(9F, -2F, -5F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, -1F, -1F, 0F, 0F, -1F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, -2F, 0F, 0F, -2F); // Box 583
		attachmentModel[26].setRotationPoint(9F, -6F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -1F, 0F, -1F, -1F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 3F, 0F, 0F, 2F); // Box 584
		attachmentModel[27].setRotationPoint(9F, -6F, 2F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F); // Box 585
		attachmentModel[28].setRotationPoint(9F, -2F, 4F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F); // Box 586
		attachmentModel[29].setRotationPoint(9F, 4F, -2F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 3F, 0F, 0F, 2F, 0F, -2F, 0F, 0F, -2F, 0F, 0F, 0F, -1F, 0F, -1F, -1F); // Box 587
		attachmentModel[30].setRotationPoint(9F, 2F, 2F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 4, 1, 0F, 0F, 0F, 2F, 0F, 0F, 3F, 0F, 0F, -2F, 0F, 0F, -2F, 0F, -1F, -1F, 0F, 0F, -1F, 0F, -2F, 0F, 0F, -2F, 0F); // Box 588
		attachmentModel[31].setRotationPoint(9F, 2F, -3F);
	}
}