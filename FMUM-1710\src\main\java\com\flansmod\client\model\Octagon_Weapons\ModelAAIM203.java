//This File was created with the Minecraft-SMP Modelling Toolbox 2.3.0.0
// Copyright (C) 2020 Minecraft-SMP.de
// This file is for Flan's Flying Mod Version 4.0.x+

// Model: AAI M203
// Model Creator: 
// Created on: 06.01.2020 - 14:40:11
// Last changed on: 06.01.2020 - 14:40:11

package com.flansmod.client.model.Octagon_Weapons; //Path where the model is located

import com.flansmod.client.model.ModelAttachment;
import com.flansmod.client.tmt.ModelRendererTurbo;

public class ModelAAIM203 extends ModelAttachment //Same as Filename
{
	int textureX = 512;
	int textureY = 512;

	public ModelAAIM203() //Same as Filename
	{
		attachmentModel = new ModelRendererTurbo[156];
		attachmentModel[0] = new ModelRendererTurbo(this, 1, 1, textureX, textureY); // Box 0
		attachmentModel[1] = new ModelRendererTurbo(this, 185, 1, textureX, textureY); // Box 1
		attachmentModel[2] = new ModelRendererTurbo(this, 1, 9, textureX, textureY); // Box 2
		attachmentModel[3] = new ModelRendererTurbo(this, 177, 17, textureX, textureY); // Box 3
		attachmentModel[4] = new ModelRendererTurbo(this, 1, 25, textureX, textureY); // Box 4
		attachmentModel[5] = new ModelRendererTurbo(this, 177, 25, textureX, textureY); // Box 5
		attachmentModel[6] = new ModelRendererTurbo(this, 1, 33, textureX, textureY); // Box 6
		attachmentModel[7] = new ModelRendererTurbo(this, 1, 41, textureX, textureY); // Box 7
		attachmentModel[8] = new ModelRendererTurbo(this, 361, 1, textureX, textureY); // Box 8
		attachmentModel[9] = new ModelRendererTurbo(this, 361, 9, textureX, textureY); // Box 9
		attachmentModel[10] = new ModelRendererTurbo(this, 353, 17, textureX, textureY); // Box 10
		attachmentModel[11] = new ModelRendererTurbo(this, 489, 1, textureX, textureY); // Box 11
		attachmentModel[12] = new ModelRendererTurbo(this, 361, 25, textureX, textureY); // Box 12
		attachmentModel[13] = new ModelRendererTurbo(this, 353, 33, textureX, textureY); // Box 13
		attachmentModel[14] = new ModelRendererTurbo(this, 185, 41, textureX, textureY); // Box 14
		attachmentModel[15] = new ModelRendererTurbo(this, 289, 41, textureX, textureY); // Box 15
		attachmentModel[16] = new ModelRendererTurbo(this, 393, 41, textureX, textureY); // Box 16
		attachmentModel[17] = new ModelRendererTurbo(this, 393, 49, textureX, textureY); // Box 17
		attachmentModel[18] = new ModelRendererTurbo(this, 1, 57, textureX, textureY); // Box 18
		attachmentModel[19] = new ModelRendererTurbo(this, 97, 57, textureX, textureY); // Box 19
		attachmentModel[20] = new ModelRendererTurbo(this, 457, 17, textureX, textureY); // Box 20
		attachmentModel[21] = new ModelRendererTurbo(this, 145, 57, textureX, textureY); // Box 21
		attachmentModel[22] = new ModelRendererTurbo(this, 489, 9, textureX, textureY); // Box 22
		attachmentModel[23] = new ModelRendererTurbo(this, 481, 25, textureX, textureY); // Box 23
		attachmentModel[24] = new ModelRendererTurbo(this, 169, 57, textureX, textureY); // Box 24
		attachmentModel[25] = new ModelRendererTurbo(this, 193, 57, textureX, textureY); // Box 25
		attachmentModel[26] = new ModelRendererTurbo(this, 249, 57, textureX, textureY); // Box 26
		attachmentModel[27] = new ModelRendererTurbo(this, 305, 57, textureX, textureY); // Box 27
		attachmentModel[28] = new ModelRendererTurbo(this, 337, 57, textureX, textureY); // Box 28
		attachmentModel[29] = new ModelRendererTurbo(this, 177, 1, textureX, textureY); // Box 29
		attachmentModel[30] = new ModelRendererTurbo(this, 505, 1, textureX, textureY); // Box 30
		attachmentModel[31] = new ModelRendererTurbo(this, 177, 9, textureX, textureY); // Box 31
		attachmentModel[32] = new ModelRendererTurbo(this, 489, 33, textureX, textureY); // Box 32
		attachmentModel[33] = new ModelRendererTurbo(this, 489, 41, textureX, textureY); // Box 33
		attachmentModel[34] = new ModelRendererTurbo(this, 497, 49, textureX, textureY); // Box 34
		attachmentModel[35] = new ModelRendererTurbo(this, 369, 57, textureX, textureY); // Box 35
		attachmentModel[36] = new ModelRendererTurbo(this, 385, 57, textureX, textureY); // Box 36
		attachmentModel[37] = new ModelRendererTurbo(this, 401, 57, textureX, textureY); // Box 37
		attachmentModel[38] = new ModelRendererTurbo(this, 417, 57, textureX, textureY); // Box 38
		attachmentModel[39] = new ModelRendererTurbo(this, 433, 57, textureX, textureY); // Box 39
		attachmentModel[40] = new ModelRendererTurbo(this, 449, 57, textureX, textureY); // Box 40
		attachmentModel[41] = new ModelRendererTurbo(this, 465, 57, textureX, textureY); // Box 41
		attachmentModel[42] = new ModelRendererTurbo(this, 481, 57, textureX, textureY); // Box 42
		attachmentModel[43] = new ModelRendererTurbo(this, 457, 17, textureX, textureY); // Box 43
		attachmentModel[44] = new ModelRendererTurbo(this, 465, 17, textureX, textureY); // Box 44
		attachmentModel[45] = new ModelRendererTurbo(this, 481, 17, textureX, textureY); // Box 45
		attachmentModel[46] = new ModelRendererTurbo(this, 505, 25, textureX, textureY); // Box 46
		attachmentModel[47] = new ModelRendererTurbo(this, 505, 33, textureX, textureY); // Box 47
		attachmentModel[48] = new ModelRendererTurbo(this, 505, 41, textureX, textureY); // Box 48
		attachmentModel[49] = new ModelRendererTurbo(this, 177, 49, textureX, textureY); // Box 49
		attachmentModel[50] = new ModelRendererTurbo(this, 497, 57, textureX, textureY); // Box 50
		attachmentModel[51] = new ModelRendererTurbo(this, 505, 57, textureX, textureY); // Box 51
		attachmentModel[52] = new ModelRendererTurbo(this, 1, 65, textureX, textureY); // Box 52
		attachmentModel[53] = new ModelRendererTurbo(this, 9, 65, textureX, textureY); // Box 53
		attachmentModel[54] = new ModelRendererTurbo(this, 17, 65, textureX, textureY); // Box 54
		attachmentModel[55] = new ModelRendererTurbo(this, 25, 65, textureX, textureY); // Box 55
		attachmentModel[56] = new ModelRendererTurbo(this, 33, 65, textureX, textureY); // Box 56
		attachmentModel[57] = new ModelRendererTurbo(this, 41, 65, textureX, textureY); // Box 57
		attachmentModel[58] = new ModelRendererTurbo(this, 49, 65, textureX, textureY); // Box 58
		attachmentModel[59] = new ModelRendererTurbo(this, 57, 65, textureX, textureY); // Box 59
		attachmentModel[60] = new ModelRendererTurbo(this, 65, 65, textureX, textureY); // Box 60
		attachmentModel[61] = new ModelRendererTurbo(this, 73, 65, textureX, textureY); // Box 61
		attachmentModel[62] = new ModelRendererTurbo(this, 81, 65, textureX, textureY); // Box 62
		attachmentModel[63] = new ModelRendererTurbo(this, 89, 65, textureX, textureY); // Box 63
		attachmentModel[64] = new ModelRendererTurbo(this, 297, 65, textureX, textureY); // Box 64
		attachmentModel[65] = new ModelRendererTurbo(this, 305, 65, textureX, textureY); // Box 65
		attachmentModel[66] = new ModelRendererTurbo(this, 313, 65, textureX, textureY); // Box 66
		attachmentModel[67] = new ModelRendererTurbo(this, 321, 65, textureX, textureY); // Box 67
		attachmentModel[68] = new ModelRendererTurbo(this, 361, 65, textureX, textureY); // Box 68
		attachmentModel[69] = new ModelRendererTurbo(this, 377, 65, textureX, textureY); // Box 69
		attachmentModel[70] = new ModelRendererTurbo(this, 393, 65, textureX, textureY); // Box 70
		attachmentModel[71] = new ModelRendererTurbo(this, 409, 65, textureX, textureY); // Box 71
		attachmentModel[72] = new ModelRendererTurbo(this, 425, 65, textureX, textureY); // Box 72
		attachmentModel[73] = new ModelRendererTurbo(this, 441, 65, textureX, textureY); // Box 73
		attachmentModel[74] = new ModelRendererTurbo(this, 457, 65, textureX, textureY); // Box 74
		attachmentModel[75] = new ModelRendererTurbo(this, 473, 65, textureX, textureY); // Box 75
		attachmentModel[76] = new ModelRendererTurbo(this, 489, 65, textureX, textureY); // Box 76
		attachmentModel[77] = new ModelRendererTurbo(this, 1, 73, textureX, textureY); // Box 77
		attachmentModel[78] = new ModelRendererTurbo(this, 17, 73, textureX, textureY); // Box 78
		attachmentModel[79] = new ModelRendererTurbo(this, 33, 73, textureX, textureY); // Box 79
		attachmentModel[80] = new ModelRendererTurbo(this, 49, 73, textureX, textureY); // Box 80
		attachmentModel[81] = new ModelRendererTurbo(this, 65, 73, textureX, textureY); // Box 81
		attachmentModel[82] = new ModelRendererTurbo(this, 81, 73, textureX, textureY); // Box 82
		attachmentModel[83] = new ModelRendererTurbo(this, 97, 73, textureX, textureY); // Box 83
		attachmentModel[84] = new ModelRendererTurbo(this, 113, 73, textureX, textureY); // Box 84
		attachmentModel[85] = new ModelRendererTurbo(this, 129, 73, textureX, textureY); // Box 85
		attachmentModel[86] = new ModelRendererTurbo(this, 177, 73, textureX, textureY); // Box 86
		attachmentModel[87] = new ModelRendererTurbo(this, 193, 73, textureX, textureY); // Box 87
		attachmentModel[88] = new ModelRendererTurbo(this, 209, 73, textureX, textureY); // Box 88
		attachmentModel[89] = new ModelRendererTurbo(this, 225, 73, textureX, textureY); // Box 89
		attachmentModel[90] = new ModelRendererTurbo(this, 241, 73, textureX, textureY); // Box 90
		attachmentModel[91] = new ModelRendererTurbo(this, 249, 73, textureX, textureY); // Box 91
		attachmentModel[92] = new ModelRendererTurbo(this, 505, 9, textureX, textureY); // Box 92
		attachmentModel[93] = new ModelRendererTurbo(this, 257, 81, textureX, textureY); // Box 93
		attachmentModel[94] = new ModelRendererTurbo(this, 369, 81, textureX, textureY); // Box 94
		attachmentModel[95] = new ModelRendererTurbo(this, 281, 73, textureX, textureY); // Box 95
		attachmentModel[96] = new ModelRendererTurbo(this, 297, 73, textureX, textureY); // Box 96
		attachmentModel[97] = new ModelRendererTurbo(this, 313, 73, textureX, textureY); // Box 97
		attachmentModel[98] = new ModelRendererTurbo(this, 337, 73, textureX, textureY); // Box 98
		attachmentModel[99] = new ModelRendererTurbo(this, 353, 73, textureX, textureY); // Box 99
		attachmentModel[100] = new ModelRendererTurbo(this, 145, 81, textureX, textureY); // Box 100
		attachmentModel[101] = new ModelRendererTurbo(this, 161, 81, textureX, textureY); // Box 101
		attachmentModel[102] = new ModelRendererTurbo(this, 1, 89, textureX, textureY); // Box 102
		attachmentModel[103] = new ModelRendererTurbo(this, 17, 89, textureX, textureY); // Box 103
		attachmentModel[104] = new ModelRendererTurbo(this, 33, 89, textureX, textureY); // Box 104
		attachmentModel[105] = new ModelRendererTurbo(this, 49, 89, textureX, textureY); // Box 105
		attachmentModel[106] = new ModelRendererTurbo(this, 65, 89, textureX, textureY); // Box 106
		attachmentModel[107] = new ModelRendererTurbo(this, 81, 89, textureX, textureY); // Box 107
		attachmentModel[108] = new ModelRendererTurbo(this, 97, 89, textureX, textureY); // Box 108
		attachmentModel[109] = new ModelRendererTurbo(this, 113, 89, textureX, textureY); // Box 109
		attachmentModel[110] = new ModelRendererTurbo(this, 129, 89, textureX, textureY); // Box 110
		attachmentModel[111] = new ModelRendererTurbo(this, 145, 89, textureX, textureY); // Box 111
		attachmentModel[112] = new ModelRendererTurbo(this, 161, 89, textureX, textureY); // Box 112
		attachmentModel[113] = new ModelRendererTurbo(this, 177, 89, textureX, textureY); // Box 113
		attachmentModel[114] = new ModelRendererTurbo(this, 193, 89, textureX, textureY); // Box 114
		attachmentModel[115] = new ModelRendererTurbo(this, 209, 89, textureX, textureY); // Box 115
		attachmentModel[116] = new ModelRendererTurbo(this, 225, 89, textureX, textureY); // Box 116
		attachmentModel[117] = new ModelRendererTurbo(this, 241, 89, textureX, textureY); // Box 117
		attachmentModel[118] = new ModelRendererTurbo(this, 257, 89, textureX, textureY); // Box 118
		attachmentModel[119] = new ModelRendererTurbo(this, 265, 89, textureX, textureY); // Box 119
		attachmentModel[120] = new ModelRendererTurbo(this, 377, 89, textureX, textureY); // Box 120
		attachmentModel[121] = new ModelRendererTurbo(this, 1, 97, textureX, textureY); // Box 121
		attachmentModel[122] = new ModelRendererTurbo(this, 169, 97, textureX, textureY); // Box 122
		attachmentModel[123] = new ModelRendererTurbo(this, 401, 89, textureX, textureY); // Box 123
		attachmentModel[124] = new ModelRendererTurbo(this, 185, 57, textureX, textureY); // Box 124
		attachmentModel[125] = new ModelRendererTurbo(this, 353, 25, textureX, textureY); // Box 125
		attachmentModel[126] = new ModelRendererTurbo(this, 433, 89, textureX, textureY); // Box 126
		attachmentModel[127] = new ModelRendererTurbo(this, 241, 57, textureX, textureY); // Box 127
		attachmentModel[128] = new ModelRendererTurbo(this, 297, 57, textureX, textureY); // Box 128
		attachmentModel[129] = new ModelRendererTurbo(this, 177, 41, textureX, textureY); // Box 129
		attachmentModel[130] = new ModelRendererTurbo(this, 505, 65, textureX, textureY); // Box 130
		attachmentModel[131] = new ModelRendererTurbo(this, 329, 57, textureX, textureY); // Box 131
		attachmentModel[132] = new ModelRendererTurbo(this, 361, 57, textureX, textureY); // Box 132
		attachmentModel[133] = new ModelRendererTurbo(this, 329, 65, textureX, textureY); // Box 133
		attachmentModel[134] = new ModelRendererTurbo(this, 369, 65, textureX, textureY); // Box 134
		attachmentModel[135] = new ModelRendererTurbo(this, 385, 65, textureX, textureY); // Box 135
		attachmentModel[136] = new ModelRendererTurbo(this, 313, 97, textureX, textureY); // Box 136
		attachmentModel[137] = new ModelRendererTurbo(this, 1, 105, textureX, textureY); // Box 137
		attachmentModel[138] = new ModelRendererTurbo(this, 457, 89, textureX, textureY); // Box 138
		attachmentModel[139] = new ModelRendererTurbo(this, 401, 65, textureX, textureY); // Box 139
		attachmentModel[140] = new ModelRendererTurbo(this, 417, 65, textureX, textureY); // Box 140
		attachmentModel[141] = new ModelRendererTurbo(this, 441, 97, textureX, textureY); // Box 141
		attachmentModel[142] = new ModelRendererTurbo(this, 433, 65, textureX, textureY); // Box 142
		attachmentModel[143] = new ModelRendererTurbo(this, 457, 97, textureX, textureY); // Box 143
		attachmentModel[144] = new ModelRendererTurbo(this, 473, 97, textureX, textureY); // Box 144
		attachmentModel[145] = new ModelRendererTurbo(this, 449, 65, textureX, textureY); // Box 145
		attachmentModel[146] = new ModelRendererTurbo(this, 465, 65, textureX, textureY); // Box 146
		attachmentModel[147] = new ModelRendererTurbo(this, 481, 65, textureX, textureY); // Box 147
		attachmentModel[148] = new ModelRendererTurbo(this, 497, 65, textureX, textureY); // Box 148
		attachmentModel[149] = new ModelRendererTurbo(this, 9, 73, textureX, textureY); // Box 149
		attachmentModel[150] = new ModelRendererTurbo(this, 25, 73, textureX, textureY); // Box 150
		attachmentModel[151] = new ModelRendererTurbo(this, 41, 73, textureX, textureY); // Box 151
		attachmentModel[152] = new ModelRendererTurbo(this, 57, 73, textureX, textureY); // Box 152
		attachmentModel[153] = new ModelRendererTurbo(this, 73, 73, textureX, textureY); // Box 153
		attachmentModel[154] = new ModelRendererTurbo(this, 89, 73, textureX, textureY); // Box 154
		attachmentModel[155] = new ModelRendererTurbo(this, 105, 73, textureX, textureY); // Box 155

		attachmentModel[0].addShapeBox(0F, 0F, 0F, 84, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 0
		attachmentModel[0].setRotationPoint(24.5F, 3F, -2F);

		attachmentModel[1].addShapeBox(0F, 0F, 0F, 84, 4, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, -1F, 0F, 0F, -1F); // Box 1
		attachmentModel[1].setRotationPoint(24.5F, 3F, 2F);

		attachmentModel[2].addShapeBox(0F, 0F, 0F, 84, 4, 4, 0F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -3F, 0F, 0F, -3F, 0F); // Box 2
		attachmentModel[2].setRotationPoint(24.5F, 3F, -6F);

		attachmentModel[3].addShapeBox(0F, 0F, 0F, 84, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 3
		attachmentModel[3].setRotationPoint(24.5F, 7F, 5F);

		attachmentModel[4].addShapeBox(0F, 0F, 0F, 84, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 4
		attachmentModel[4].setRotationPoint(24.5F, 7F, -6F);

		attachmentModel[5].addShapeBox(0F, 0F, 0F, 84, 4, 4, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 5
		attachmentModel[5].setRotationPoint(24.5F, 11F, -6F);

		attachmentModel[6].addShapeBox(0F, 0F, 0F, 84, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 6
		attachmentModel[6].setRotationPoint(24.5F, 14F, -2F);

		attachmentModel[7].addShapeBox(0F, 0F, 0F, 84, 4, 4, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4F, 0F, 0F, -4F, 0F); // Box 7
		attachmentModel[7].setRotationPoint(24.5F, 11F, 2F);

		attachmentModel[8].addShapeBox(0F, 0F, 0F, 63, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 8
		attachmentModel[8].setRotationPoint(24.5F, 1F, -2F);

		attachmentModel[9].addShapeBox(0F, 0F, 0F, 63, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 9
		attachmentModel[9].setRotationPoint(24.5F, 1F, 1F);

		attachmentModel[10].addShapeBox(0F, 0F, 0F, 47, 1, 4, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F); // Box 10
		attachmentModel[10].setRotationPoint(24.5F, 15F, -2F);

		attachmentModel[11].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 11
		attachmentModel[11].setRotationPoint(25.5F, 16F, -3F);

		attachmentModel[12].addShapeBox(0F, 0F, 0F, 47, 4, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 12
		attachmentModel[12].setRotationPoint(24.5F, 7F, 6F);

		attachmentModel[13].addShapeBox(0F, 0F, 0F, 47, 4, 1, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 13
		attachmentModel[13].setRotationPoint(24.5F, 7F, -7F);

		attachmentModel[14].addShapeBox(0F, 0F, 0F, 47, 4, 4, 0F, 0F, -3F, 1F, 0F, -3F, 1F, 0F, 1F, -1F, 0F, 1F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -4F, 0F, 0F, -4F, 0F); // Box 14
		attachmentModel[14].setRotationPoint(24.5F, 12F, 3F);

		attachmentModel[15].addShapeBox(0F, 0F, 0F, 47, 4, 4, 0F, 0F, 1F, -1F, 0F, 1F, -1F, 0F, -3F, 1F, 0F, -3F, 1F, 0F, -4F, 0F, 0F, -4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 15
		attachmentModel[15].setRotationPoint(24.5F, 12F, -7F);

		attachmentModel[16].addShapeBox(0F, 0F, 0F, 47, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 16
		attachmentModel[16].setRotationPoint(24.5F, 3F, -5F);

		attachmentModel[17].addShapeBox(0F, 0F, 0F, 47, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 1F, -1F, 0F, 1F, -1F); // Box 17
		attachmentModel[17].setRotationPoint(24.5F, 3F, 4F);

		attachmentModel[18].addShapeBox(0F, 0F, 0F, 47, 1, 1, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 18
		attachmentModel[18].setRotationPoint(24.5F, 3F, 4F);

		attachmentModel[19].addShapeBox(0F, 0F, 0F, 22, 2, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 19
		attachmentModel[19].setRotationPoint(4F, 0.5F, -3F);

		attachmentModel[20].addShapeBox(0F, 0F, 0F, 3, 3, 14, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 20
		attachmentModel[20].setRotationPoint(21.5F, 6F, -7F);

		attachmentModel[21].addShapeBox(0F, 0F, 0F, 3, 3, 14, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 21
		attachmentModel[21].setRotationPoint(21.5F, 9F, -7F);

		attachmentModel[22].addShapeBox(0F, 0F, 0F, 3, 4, 6, 0F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 22
		attachmentModel[22].setRotationPoint(21.5F, 12F, -3F);

		attachmentModel[23].addShapeBox(0F, 0F, 0F, 10, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F); // Box 23
		attachmentModel[23].setRotationPoint(78F, 0F, -3.5F);

		attachmentModel[24].addShapeBox(0F, 0F, 0F, 4, 4, 6, 0F, 7.7F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 7.7F, 0F, 0F, 0.5F, 0F, 4F, 0F, 0F, 4F, 0F, 0F, 4F, 0.5F, 0F, 4F); // Box 24
		attachmentModel[24].setRotationPoint(20.5F, 2F, -3F);

		attachmentModel[25].addShapeBox(0F, 0F, 0F, 19, 8, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 25
		attachmentModel[25].setRotationPoint(2.5F, 2F, -3F);

		attachmentModel[26].addShapeBox(0F, 0F, 0F, 18, 2, 6, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 26
		attachmentModel[26].setRotationPoint(3.5F, 10F, -3F);

		attachmentModel[27].addShapeBox(0F, 0F, 0F, 8, 1, 6, 0F, 10.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 10.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 27
		attachmentModel[27].setRotationPoint(14F, 12.5F, -3F);

		attachmentModel[28].addShapeBox(0F, 0F, 0F, 6, 2, 6, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 28
		attachmentModel[28].setRotationPoint(16F, 13.5F, -3F);

		attachmentModel[29].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F); // Box 29
		attachmentModel[29].setRotationPoint(2F, 3F, -0.75F);

		attachmentModel[30].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0.5F, 0F, -0.5F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 30
		attachmentModel[30].setRotationPoint(2F, 3F, -0.75F);

		attachmentModel[31].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F); // Box 31
		attachmentModel[31].setRotationPoint(2F, 2F, -0.75F);

		attachmentModel[32].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 32
		attachmentModel[32].setRotationPoint(29.5F, 16F, -3F);

		attachmentModel[33].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 33
		attachmentModel[33].setRotationPoint(33.5F, 16F, -3F);

		attachmentModel[34].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 34
		attachmentModel[34].setRotationPoint(37.5F, 16F, -3F);

		attachmentModel[35].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 35
		attachmentModel[35].setRotationPoint(41.5F, 16F, -3F);

		attachmentModel[36].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 36
		attachmentModel[36].setRotationPoint(45.5F, 16F, -3F);

		attachmentModel[37].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 37
		attachmentModel[37].setRotationPoint(49.5F, 16F, -3F);

		attachmentModel[38].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 38
		attachmentModel[38].setRotationPoint(53.5F, 16F, -3F);

		attachmentModel[39].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 39
		attachmentModel[39].setRotationPoint(57.5F, 16F, -3F);

		attachmentModel[40].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 40
		attachmentModel[40].setRotationPoint(61.5F, 16F, -3F);

		attachmentModel[41].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 41
		attachmentModel[41].setRotationPoint(65.5F, 16F, -3F);

		attachmentModel[42].addShapeBox(0F, 0F, 0F, 1, 1, 6, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 42
		attachmentModel[42].setRotationPoint(69.5F, 16F, -3F);

		attachmentModel[43].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 43
		attachmentModel[43].setRotationPoint(25.5F, 6F, 7F);

		attachmentModel[44].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 44
		attachmentModel[44].setRotationPoint(29.5F, 6F, 7F);

		attachmentModel[45].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 45
		attachmentModel[45].setRotationPoint(33.5F, 6F, 7F);

		attachmentModel[46].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 46
		attachmentModel[46].setRotationPoint(37.5F, 6F, 7F);

		attachmentModel[47].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 47
		attachmentModel[47].setRotationPoint(41.5F, 6F, 7F);

		attachmentModel[48].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 48
		attachmentModel[48].setRotationPoint(45.5F, 6F, 7F);

		attachmentModel[49].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 49
		attachmentModel[49].setRotationPoint(49.5F, 6F, 7F);

		attachmentModel[50].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 50
		attachmentModel[50].setRotationPoint(53.5F, 6F, 7F);

		attachmentModel[51].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 51
		attachmentModel[51].setRotationPoint(57.5F, 6F, 7F);

		attachmentModel[52].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 52
		attachmentModel[52].setRotationPoint(61.5F, 6F, 7F);

		attachmentModel[53].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 53
		attachmentModel[53].setRotationPoint(65.5F, 6F, 7F);

		attachmentModel[54].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 54
		attachmentModel[54].setRotationPoint(69.5F, 6F, 7F);

		attachmentModel[55].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 55
		attachmentModel[55].setRotationPoint(69.5F, 6F, -8F);

		attachmentModel[56].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 56
		attachmentModel[56].setRotationPoint(65.5F, 6F, -8F);

		attachmentModel[57].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 57
		attachmentModel[57].setRotationPoint(61.5F, 6F, -8F);

		attachmentModel[58].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 58
		attachmentModel[58].setRotationPoint(57.5F, 6F, -8F);

		attachmentModel[59].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 59
		attachmentModel[59].setRotationPoint(53.5F, 6F, -8F);

		attachmentModel[60].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 60
		attachmentModel[60].setRotationPoint(49.5F, 6F, -8F);

		attachmentModel[61].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 61
		attachmentModel[61].setRotationPoint(45.5F, 6F, -8F);

		attachmentModel[62].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 62
		attachmentModel[62].setRotationPoint(41.5F, 6F, -8F);

		attachmentModel[63].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 63
		attachmentModel[63].setRotationPoint(37.5F, 6F, -8F);

		attachmentModel[64].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 64
		attachmentModel[64].setRotationPoint(33.5F, 6F, -8F);

		attachmentModel[65].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 65
		attachmentModel[65].setRotationPoint(29.5F, 6F, -8F);

		attachmentModel[66].addShapeBox(0F, 0F, 0F, 1, 6, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 66
		attachmentModel[66].setRotationPoint(25.5F, 6F, -8F);

		attachmentModel[67].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 67
		attachmentModel[67].setRotationPoint(25.5F, 12F, 3F);

		attachmentModel[68].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 68
		attachmentModel[68].setRotationPoint(29.5F, 12F, 3F);

		attachmentModel[69].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 69
		attachmentModel[69].setRotationPoint(33.5F, 12F, 3F);

		attachmentModel[70].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 70
		attachmentModel[70].setRotationPoint(41.5F, 12F, 3F);

		attachmentModel[71].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 71
		attachmentModel[71].setRotationPoint(37.5F, 12F, 3F);

		attachmentModel[72].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 72
		attachmentModel[72].setRotationPoint(45.5F, 12F, 3F);

		attachmentModel[73].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 73
		attachmentModel[73].setRotationPoint(53.5F, 12F, 3F);

		attachmentModel[74].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 74
		attachmentModel[74].setRotationPoint(49.5F, 12F, 3F);

		attachmentModel[75].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 75
		attachmentModel[75].setRotationPoint(57.5F, 12F, 3F);

		attachmentModel[76].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 76
		attachmentModel[76].setRotationPoint(65.5F, 12F, 3F);

		attachmentModel[77].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 77
		attachmentModel[77].setRotationPoint(61.5F, 12F, 3F);

		attachmentModel[78].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, -4F, 0F, 1F, -4F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -5F, 0F, 0F, -5F, 0F); // Box 78
		attachmentModel[78].setRotationPoint(69.5F, 12F, 3F);

		attachmentModel[79].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 79
		attachmentModel[79].setRotationPoint(65.5F, 12F, -8F);

		attachmentModel[80].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 80
		attachmentModel[80].setRotationPoint(61.5F, 12F, -8F);

		attachmentModel[81].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 81
		attachmentModel[81].setRotationPoint(69.5F, 12F, -8F);

		attachmentModel[82].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 82
		attachmentModel[82].setRotationPoint(57.5F, 12F, -8F);

		attachmentModel[83].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 83
		attachmentModel[83].setRotationPoint(53.5F, 12F, -8F);

		attachmentModel[84].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 84
		attachmentModel[84].setRotationPoint(49.5F, 12F, -8F);

		attachmentModel[85].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 85
		attachmentModel[85].setRotationPoint(45.5F, 12F, -8F);

		attachmentModel[86].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 86
		attachmentModel[86].setRotationPoint(41.5F, 12F, -8F);

		attachmentModel[87].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 87
		attachmentModel[87].setRotationPoint(37.5F, 12F, -8F);

		attachmentModel[88].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 88
		attachmentModel[88].setRotationPoint(33.5F, 12F, -8F);

		attachmentModel[89].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 89
		attachmentModel[89].setRotationPoint(29.5F, 12F, -8F);

		attachmentModel[90].addShapeBox(0F, 0F, 0F, 1, 5, 5, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -4F, 0F, 1F, -4F, 0F, 0F, -5F, 0F, 0F, -5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 90
		attachmentModel[90].setRotationPoint(25.5F, 12F, -8F);

		attachmentModel[91].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 4F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 4F, 0F, 0F, 0.4F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0.4F, 0F, 0F); // Box 91
		attachmentModel[91].setRotationPoint(15F, 1F, 3F);

		attachmentModel[92].addShapeBox(0F, 0F, 0F, 1, 1, 1, 0F, 0F, 0F, 0F, 2F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 92
		attachmentModel[92].setRotationPoint(25F, 1.5F, 3F);

		attachmentModel[93].addShapeBox(0F, 0F, 0F, 52, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 1.5F, 0.5F, 0F, 1.5F, 0.5F); // Box 93
		attachmentModel[93].setRotationPoint(26F, 0.5F, -4F);

		attachmentModel[94].addShapeBox(0F, 0F, 0F, 67, 3, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 94
		attachmentModel[94].setRotationPoint(11F, -2F, 3F);

		attachmentModel[95].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 95
		attachmentModel[95].setRotationPoint(25.5F, 3F, 5F);

		attachmentModel[96].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 96
		attachmentModel[96].setRotationPoint(29.5F, 3F, 5F);

		attachmentModel[97].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 97
		attachmentModel[97].setRotationPoint(33.5F, 3F, 5F);

		attachmentModel[98].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 98
		attachmentModel[98].setRotationPoint(45.5F, 3F, 5F);

		attachmentModel[99].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 99
		attachmentModel[99].setRotationPoint(41.5F, 3F, 5F);

		attachmentModel[100].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 100
		attachmentModel[100].setRotationPoint(37.5F, 3F, 5F);

		attachmentModel[101].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 101
		attachmentModel[101].setRotationPoint(57.5F, 3F, 5F);

		attachmentModel[102].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 102
		attachmentModel[102].setRotationPoint(53.5F, 3F, 5F);

		attachmentModel[103].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 103
		attachmentModel[103].setRotationPoint(49.5F, 3F, 5F);

		attachmentModel[104].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 104
		attachmentModel[104].setRotationPoint(69.5F, 3F, 5F);

		attachmentModel[105].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 105
		attachmentModel[105].setRotationPoint(65.5F, 3F, 5F);

		attachmentModel[106].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 1F, -2F, 0F, 1F, -2F, 0F, 1F, 0F, -1F, 1F, 0F, -1F); // Box 106
		attachmentModel[106].setRotationPoint(61.5F, 3F, 5F);

		attachmentModel[107].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 107
		attachmentModel[107].setRotationPoint(25.5F, 3F, -8F);

		attachmentModel[108].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 108
		attachmentModel[108].setRotationPoint(29.5F, 3F, -8F);

		attachmentModel[109].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 109
		attachmentModel[109].setRotationPoint(33.5F, 3F, -8F);

		attachmentModel[110].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 110
		attachmentModel[110].setRotationPoint(37.5F, 3F, -8F);

		attachmentModel[111].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 111
		attachmentModel[111].setRotationPoint(41.5F, 3F, -8F);

		attachmentModel[112].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 112
		attachmentModel[112].setRotationPoint(45.5F, 3F, -8F);

		attachmentModel[113].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 113
		attachmentModel[113].setRotationPoint(49.5F, 3F, -8F);

		attachmentModel[114].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 114
		attachmentModel[114].setRotationPoint(53.5F, 3F, -8F);

		attachmentModel[115].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 115
		attachmentModel[115].setRotationPoint(57.5F, 3F, -8F);

		attachmentModel[116].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 116
		attachmentModel[116].setRotationPoint(61.5F, 3F, -8F);

		attachmentModel[117].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 117
		attachmentModel[117].setRotationPoint(65.5F, 3F, -8F);

		attachmentModel[118].addShapeBox(0F, 0F, 0F, 1, 3, 3, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, -1F, 1F, 0F, -1F, 1F, -2F, 0F, 1F, -2F, 0F); // Box 118
		attachmentModel[118].setRotationPoint(69.5F, 3F, -8F);

		attachmentModel[119].addShapeBox(0F, 0F, 0F, 52, 1, 1, 0F, 0F, 0F, 0.5F, 1.5F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0.5F, 1.5F, 1.5F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 119
		attachmentModel[119].setRotationPoint(26F, 0.5F, 3F);

		attachmentModel[120].addShapeBox(0F, 0F, 0F, 10, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 120
		attachmentModel[120].setRotationPoint(78F, 0F, 2.5F);

		attachmentModel[121].addShapeBox(0F, 0F, 0F, 77, 1, 6, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F); // Box 121
		attachmentModel[121].setRotationPoint(11F, 0F, -3F);

		attachmentModel[122].addShapeBox(0F, 0F, 0F, 67, 3, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F); // Box 122
		attachmentModel[122].setRotationPoint(11F, -2F, -4F);

		attachmentModel[123].addShapeBox(0F, 0F, 0F, 11, 2, 1, 0F, 4F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 4F, 0F, 0F, 0.4F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0.4F, 0F, 0F); // Box 123
		attachmentModel[123].setRotationPoint(15F, 1F, -4F);

		attachmentModel[124].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, 0F, 0F, 2F, 1F, 0F, 2F, 1F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 124
		attachmentModel[124].setRotationPoint(16F, 15.5F, -1F);

		attachmentModel[125].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 125
		attachmentModel[125].setRotationPoint(16F, 17F, -1F);

		attachmentModel[126].addShapeBox(0F, 0F, 0F, 9, 1, 2, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F); // Box 126
		attachmentModel[126].setRotationPoint(5F, 20.5F, -1F);

		attachmentModel[127].addShapeBox(0F, 0F, 0F, 3, 3, 2, 0F, 0F, -2F, 0F, -1F, 0F, 0F, -1F, 0F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, -3F, 0F, 0F, -3F, 0F, 0F, 0F, 0F); // Box 127
		attachmentModel[127].setRotationPoint(14F, 18F, -1F);

		attachmentModel[128].addShapeBox(0F, 0F, 0F, 2, 2, 2, 0F, -1F, 0F, 0F, 0F, -1F, 0F, 0F, -1F, 0F, -1F, 0F, 0F, 0F, -2F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F); // Box 128
		attachmentModel[128].setRotationPoint(2.5F, 19.5F, -1F);

		attachmentModel[129].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F); // Box 129
		attachmentModel[129].setRotationPoint(4.5F, 20.5F, -1F);

		attachmentModel[130].addShapeBox(0F, 0F, 0F, 1, 5, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -2F, 0F, 0F, 2F, 0F, 0F, 2F, 0F, 0F, -2F, 0F, 0F); // Box 130
		attachmentModel[130].setRotationPoint(0.5F, 14.5F, -1F);

		attachmentModel[131].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 131
		attachmentModel[131].setRotationPoint(0.5F, 13.5F, -1F);

		attachmentModel[132].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, -1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, -1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 132
		attachmentModel[132].setRotationPoint(0.5F, 11.5F, -1F);

		attachmentModel[133].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F); // Box 133
		attachmentModel[133].setRotationPoint(5F, 12.5F, -1F);

		attachmentModel[134].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, -0.5F, 0F, 0F); // Box 134
		attachmentModel[134].setRotationPoint(4.5F, 14.5F, -1F);

		attachmentModel[135].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -1.5F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, -1.5F, 0F, 0F); // Box 135
		attachmentModel[135].setRotationPoint(5F, 16.5F, -1F);

		attachmentModel[136].addShapeBox(0F, 0F, 0F, 62, 2, 1, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 136
		attachmentModel[136].setRotationPoint(26F, 1F, -3F);

		attachmentModel[137].addShapeBox(0F, 0F, 0F, 62, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F); // Box 137
		attachmentModel[137].setRotationPoint(26F, 1F, 2F);

		attachmentModel[138].addShapeBox(0F, 0F, 0F, 18, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F, 0F, -0.5F, 0F); // Box 138
		attachmentModel[138].setRotationPoint(43F, 2.5F, 2F);

		attachmentModel[139].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 139
		attachmentModel[139].setRotationPoint(61F, 1.5F, 2F);

		attachmentModel[140].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 1.5F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F, 0F, 0.5F, 0F); // Box 140
		attachmentModel[140].setRotationPoint(42F, 1.5F, 2F);

		attachmentModel[141].addShapeBox(0F, 0F, 0F, 6, 2, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 141
		attachmentModel[141].setRotationPoint(52.5F, 0.5F, 5F);

		attachmentModel[142].addShapeBox(0F, 0F, 0F, 4, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 142
		attachmentModel[142].setRotationPoint(53.5F, -0.5F, 5F);

		attachmentModel[143].addShapeBox(0F, 0F, 0F, 6, 1, 1, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 143
		attachmentModel[143].setRotationPoint(52.5F, 1.5F, 4F);

		attachmentModel[144].addShapeBox(0F, 0F, 0F, 8, 1, 1, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 144
		attachmentModel[144].setRotationPoint(52.5F, 1.5F, 3F);

		attachmentModel[145].addShapeBox(0F, 0F, 0F, 3, 1, 2, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 145
		attachmentModel[145].setRotationPoint(12.5F, 13F, -1F);

		attachmentModel[146].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 146
		attachmentModel[146].setRotationPoint(14.5F, 14F, -1F);

		attachmentModel[147].addShapeBox(0F, 0F, 0F, 1, 2, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 147
		attachmentModel[147].setRotationPoint(14.5F, 15F, -1F);

		attachmentModel[148].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0.5F, 0F, 0F, -1F, 0.5F, 0F, -1F, 0.5F, 0F, 0.5F, 0F, 0F); // Box 148
		attachmentModel[148].setRotationPoint(14.5F, 17F, -1F);

		attachmentModel[149].addShapeBox(0F, 0F, 0F, 1, 1, 2, 0F, 0F, 0F, 0F, -0.5F, -0.5F, 0F, -0.5F, -0.5F, 0F, 0F, 0F, 0F, 1.5F, 0F, 0F, -2F, 0.5F, 0F, -2F, 0.5F, 0F, 1.5F, 0F, 0F); // Box 149
		attachmentModel[149].setRotationPoint(14F, 18F, -1F);

		attachmentModel[150].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 150
		attachmentModel[150].setRotationPoint(34F, -1F, -6.5F);

		attachmentModel[151].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 151
		attachmentModel[151].setRotationPoint(35F, -2F, -7F);

		attachmentModel[152].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 152
		attachmentModel[152].setRotationPoint(35F, 0F, -7F);

		attachmentModel[153].addShapeBox(0F, 0F, 0F, 3, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 153
		attachmentModel[153].setRotationPoint(57F, -1F, -6.5F);

		attachmentModel[154].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F); // Box 154
		attachmentModel[154].setRotationPoint(58F, -2F, -7F);

		attachmentModel[155].addShapeBox(0F, 0F, 0F, 1, 1, 3, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 1F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F, 0F); // Box 155
		attachmentModel[155].setRotationPoint(58F, 0F, -7F);

		flipAll();
	}
}